<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rome.stock.core.infrastructure.mapper.RealWarehouseMapper">
    <resultMap id="baseResultMap" type="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        <result column="id" property="id"/>
        <result column="real_warehouse_code" property="realWarehouseCode"/>
        <result column="real_warehouse_out_code" property="realWarehouseOutCode"/>
        <result column="factory_code" property="factoryCode"></result>
        <result column="real_warehouse_name" property="realWarehouseName"/>
        <result column="real_warehouse_type" property="realWarehouseType"/>
        <result column="shop_code" property="shopCode"/>
        <result column="real_warehouse_status" property="realWarehouseStatus"/>
        <result column="real_warehouse_priority" property="realWarehousePriority"/>
        <result column="real_warehouse_postcode" property="realWarehousePostcode"/>
        <result column="real_warehouse_mobile" property="realWarehouseMobile"/>
        <result column="real_warehouse_phone" property="realWarehousePhone"/>
        <result column="real_warehouse_email" property="realWarehouseEmail"/>
        <result column="real_warehouse_country" property="realWarehouseCountry"/>
        <result column="real_warehouse_province" property="realWarehouseProvince"/>
        <result column="real_warehouse_city" property="realWarehouseCity"/>
        <result column="real_warehouse_county" property="realWarehouseCounty"/>
        <result column="real_warehouse_area" property="realWarehouseArea"/>
        <result column="real_warehouse_country_code" property="realWarehouseCountryCode"/>
        <result column="real_warehouse_province_code" property="realWarehouseProvinceCode"/>
        <result column="real_warehouse_city_code" property="realWarehouseCityCode"/>
        <result column="real_warehouse_county_code" property="realWarehouseCountyCode"/>
        <result column="real_warehouse_area_code" property="realWarehouseAreaCode"/>
        <result column="real_warehouse_address" property="realWarehouseAddress"/>
        <result column="real_warehouse_contact_name" property="realWarehouseContactName"/>
        <result column="real_warehouse_remark" property="realWarehouseRemark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="is_available" property="isAvailable"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="version_no" property="versionNo"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="app_id" property="appId"/>
        <result column="real_warehouse_rank" property="realWarehouseRank"/>
        <result column="region_name" property="regionName"/>
        <result column="region_id" property="regionId"/>
        <result column="factory_name" property="factoryName"/>
        <result column="rw_business_type" property="rwBusinessType"/>
        <result column="company_code" property="companyCode"/>
        <result column="company_name" property="companyName"/>
        <result column="cost_center_code" property="costCenterCode"/>
        <result column="cost_center_name" property="costCenterName"/>
        <result column="allow_negtive_stock" property="allowNegtiveStock"/>
        <result column="return_warehouse_id" property="returnWarehouseId"/>
        <result column="solid_empty_warehouse_status" property="solidEmptyWarehouseStatus"/>
        <result column="material_storage_type" property="materialStorageType"/>
        <result column="warehouse_store_identi" property="warehouseStoreIdenti"/>
    </resultMap>
    <select id="queryById" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            `id` = #{id}
            <include refid="where_base_column"/>
        </where>

    </select>

    <select id="queryByIds" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            `id` in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            and is_available = 1 and is_deleted = 0
        </where>

    </select>

    <select id="queryByCode" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            `real_warehouse_code` = #{code}
            <include refid="where_base_column"/>
        </where>
        AND is_deleted = 0
        AND is_available = 1
        LIMIT 1
    </select>

    <select id="queryRealWarehouseByInCode" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        WHERE is_deleted = 0 AND is_available = 1
        AND `real_warehouse_code` = #{code}
    </select>

    <select id="queryByOutCodeAndFactoryCode" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            `real_warehouse_out_code` = #{outCode} and `factory_code` = #{factoryCode}
            <include refid="where_base_column"/>
        </where>
    </select>

    <select id="queryBatchByOutCodeAndFactoryCode" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            (`real_warehouse_out_code`,`factory_code` ) in
            <foreach collection="list" separator="," open="(" close=")" item="item">
            (#{item.warehouseOutCode},#{item.factoryCode})
            </foreach>
            <include refid="where_base_column"/>
        </where>
    </select>

    <select id="queryByShopCodeList" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        <where>
            shop_code in
            <foreach collection="list" item="shopCode" open="(" close=")" separator=",">
                #{shopCode}
            </foreach>
            AND is_deleted = 0
            AND is_available = 1
        </where>
    </select>

    <select id="queryAvailableRwListByShopCodeList" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        <where>
            shop_code in
            <foreach collection="list" item="shopCode" open="(" close=")" separator=",">
                #{shopCode}
            </foreach>
            AND is_deleted = 0
            AND is_available = 1
            AND real_warehouse_status=1
        </where>
    </select>

    <select id="queryByCondition" parameterType="com.rome.stock.core.api.dto.RealWarehouseParamDTO"
            resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            <if test="null != realWarehouseCodeList and realWarehouseCodeList.size()>0  ">
                 real_warehouse_code in
                 <foreach collection="realWarehouseCodeList" item="realWarehouseCode" separator="," open="(" close=")">
                                #{realWarehouseCode}
                   </foreach>
            </if>
            <if test="null != realWarehouseOutCode and  '' != realWarehouseOutCode">
                and real_warehouse_out_code = #{realWarehouseOutCode}
            </if>
            <if test="null != factoryCode and  '' != factoryCode">
                and factory_code = #{factoryCode}
            </if>
            <if test="null != realWarehouseName and  '' != realWarehouseName">
                and real_warehouse_name like '%${realWarehouseName}%'
            </if>
            <if test="realWarehouseType != null and  0 != realWarehouseType">
                and real_warehouse_type = #{realWarehouseType}
            </if>
            <if test="notInType != null and  0 != notInType">
                and real_warehouse_type != #{notInType}
            </if>
            <if test="realWarehouseRank != null and  0 != realWarehouseRank">
                and real_warehouse_rank = #{realWarehouseRank}
            </if>
            <if test="null != nameOrCode and  '' != nameOrCode">
                and (
                real_warehouse_name like CONCAT('%',#{nameOrCode},'%' )
                or
                real_warehouse_code like CONCAT('%',#{nameOrCode},'%' )
                )
            </if>
            <include refid="where_base_column"/>
        </where>
    </select>

    <select id="queryAll" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            <include refid="where_base_column"/>
        </where>
    </select>

    <!-- 查询全部,包含删除的,专用的 -->
    <select id="queryAllByCache" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
    </select>

    <insert id="save" parameterType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO" useGeneratedKeys="true" keyProperty="id">
        insert into `sc_real_warehouse`
        (`real_warehouse_code`, `real_warehouse_out_code`, `factory_code`, `real_warehouse_name`, `real_warehouse_type`, `shop_code`,
         `real_warehouse_status`, `real_warehouse_priority`, `real_warehouse_postcode`, `real_warehouse_mobile`, `real_warehouse_phone`,
         `real_warehouse_email`, `real_warehouse_country`, `real_warehouse_province`, `real_warehouse_city`, `real_warehouse_county`,
         `real_warehouse_area`,  `real_warehouse_province_code`, `real_warehouse_city_code`, `real_warehouse_county_code`,
         `real_warehouse_area_code`, `real_warehouse_address`, `real_warehouse_contact_name`, `real_warehouse_remark`, `creator`, `modifier`, `is_available`,
         `is_deleted`, `version_no`,`real_warehouse_rank`,`region_name`,`region_id` ,`factory_name`,`rw_business_type`,`company_code`,`company_name`,`cost_center_code`,`cost_center_name`,`allow_negtive_stock`,
         `material_storage_type`, `revenue_stat`,`cost_stat`
        <if test="solidEmptyWarehouseStatus != null">
          , `solid_empty_warehouse_status`
        </if>
        )
        values (
        #{realWarehouseCode},#{realWarehouseOutCode},#{factoryCode},#{realWarehouseName},#{realWarehouseType},#{shopCode},
        #{realWarehouseStatus},1,#{realWarehousePostcode},#{realWarehouseMobile},
        #{realWarehousePhone},#{realWarehouseEmail},#{realWarehouseCountry},#{realWarehouseProvince},
        #{realWarehouseCity},#{realWarehouseCounty},#{realWarehouseArea},
        #{realWarehouseProvinceCode},#{realWarehouseCityCode},#{realWarehouseCountyCode},#{realWarehouseAreaCode},
        #{realWarehouseAddress},#{realWarehouseContactName},#{realWarehouseRemark},#{creator},#{creator},
        1,0,0,#{realWarehouseRank},#{regionName},#{regionId}
        ,#{factoryName},#{rwBusinessType},#{companyCode},#{companyName},#{costCenterCode},#{costCenterName},#{allowNegtiveStock},
        #{materialStorageType},#{revenueStat},#{costStat}
        <if test="solidEmptyWarehouseStatus != null">
            ,#{solidEmptyWarehouseStatus}
        </if>
        )
    </insert>

    <update id="updateByWhere" parameterType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        update `sc_real_warehouse`
        <set>
            <if test="realWarehouseAreaCode != null and  '' != realWarehouseAreaCode">
                real_warehouse_area_code = #{realWarehouseAreaCode},
            </if>
            <if test="realWarehouseCode != null and  '' != realWarehouseCode">
                real_warehouse_code = #{realWarehouseCode},
            </if>
            <if test="realWarehouseOutCode != null and  '' != realWarehouseOutCode">
                real_warehouse_out_code = #{realWarehouseOutCode},
            </if>
            <if test="factoryCode != null and  '' != factoryCode">
                factory_code = #{factoryCode},
            </if>
            <if test="realWarehouseName != null and  '' != realWarehouseName">
                real_warehouse_name = #{realWarehouseName},
            </if>
            <if test="realWarehouseType != null and  0 != realWarehouseType">
                real_warehouse_type = #{realWarehouseType},
            </if>
            <if test="realWarehouseProvince != null and  '' != realWarehouseProvince">
                real_warehouse_province = #{realWarehouseProvince},
            </if>
            <if test="realWarehouseProvinceCode != null and  '' != realWarehouseProvinceCode">
                real_warehouse_province_code = #{realWarehouseProvinceCode},
            </if>
            <if test="realWarehouseAddress != null and  '' != realWarehouseAddress">
                real_warehouse_address = #{realWarehouseAddress},
            </if>
            <if test="realWarehouseContactName != null and  '' != realWarehouseContactName">
                real_warehouse_contact_name = #{realWarehouseContactName},
            </if>
            <if test="realWarehousePhone != null and  '' != realWarehousePhone">
                real_warehouse_phone = #{realWarehousePhone},
            </if>
            <if test="realWarehouseProvince != null and  '' != realWarehouseProvince">
                real_warehouse_province = #{realWarehouseProvince},
            </if>
            <if test="realWarehouseCity != null and  '' != realWarehouseCity">
                real_warehouse_city = #{realWarehouseCity},
            </if>
            <if test="realWarehouseCityCode != null and  '' != realWarehouseCityCode">
                real_warehouse_city_code = #{realWarehouseCityCode},
            </if>
            <if test="realWarehouseCounty != null and  '' != realWarehouseCounty">
                real_warehouse_county = #{realWarehouseCounty},
            </if>
            <if test="realWarehouseCountyCode != null and  '' != realWarehouseCountyCode">
                real_warehouse_county_code = #{realWarehouseCountyCode},
            </if>
            <if test="realWarehouseMobile != null and  '' != realWarehouseMobile">
                real_warehouse_mobile = #{realWarehouseMobile},
            </if>
            <if test="realWarehouseRank != null and 0 != realWarehouseRank">
                real_warehouse_rank = #{realWarehouseRank},
            </if>
            <if test="regionName != null and ''!=regionName">
                region_name = #{regionName},
            </if>
            <if test="regionId != null">
                region_id = #{regionId},
            </if>
            <if test="modifier != null">
                modifier = #{modifier},
            </if>
            <if test="factoryName != null and  '' != factoryName">
                factory_name = #{factoryName},
            </if>
            <if test="rwBusinessType != null">
                rw_business_type = #{rwBusinessType},
            </if>
            <if test="companyCode != null and  '' != companyCode">
                company_code = #{companyCode},
            </if>
            <if test="companyName != null and  '' != companyName">
                company_name = #{companyName},
            </if>
            <if test="costCenterCode != null and  '' != costCenterCode">
                cost_center_code = #{costCenterCode},
            </if>
            <if test="costCenterName != null and  '' != costCenterName">
                cost_center_name = #{costCenterName},
            </if>
            <if test="allowNegtiveStock != null">
                allow_negtive_stock = #{allowNegtiveStock},
            </if>
            <if test="realWarehouseStatus != null">
                real_warehouse_status = #{realWarehouseStatus},
            </if>
            <if test="solidEmptyWarehouseStatus != null">
                solid_empty_warehouse_status = #{solidEmptyWarehouseStatus},
            </if>
            <if test="materialStorageType != null">
                material_storage_type = #{materialStorageType},
            </if>
            revenue_stat = #{revenueStat},
            cost_stat = #{costStat},
            shop_code = #{shopCode}
        </set>
        <where>
            `id` = #{id}
        </where>
    </update>

    <update id="updateStatusEnable">
        update `sc_real_warehouse`
        set `real_warehouse_status` = 1,
        `modifier` = #{userId}
        <where>
            `id` = #{realWarehouseId}
        </where>
    </update>

    <update id="updateStatusDisable">
        update `sc_real_warehouse`
        set `real_warehouse_status` = 2,
        `modifier` = #{userId}
        <where>
            `id` = #{realWarehouseId}
        </where>
    </update>

    <update id="updateIsAvailable">
        update `sc_real_warehouse`
        set `is_available` = #{isAvailable},
        `modifier` = #{modifier}
        <where>
            `id` = #{id}
        </where>
    </update>

    <update id="updateIsDeleted">
        update `sc_real_warehouse`
        set `is_deleted` = #{isDeleted},
        `modifier` = #{modifier}
        <where>
            id = #{id}
        </where>
    </update>
    <update id="updateRealWarehouseByInitField">
        update `sc_real_warehouse`
        <set>
            <if test="factoryName != null and  '' != factoryName">
                factory_name = #{factoryName},
            </if>
            <if test="companyCode != null and  '' != companyCode">
                company_code = #{companyCode},
            </if>
            <if test="companyName != null and  '' != companyName">
                company_name = #{companyName},
            </if>
            <if test="allowNegtiveStock != null">
                allow_negtive_stock = #{allowNegtiveStock},
            </if>
            <if test="rwBusinessType != null">
                rw_business_type = #{rwBusinessType},
            </if>
            <if test="costCenterCode != null and  '' != costCenterCode">
                cost_center_code = #{costCenterCode},
            </if>
            <if test="costCenterName != null and  '' != costCenterName">
                cost_center_name = #{costCenterName},
            </if>
            shop_code = #{shopCode}
        </set>
        <where>
            `id` = #{id}
        </where>
    </update>
    <update id="updateCostCenterCodeAndCostCenterName">
        update sc_real_warehouse set cost_center_code=#{costCenterCode},cost_center_name=#{costCenterName}
        where factory_code=#{factoryCode}
    </update>
    <update id="updateReturnWarehouse">
        update sc_real_warehouse
        set return_warehouse_id=#{returnWarehouseId}
        where id=#{id} and is_available=1 and is_deleted=0
    </update>

    <select id="queryWarehouseIdByShopCode" parameterType="java.lang.String" resultType="java.lang.Long">
    select id from sc_real_warehouse where shop_code=#{shopCode} and real_warehouse_status=1 and is_available=1 and is_deleted=0
  </select>

    <select id="querysByCodes" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        <where>
            real_warehouse_code in
            <foreach collection="wareHouseCodes" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
            <include refid="where_base_column"/>
        </where>
    </select>

    <select id="queryWarehouseByIds" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        <if test="idList != null and idList.size()>0">
            where id in
            <foreach collection="idList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryWarehouseByIdPage" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO" parameterType="com.rome.stock.core.api.dto.QueryRealWarehoureKpDTO">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        <if test="idList != null and idList.size()>0">
            where id in
            <foreach collection="idList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!--根据仓库id查询仓库名称-->
    <select id="getWarehouseNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select real_warehouse_name from sc_real_warehouse
	where is_deleted = 0 and is_available = 1 and id = #{warehouseId}
  </select>

    <select id="getByRwCodeAndFactoryCode" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            `real_warehouse_code` = #{realWarehouseCode} and `factory_code` = #{factoryCode}
            <include refid="where_base_column"/>
        </where>
        and is_deleted = 0
        and is_available = 1
    </select>

    <!--根据工厂code查询仓库-->
    <select id="queryRealWarehouseByFactoryCode" parameterType="java.lang.String" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        where is_deleted = 0
        <if test="factoryCode != null and factoryCode != ''">
            and factory_code = #{factoryCode}
        </if>
    </select>

    <select id="querySingleRealWarehouseByFactoryCodeAndRwType"  resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        where is_deleted = 0
        and factory_code = #{factoryCode}
        and real_warehouse_type = #{rwType}
        limit 1
    </select>


    <!--根据仓库类型查询工厂-->
    <select id="getRealWarehouseFactory" resultMap="baseResultMap" parameterType="java.util.List">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse` where
        is_deleted = 0
        and is_available = 1
        and `real_warehouse_type` in
        <foreach collection="types" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
    </select>

    <!--根据工厂code和仓库类型查询仓库信息-->
    <select id="queryRealWarehouseByFactoryCodeAndRWType" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse` where
        is_deleted = 0
        and is_available = 1
        <if test="factoryCode!= null and factoryCode!= ''">
            and factory_code = #{factoryCode}
        </if>
        <!--委外的供应商编码就是 realWarehouseOutCode-->
        <if test="supplierCode!= null and supplierCode!= ''">
            and real_warehouse_out_code = #{supplierCode}
        </if>
        <if test="types!= null and types.size != 0">
            and `real_warehouse_type` in
            <foreach collection="types" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
        </if>

    </select>

    <select id="queryRealWarehouseIdsByCode" parameterType="java.lang.String" resultType="java.lang.Long">
    select id from sc_real_warehouse where real_warehouse_code =#{realWarehouseCode}
  </select>

    <select id="queryRealWarehouseByRealWarehouseCode" parameterType="java.lang.String" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select <include refid="select_base_column"/> from sc_real_warehouse
        where real_warehouse_code=#{realWarehouseCode} limit 1
    </select>

    <!--根据实仓内部编码查实仓信息-->
    <select id="queryRealWarehouseByRealWarehouseCodeList"  resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select <include refid="select_base_column"/> from sc_real_warehouse
        where real_warehouse_code in
        <foreach collection="realWarehouseCodeList" item="realWarehouseCode" separator="," open="(" close=")">
            #{realWarehouseCode}
        </foreach>
    </select>

    <!--根据仓库类型查仓库-->
    <select id="queryRealWarehouseByRWType" resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse` where
        is_deleted = 0
        and is_available = 1
        and real_warehouse_type = #{type}
    </select>

    <sql id="insert_base_column">
    `real_warehouse_code`, `real_warehouse_out_code`, `factory_code`, `real_warehouse_name`, `real_warehouse_type`, `shop_code`,
    `real_warehouse_status`, `real_warehouse_priority`, `real_warehouse_postcode`, `real_warehouse_mobile`, `real_warehouse_phone`,
    `real_warehouse_email`, `real_warehouse_country`, `real_warehouse_province`, `real_warehouse_city`, `real_warehouse_county`,
    `real_warehouse_area`,  `real_warehouse_province_code`, `real_warehouse_city_code`, `real_warehouse_county_code`,
    `real_warehouse_area_code`, `real_warehouse_address`, `real_warehouse_contact_name`, `real_warehouse_remark`, `creator`, `modifier`, `is_available`,
    `is_deleted`, `version_no`,`real_warehouse_rank`,`region_name`,`region_id`
    ,`factory_name`,`rw_business_type`,`company_code`,`company_name`,`cost_center_code`,`cost_center_name`,`allow_negtive_stock`, `solid_empty_warehouse_status`
    ,`material_storage_type`
  </sql>
    <sql id="select_base_column">
    `id`, `real_warehouse_code`, `real_warehouse_out_code`, `factory_code`, `real_warehouse_name`, `real_warehouse_type`, `shop_code`,
    `real_warehouse_status`, `real_warehouse_priority`, `real_warehouse_postcode`, `real_warehouse_mobile`, `real_warehouse_phone`,
    `real_warehouse_email`, `real_warehouse_country`, `real_warehouse_province`, `real_warehouse_city`, `real_warehouse_county`,
    `real_warehouse_area`, `real_warehouse_country_code`, `real_warehouse_province_code`, `real_warehouse_city_code`, `real_warehouse_county_code`,
    `real_warehouse_area_code`, `real_warehouse_address`, `real_warehouse_contact_name`, `real_warehouse_remark`, `create_time`, `update_time`,
     `creator`, `modifier`, `is_available`, `is_deleted`, `version_no`, `tenant_id`, `app_id`,`real_warehouse_rank`,`region_name`,`region_id`
     ,`factory_name`,`rw_business_type`,`company_code`,`company_name`,`cost_center_code`,`cost_center_name`,`allow_negtive_stock`,`return_warehouse_id`,`solid_empty_warehouse_status`
     ,`material_storage_type` ,`warehouse_store_identi`
  </sql>
    <sql id="where_base_column">
    AND `is_deleted` = 0
  </sql>

    <select id="queryWmsRealWarehouseByFactoryCode"
            resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        where factory_code = #{factoryCode} and real_warehouse_type != 2 and is_deleted = 0
    </select>
    <!-- 查询所有非门店仓 -->
    <select id="queryNotShopWarehouse" parameterType="java.lang.Integer"
            resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select
        <include refid="select_base_column"/>
        from sc_real_warehouse
        where is_deleted = 0 and is_available = 1
        <if test="type != null">
            and real_warehouse_type != #{type}
        </if>
        <if test="factoryCode != null and factoryCode != ''">
            and factory_code = #{factoryCode}
        </if>
    </select>
    <!-- 获取所有非门店仓下的工厂 -->
    <select id="queryNotShopFactory" parameterType="java.lang.Integer" resultType="java.lang.String">
        select DISTINCT
        factory_code
        from sc_real_warehouse
        where is_deleted = 0 and is_available = 1
        <if test="type != null">
            and real_warehouse_type != #{type}
        </if>
    </select>

    <!-- 获取所有门店仓下的工厂 -->
    <select id="queryShopFactory" parameterType="java.lang.Integer" resultType="java.lang.String">
        select DISTINCT
        shop_code
        from sc_real_warehouse
        where is_deleted = 0 and is_available = 1

        and real_warehouse_type = #{type}

    </select>
    <select id="getRealWarehouseByRmCode" resultMap="baseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse WHERE is_deleted = 0 AND is_available = 1
        <if test="warehouseCode != null and warehouseCode != ''">
            AND real_warehouse_code = #{warehouseCode}
        </if>
    </select>
    <!-- 根据批量工厂编码查询实仓信息 -->
    <select id="queryRealWarehousesByFactoryCodes" resultMap="baseResultMap">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse WHERE is_deleted = 0 AND is_available = 1
        <if test="factoryCodes != null and factoryCodes.size > 0">
            AND factory_code IN
            <foreach collection="factoryCodes" close=")" open="(" item="item" index="index" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 根据商家编号查询对实仓信息 -->
    <select id="queryVirtualSkuRealWarehouse" resultMap="baseResultMap">
        SELECT
          <include refid="select_base_column"/>
        FROM sc_real_warehouse
        WHERE real_warehouse_type='15' AND is_deleted = 0 AND is_available = 1
    </select>

    <!-- 根据仓库编号实仓ID -->
    <select id="queryRWIdByRealWarehouseCode" resultType="java.lang.Long">
    	SELECT id
        FROM sc_real_warehouse WHERE is_deleted = 0 AND is_available = 1
        AND real_warehouse_code = #{realWarehouseCode} limit 1
    </select>

    <!-- 根据门店编码查询实仓ID -->
    <select id="queryRWIdByShopCode" resultType="java.lang.Long">
    	SELECT id
        FROM sc_real_warehouse WHERE is_deleted = 0 AND is_available = 1
    	AND shop_code = #{shopCode} limit 1
    </select>
    <!-- 根据实仓ID查询实仓 -->
    <select id="queryRealWarehouseById" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        SELECT <include refid="select_base_column"/>
        FROM sc_real_warehouse WHERE is_deleted = 0 AND is_available = 1
        AND id = #{id}
    </select>

    <!-- 查询实仓信息列表（排除门店） -->
    <select id="queryRWList" resultMap="baseResultMap">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse
        <where>
            is_deleted = 0 AND is_available = 1 AND real_warehouse_type != 1
            <if test="nameOrCode!=null and nameOrCode!='' ">
                and  real_warehouse_code like CONCAT('%',#{nameOrCode},'%') or real_warehouse_name like CONCAT('%',#{nameOrCode},'%')
            </if>
        </where>
    </select>

    <!-- 根据鲲鹏仓配置，查询实仓id -->
    <select id="queryRealWarehouseIdsByKpRands" resultType="java.lang.Long">
        select   real_warehouse_id from sc_real_warehouse_wms_config c
        left join  sc_real_warehouse r on r.id=c.real_warehouse_id
        where c.is_deleted =0 and c.is_available =1
       <if test="ranKs != null and ranKs.size > 0">
           AND real_warehouse_rank in
           <foreach collection="ranKs" close=")" open="(" item="item" index="index" separator=",">
              #{item}
           </foreach>
       </if>
       GROUP BY real_warehouse_id
    </select>

<!--根据行政区域查询仓库信息-->
    <select id="queryRWByCondition" resultMap="baseResultMap" parameterType="com.rome.stock.core.api.dto.QueryAreaRWarehouse">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse
        <where>
            is_deleted = 0 AND is_available = 1
            <if test="regionName!=null  and regionName!=''">
                 and region_name =#{regionName}
            </if>
            <if test="realWarehouseRank!=null ">
                and real_warehouse_rank=#{realWarehouseRank}
            </if>
            <if test="realWarehouseType!=null ">
                and real_warehouse_type=#{realWarehouseType}
            </if>
        </where>
    </select>

    <select id="countRealWarehouseByFactoryCode" resultType="java.lang.Integer">
        select  count(1) from sc_real_warehouse where  factory_code=#{factoryCode}
    </select>
    <select id="queryFactoryCodeByLike" resultType="java.lang.String">
        select DISTINCT factory_code from sc_real_warehouse where  factory_code like concat('%',#{factoryCode},'%')
        AND is_deleted = 0 AND is_available = 1
    </select>

    <!-- 根据仓库类型查询分组工厂-->
    <select id="queryFactoryByRwType" resultMap="baseResultMap" parameterType="integer">
        select  factory_code from sc_real_warehouse
        where real_warehouse_type = #{realWarehouseType}   group by factory_code
    </select>

    <!-- 根据仓库类型查询分组工厂-->
    <select id="queryFactoryByRwTypeList" resultMap="baseResultMap" parameterType="list">
        select  factory_code from sc_real_warehouse
        where real_warehouse_type in
        <foreach collection="list" separator="," item="realWarehouseType" close=")" open="(">
          #{realWarehouseType}
        </foreach>
        group by factory_code
    </select>



    <select id="queryRealWarehouseCodeCodeByLike" resultType="com.rome.stock.core.api.dto.RealWarehouse">
        select DISTINCT real_warehouse_code,real_warehouse_name
        from sc_real_warehouse where
        real_warehouse_code like concat('%',#{realWarehouseCode},'%')
        <if test="factoryCode!=null and ''!=factoryCode">
            and factory_code=#{factoryCode}
        </if>
        AND is_deleted = 0 AND is_available = 1
    </select>
    <select id="queryRealWarehouseStockByRealWarehouseCodeAndSkuCode" resultType="com.rome.stock.core.api.dto.QueryStockDTO">
        select  real_qty,lock_qty,onroad_qty,quality_qty,unqualified_qty,sku_code
         from sc_real_warehouse_stock
        where sku_code=#{skuCode} and real_warehouse_id=#{realWarehouseId}
    </select>
    <select id="queryCompanyCodesByFactoryCodes" resultType="com.rome.stock.core.api.dto.RealWarehouse">
        select factory_code,factory_name,company_code,company_name,cost_center_code,cost_center_name
        from sc_real_warehouse
        where factory_code in
        <foreach collection="factoryCodes" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        AND is_deleted = 0 AND is_available = 1
        group by factory_code,company_code,cost_center_code
    </select>
    <select id="queryByCompanyCodeAndRealWarehouseType" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select  *  from sc_real_warehouse
        where company_code=#{companyCode} and real_warehouse_type =#{realWarehouseType}
        and is_deleted = 0 and is_available = 1
    </select>

    <select id="queryWarehouseByLocationCode" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select *
        from sc_real_warehouse
        where real_warehouse_out_code in
        <foreach collection="locationCodes" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        AND is_deleted = 0 AND is_available = 1
    </select>

    <update id="updateRealWarehouseByWhereMerchant" parameterType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        update `sc_real_warehouse`
        <set>
            <if test="realWarehousePostcode != null">
                real_warehouse_postcode = #{realWarehousePostcode},
            </if>
            <if test="realWarehouseProvinceCode != null">
                real_warehouse_province_code = #{realWarehouseProvinceCode},
            </if>
            <if test="realWarehouseProvince != null">
                real_warehouse_province = #{realWarehouseProvince},
            </if>
            <if test="realWarehouseCity != null">
                real_warehouse_city = #{realWarehouseCity},
            </if>
            <if test="realWarehouseCityCode != null">
                real_warehouse_city_code = #{realWarehouseCityCode},
            </if>
            <if test="realWarehouseCounty != null">
                real_warehouse_county = #{realWarehouseCounty},
            </if>
            <if test="realWarehouseCountyCode != null">
                real_warehouse_county_code = #{realWarehouseCountyCode},
            </if>
            <if test="realWarehouseAddress != null">
                real_warehouse_address = #{realWarehouseAddress},
            </if>
            <if test="realWarehouseContactName != null">
                real_warehouse_contact_name = #{realWarehouseContactName},
            </if>
            <if test="realWarehousePhone != null">
                real_warehouse_phone = #{realWarehousePhone},
            </if>
            <if test="realWarehouseMobile != null">
                real_warehouse_mobile = #{realWarehouseMobile},
            </if>
            shop_code = #{shopCode}
        </set>
        <where>
            `id` = #{id}
        </where>
    </update>
    <!-- 根据外部编码与工厂编码批量查询 -->
    <select id="queryByOutCodeAndFactoryCodeList" parameterType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO"
            resultMap="baseResultMap">
        select
        <include refid="select_base_column"/>
        from `sc_real_warehouse`
        <where>
            (
        <foreach collection="list" separator=" or " item="item">
            (
            factory_code = #{item.factoryCode}
            and real_warehouse_out_code = #{item.realWarehouseOutCode}
            )
        </foreach>
           )
            <include refid="where_base_column"/>
        </where>
    </select>

    <select id="queryRealWarehouseLimitWarehouse" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        select
        factory_code,
        factory_name,
        real_warehouse_province,
        real_warehouse_city,
        real_warehouse_county,
        real_warehouse_area,
        real_warehouse_mobile,
        real_warehouse_phone,
        company_code,
        company_name,
        real_warehouse_address,
        real_warehouse_province_code,
        real_warehouse_city_code,
        real_warehouse_county_code
        from sc_real_warehouse
        where factory_code in
        <foreach collection="factoryCodes" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        and real_warehouse_type != 1 AND is_deleted = 0 AND is_available = 1
        group by factory_code
    </select>
    <select id="queryRealWarehouseByOutCodeAndType" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse
        where is_deleted = 0 AND is_available = 1
        and real_warehouse_type = #{realWarehouseType}
        and real_warehouse_out_code = #{realWarehouseOutCode}
    </select>
    <select id="queryWarehouseByRealWarehouseList" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse
        where is_deleted = 0 AND is_available = 1
        and (factory_code,real_warehouse_out_code) in
        <foreach collection="realWarehouseList" item="item" open="(" close=")" separator=",">
            (#{item.factoryCode},#{item.warehouseCode})
        </foreach>
    </select>
    <select id="queryByShopCode" resultType="com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO">
        SELECT
        <include refid="select_base_column"/>
        FROM sc_real_warehouse
        where is_deleted = 0 AND is_available = 1
        and shop_code = #{shopCode}
    </select>
    <select id="querySpecialWarehouseList" resultType="java.lang.String">
        SELECT distinct factory_code
        FROM sc_real_warehouse
        where is_deleted = 0 AND is_available = 1
        and warehouse_store_identi=1
    </select>
</mapper>
