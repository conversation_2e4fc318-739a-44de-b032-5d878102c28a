<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.rome.stock.core.infrastructure.mapper.frontrecord.FrOutsourcingMapper">

    <sql id="Base_Column_List">
        record_code,merchant_id,record_type,record_status,remark,real_warehouse_id,out_record_code,appoint_record_code,out_create_time,factory_code,factory_name,supplier_code,supplier_name,supplier_contact
    </sql>

    <insert id="insertOutsourcingOutRecord" useGeneratedKeys="true" keyProperty="id" parameterType="com.rome.stock.core.infrastructure.dataobject.frontrecord.FrOutsourcingDO">
      insert into sc_fr_outsourcing(record_code,merchant_id,record_type,record_status,remark,real_warehouse_id,out_record_code,appoint_record_code,out_create_time,factory_code,factory_name,supplier_code,supplier_name,supplier_contact,purchase_order_no)
      values(#{recordCode},#{merchantId},#{recordType},#{recordStatus},#{remark},#{realWarehouseId},#{outRecordCode},#{appointRecordCode},#{outCreateTime},#{factoryCode},#{factoryName},#{supplierCode},#{supplierName},#{supplierContact},#{purchaseOrderNo})
    </insert>

    <!--更新前置单状态为已收货完成状态，初始状态转已出库-->
    <update id="updateOutAllocationStatus" parameterType="java.lang.Integer">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.FrontRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.FrontRecordStatusVO@OUT_ALLOCATION"/>
        update sc_fr_outsourcing
        set record_status=#{afterRecordStatus.status}
        where record_code = #{recordCode} and record_status=#{beforeRecordStatus.status}
        AND is_deleted=0
        AND is_available=1
    </update>

    <update id="updateToCancle">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.FrontRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.FrontRecordStatusVO@DISABLED"/>
        update sc_fr_outsourcing
        set record_status=#{afterRecordStatus.status}
        where record_code=#{recordCode} and record_status=#{beforeRecordStatus.status}
        AND is_deleted = 0
        AND is_available = 1
    </update>


    <!--查询： outRecordCode-->
    <select id="queryNumByOutRecordCode" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(out_record_code)
        from sc_fr_outsourcing
        where
		 out_record_code =#{outRecordCode} and is_deleted  = 0 AND is_available=1
    </select>

    <select id="queryById" resultType="com.rome.stock.core.infrastructure.dataobject.frontrecord.FrOutsourcingDO" parameterType="java.lang.Long">
        select id,record_code ,real_warehouse_id,out_record_code
        from sc_fr_outsourcing
        where
		 id =#{id} and is_deleted  = 0 AND is_available=1
    </select>
    <select id="queryFrontRecordByIds" resultType="com.rome.stock.core.infrastructure.dataobject.frontrecord.FrOutsourcingDO">
        select id,record_code,out_record_code,record_type from sc_fr_outsourcing
        where id in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>
