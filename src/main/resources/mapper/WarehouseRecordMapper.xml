<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rome.stock.core.infrastructure.mapper.WarehouseRecordMapper">
    <resultMap id="WarehouseRecordMap" type="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">

    </resultMap>

    <sql id="BASE_COLUMN">
       id,record_code,sap_order_code,business_type,record_status,record_type,user_code,virtual_warehouse_id,
       real_warehouse_id,merchant_id,channel_type,channel_code,factory_code,factory_name,order_remark_user,
       out_create_time,tms_record_code,pay_time,mobile,reason,reasons,expect_receive_date_start,expect_receive_date_end,
       relinquish_time,delivery_time,receiver_time,create_time,update_time,creator,out_or_in_time,box_batch_tag,
       modifier,is_available,is_deleted,version_no,tenant_id,app_id,sync_wms_status,sync_dispatch_status,
       batch_status,sync_transfer_status,cmp_status,sync_trade_status,sync_wms_fail_time,self_takeout,sync_fulfillment_status,sync_purchase_status,trans_way
    </sql>

    <sql id="WDT_BASE_COLUMN">
    id,record_code,channel_code,record_type,record_status,split_type,allot_status,real_warehouse_id,virtual_warehouse_id,
    logistics_code,mobile,origin_order_code,out_record_code,out_create_time,user_code,pay_time,merchant_id,create_time,
    update_time,creator,version_no,modifier,is_available,is_deleted,tenant_id,app_id,trans_type,trans_way
    </sql>

     <insert id="insertWarehouseRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,record_type,
        real_warehouse_id,channel_type,channel_code,merchant_id,user_code,mobile,
        out_create_time,sync_wms_status,sap_order_code,pay_time,
        sync_transfer_status,batch_status,self_takeout,sync_fulfillment_status,app_id,virtual_warehouse_id
        <if test="syncTradeStatus!=null and '' !=syncTradeStatus">
        ,sync_trade_status
        </if>
         <if test="boxBatchTag !=  null ">
             ,box_batch_tag
         </if>
        <if test="syncDispatchStatus!=null and '' !=syncDispatchStatus">
            ,sync_dispatch_status
        </if>
        <if test="outOrInTime !=null">
            ,out_or_in_time
        </if>
        <if test="deliveryTime!=null ">
            ,delivery_time
        </if>
        <if test="createTime!=null ">
            ,create_time
        </if>
        <if test="reasons!=null ">
            ,reasons
        </if>
        <if test="transWay!=null ">
            ,trans_way
        </if>
        )
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},#{realWarehouseId},
        #{channelType},#{channelCode},#{merchantId},#{userCode},#{mobile},#{outCreateTime},
        #{syncWmsStatus},#{sapOrderCode},#{payTime},
        #{syncTransferStatus},#{batchStatus},#{selfTakeout},#{syncFulfillmentStatus},#{appId},#{virtualWarehouseId}
        <if test="syncTradeStatus!=null and '' !=syncTradeStatus">
        ,#{syncTradeStatus}
        </if>
         <if test=" boxBatchTag!=null ">
           ,#{boxBatchTag}
         </if>

        <if test="syncDispatchStatus!=null and '' !=syncDispatchStatus">
            ,#{syncDispatchStatus}
        </if>
        <if test="outOrInTime!=null ">
            ,#{outOrInTime}
        </if>
        <if test="deliveryTime!=null ">
            ,#{deliveryTime}
        </if>
        <if test="createTime!=null ">
            ,#{createTime}
        </if>
        <if test="reasons!=null ">
            ,#{reasons}
        </if>
        <if test="transWay!=null ">
            ,#{transWay}
        </if>
        )
    </insert>

    <insert id="insertNoChannelWarehouseRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,
        record_type,real_warehouse_id,merchant_id,out_create_time,sync_wms_status, sync_transfer_status)
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},#{syncWmsStatus}, #{syncTransferStatus})
    </insert>

    <insert id="insertNoChannelInWarehouseRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,
        record_type,real_warehouse_id,merchant_id,out_create_time,
        sync_wms_status,receiver_time, sync_transfer_status,batch_status)
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},
        #{syncWmsStatus},#{receiverTime}, #{syncTransferStatus},#{batchStatus})
    </insert>

    <insert id="insertOutWarehouseRecordIncludeBatchCmp" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,sap_order_code,
        record_type,real_warehouse_id,merchant_id,out_create_time,sync_wms_status,out_or_in_time,delivery_time,
        receiver_time,batch_status, sync_transfer_status,cmp_status)
        values(#{recordCode},#{businessType},#{recordStatus},#{sapOrderCode},#{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},#{syncWmsStatus},#{outOrInTime},#{deliveryTime},
        #{receiverTime},#{batchStatus}, #{syncTransferStatus},#{cmpStatus})
    </insert>

    <insert id="insertInWarehouseRecordIncludeBatchStatus" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,sap_order_code,
        record_type,real_warehouse_id,merchant_id,out_create_time,sync_wms_status,
        receiver_time,batch_status, sync_transfer_status,out_or_in_time, app_id)
        values(#{recordCode},#{businessType},#{recordStatus},#{sapOrderCode},#{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},#{syncWmsStatus},
        #{receiverTime},#{batchStatus}, #{syncTransferStatus},#{outOrInTime},#{appId})
    </insert>


    <insert id="insertNoChannelOutWarehouseRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,
        record_type,real_warehouse_id,merchant_id,out_create_time,
        sync_wms_status,delivery_time, sync_transfer_status,batch_status)
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},
        #{syncWmsStatus},#{deliveryTime}, #{syncTransferStatus},#{batchStatus})
    </insert>

    <insert id="insertOutWarehouseRecordIncludeBatchStatus" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,sap_order_code,
        record_type,real_warehouse_id,merchant_id,out_create_time,sync_wms_status,
        delivery_time,batch_status, sync_transfer_status,out_or_in_time,virtual_warehouse_id, app_id)
        values(#{recordCode},#{businessType},#{recordStatus},#{sapOrderCode}, #{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},#{syncWmsStatus},
        #{deliveryTime},#{batchStatus}, #{syncTransferStatus},#{outOrInTime},#{virtualWarehouseId},#{appId})
    </insert>

    <insert id="insertInWarehouseRecordIncludeBatchCmp" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,sap_order_code,
        record_type,real_warehouse_id,merchant_id,out_create_time,sync_wms_status,out_or_in_time,
        delivery_time,batch_status, sync_transfer_status,cmp_status)
        values(#{recordCode},#{businessType},#{recordStatus},#{sapOrderCode},#{recordType},
        #{realWarehouseId},#{merchantId},#{outCreateTime},#{syncWmsStatus},#{outOrInTime},
        #{deliveryTime},#{batchStatus}, #{syncTransferStatus},#{cmpStatus})
    </insert>


    <!--采购入库修改操作-->
    <update id="updateWarehouseRecord" parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        update sc_warehouse_record
        set real_warehouse_id = #{realWarehouseId},
        merchant_id=#{merchantId}
        where id=#{id}
    </update>
    <delete id="deleteWarehouseRecordById">
        delete from sc_warehouse_record
        where  id=#{id}
    </delete>
    <delete id="deleteWarehouseRecordByIdAndRecordStatus">
        delete from sc_warehouse_record
        where  id=#{id}
        <if test="recordStatus!=null ">
            and record_status = #{recordStatus}
        </if>
    </delete>
    <delete id="deleteWarehouseRecordDetailByIds">
        delete from sc_warehouse_record_detail
        where  id in
        <foreach item="item" index="index" collection="deleteList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="cancelAndUpdateRecordCode">
        update sc_warehouse_record set record_code=#{newRecordCode},is_deleted=1,record_status=2
        where record_code=#{recordCode}
    </update>

    <update id="cancelDetailAndUpdateRecordCode">
        update sc_warehouse_record_detail set record_code=#{newRecordCode},is_deleted=1
        where record_code=#{recordCode}
    </update>

    <insert id="insertUserWarehouseRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,record_type,
        real_warehouse_id,channel_type,channel_code,merchant_id,out_create_time,
        mobile,user_code,sync_wms_status, sync_transfer_status,batch_status
        <if test="sapOrderCode!=null ">
            ,sap_order_code
        </if>
        <if test="outOrInTime!=null ">
            ,out_or_in_time
        </if>
        <if test="deliveryTime!=null ">
            ,delivery_time
        </if>
        <if test="reasons!=null ">
            ,reasons
        </if>
        )
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},
        #{realWarehouseId},#{channelType},#{channelCode},#{merchantId},#{outCreateTime},
        #{mobile},#{userCode},#{syncWmsStatus}, #{syncTransferStatus},#{batchStatus}
        <if test="sapOrderCode!=null ">
            ,#{sapOrderCode}
        </if>
        <if test="outOrInTime!=null ">
            ,#{outOrInTime}
        </if>
        <if test="deliveryTime!=null ">
            ,#{deliveryTime}
        </if>
        <if test="reasons!=null ">
            ,#{reasons}
        </if>
        )
    </insert>

    <insert id="insertUserWarehouseRecordIncludeBatchStatus" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,
        record_type,real_warehouse_id,channel_type,channel_code,merchant_id,
        out_create_time,mobile,user_code,sync_wms_status,batch_status, sync_transfer_status,out_or_in_time)
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},
        #{realWarehouseId},#{channelType},#{channelCode},#{merchantId},
        #{outCreateTime},#{mobile},#{userCode},
        #{syncWmsStatus},#{batchStatus}, #{syncTransferStatus},#{outOrInTime})
    </insert>

    <insert id="insertUserReturnWarehouseRecord" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,sap_order_code,business_type,record_status,
        record_type,user_code,real_warehouse_id,channel_type,channel_code,out_or_in_time,delivery_time,
        merchant_id,out_create_time,mobile,reason,sync_wms_status, sync_transfer_status,batch_status)
        values(#{recordCode},#{sapOrderCode},#{businessType},#{recordStatus},#{recordType},
        #{userCode},#{realWarehouseId},#{channelType},#{channelCode},#{outOrInTime},#{deliveryTime},
        #{merchantId},#{outCreateTime},#{mobile},
        #{reason},#{syncWmsStatus}, #{syncTransferStatus},#{batchStatus})
    </insert>

    <update id="updateCompleteStatus">
        update sc_warehouse_record set real_warehouse_id = #{realWarehouseId},virtual_warehouse_id=#{virtualWarehouseId}, record_status = 15 where id=#{id}
    </update>


    <select id="getWarehouseRecordSyncWmsStatusById" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select sync_wms_status
        from sc_warehouse_record
        where is_deleted = 0
        and is_available = 1
        and id =#{id}
    </select>

    <!--根据页面查询条件查询出库单-->
    <select id="queryGroupWarehouseRecordList" resultMap="WarehouseRecordMap"
            parameterType="com.rome.stock.core.api.dto.qry.GroupWarehouseRecordCondition">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record record
        where record.is_deleted = 0
        and business_type =1
        <if test="recordType!=null">
            and record.record_type = #{recordType}
        </if>
        <if test="recordCode!=null and '' !=recordCode">
            and record.record_code = #{recordCode}
        </if>

        <if test="realWarehouseId!=null ">
            and record.real_warehouse_id = #{realWarehouseId}
        </if>
        <if test="recordStatus!=null ">
            and record.record_status = #{recordStatus}
        </if>
        <if test="startTime!=null ">
            and record.create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null ">
            and record.create_time &lt;= #{endTime}
        </if>
        <if test="null != startPayTime and null != endPayTime">
            and pay_time between #{startPayTime} and #{endPayTime}
        </if>
        <if test="syncWmsStatus!=null ">
            and record.sync_wms_status = #{syncWmsStatus}
        </if>
        <if test="syncFulfillmentStatus!=null ">
            and record.sync_fulfillment_status = #{syncFulfillmentStatus}
        </if>
        <if test="tmsRecordCode!=null ">
            and record.tms_record_code = #{tmsRecordCode}
        </if>

        <!--<if test="channelCodeList != null and channelCodeList.size()>0 ">-->
            <!--and record.channel_code in-->
            <!--<foreach item="item" index="index" collection="channelCodeList" open="(" separator="," close=")">-->
                <!--#{item}-->
            <!--</foreach>-->
        <!--</if>-->
        <if test="warehouseRecordIds != null and warehouseRecordIds.size()>0">
            and record.id in
            <foreach item="item" index="index" collection="warehouseRecordIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by id desc
    </select>

    <select id="queryGroupWarehouseRecordCodeList" resultType="java.lang.Integer"
            parameterType="com.rome.stock.core.api.dto.qry.GroupWarehouseRecordCondition">
        select count(1)
        from sc_warehouse_record record
        where record.is_deleted = 0
        and business_type =1
        <if test="recordType!=null">
            and record.record_type = #{recordType}
        </if>
        <if test="userCode!=null and '' !=userCode">
            and record.user_code = #{userCode}
        </if>
        <if test="recordCode!=null and '' !=recordCode">
            and record.record_code = #{recordCode}
        </if>

        <if test="realWarehouseId!=null ">
            and record.real_warehouse_id = #{realWarehouseId}
        </if>
        <if test="recordStatus!=null ">
            and record.record_status = #{recordStatus}
        </if>
        <if test="startTime!=null ">
            and record.create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null ">
            and record.create_time &lt;= #{endTime}
        </if>
        <if test="null != startPayTime and null != endPayTime">
            and pay_time between #{startPayTime} and #{endPayTime}
        </if>
        <if test="syncWmsStatus!=null ">
            and record.sync_wms_status = #{syncWmsStatus}
        </if>
        <if test="syncFulfillmentStatus!=null ">
            and record.sync_fulfillment_status = #{syncFulfillmentStatus}
        </if>
        <if test="tmsRecordCode!=null ">
            and record.tms_record_code = #{tmsRecordCode}
        </if>

        <if test="channelCodeList != null and channelCodeList.size()>0 ">
            and record.channel_code in
            <foreach item="item" index="index" collection="channelCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warehouseRecordIds != null and warehouseRecordIds.size()>0">
            and record.id in
            <foreach item="item" index="index" collection="warehouseRecordIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="skuCode!=null and '' !=skuCode">
            and exists (select detail.warehouse_record_id
            from sc_warehouse_record_detail detail
            where record.id = detail.warehouse_record_id and detail.is_deleted=0
            and detail.sku_code=#{skuCode})
        </if>
    </select>

    <select id="querySalesReturnWarehouseRecordList" resultMap="WarehouseRecordMap"
            parameterType="com.rome.stock.core.api.dto.qry.SalesReturnRecordParamDTO">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0
        and business_type =2
        <if test="null != recordCode and '' != recordCode">
            and record_code = #{recordCode}
        </if>
        <if test="null != recordType">
            and record_type = #{recordType}
        </if>
        <if test="channelCodeList != null">
            and channel_code in
            <foreach item="item" index="index" collection="channelCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="realWarehouseId!=null">
            and real_warehouse_id = #{realWarehouseId}
        </if>

        <if test="null != recordStatus">
            and record_status = #{recordStatus}
        </if>
        <if test="null != startCrateTime and null != endCreateTime">
            and create_time between #{startCrateTime} and #{endCreateTime}
        </if>
        <if test="null != reason and '' != reason   ">
            and reason like CONCAT('%',#{reason},'%' )
        </if>
        <if test="warehouseRecordIds != null">
            and id in
            <foreach item="item" index="index" collection="warehouseRecordIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by id desc
    </select>

    <select id="queryInWarehouseRecordList" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        <bind name="inType" value="@com.rome.stock.core.constant.WarehouseRecordBusinessTypeVO@IN_WAREHOUSE_RECORD"/>
        select
        id,record_code,record_status,record_type,real_warehouse_id,create_time
        from sc_warehouse_record
        where is_deleted = 0 and is_available = 1
        and business_type = #{inType.type}
        ORDER BY id desc
    </select>

    <select id="queryInWarehouseRecordPage" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0 and is_available = 1
        and record_type in
        <foreach collection="types" item="recordType" open="(" close=")" separator=",">
            #{recordType}
        </foreach>
        <if test="ids.size()>0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="realWarehouseIds.size()>0">
            and real_warehouse_id in
            <foreach collection="realWarehouseIds" item="realWarehouseId" open="(" close=")" separator=",">
                #{realWarehouseId}
            </foreach>
        </if>
        <if test="warehouseRecord.recordCode != null and warehouseRecord.recordCode != '' ">
            and record_code = #{warehouseRecord.recordCode}
        </if>
        <if test="warehouseRecord.recordStatus != null">
            and record_status = #{warehouseRecord.recordStatus}
        </if>
        <if test="warehouseRecord.startDate != null ">
            <![CDATA[ AND create_time >= date_format(#{warehouseRecord.startDate},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="warehouseRecord.endDate != null ">
            <![CDATA[ AND create_time <= date_format(#{warehouseRecord.endDate},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="null != warehouseRecord.sapOrderCode and '' != warehouseRecord.sapOrderCode">
            and sap_order_code = #{warehouseRecord.sapOrderCode}
        </if>
        ORDER BY id desc
    </select>

    <select id="queryOutWarehouseRecordPage"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0 and is_available = 1
        and record_type in
        <foreach collection="types" item="recordType" open="(" close=")" separator=",">
            #{recordType}
        </foreach>
        <if test="ids.size()>0">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="realWarehouseIds.size()>0">
            and real_warehouse_id in
            <foreach collection="realWarehouseIds" item="realWarehouseId" open="(" close=")" separator=",">
                #{realWarehouseId}
            </foreach>
        </if>
        <if test="warehouseRecord.recordCode != null and warehouseRecord.recordCode != '' ">
            and record_code = #{warehouseRecord.recordCode}
        </if>
        <if test="warehouseRecord.recordStatus != null">
            and record_status = #{warehouseRecord.recordStatus}
        </if>
        <!-- <if test="warehouseRecord.recordStatus == 0">
                and record_status = #{warehouseRecord.recordStatus}
            </if>-->
        <if test="warehouseRecord.startDate != null">
            <![CDATA[ AND create_time >= date_format(#{warehouseRecord.startDate},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="warehouseRecord.endDate != null ">
            <![CDATA[ AND create_time <= date_format(#{warehouseRecord.endDate},'%Y-%m-%d %H:%i:%s')]]>
        </if>
        <if test="null != warehouseRecord.sapOrderCode and '' != warehouseRecord.sapOrderCode">
            and sap_order_code = #{warehouseRecord.sapOrderCode}
        </if>
        ORDER BY id desc
    </select>

    <select id="queryOutWarehouseRecordList"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        <bind name="inType" value="@com.rome.stock.core.constant.WarehouseRecordBusinessTypeVO@OUT_WAREHOUSE_RECORD"/>
        select
        id,record_code,record_status,record_type,real_warehouse_id,create_time
        from sc_warehouse_record
        where is_deleted = 0 and is_available = 1
        and business_type = #{inType.type}
        <if test="syncWmsStatus != null">
            and sync_wms_status= #{syncWmsStatus}
        </if>
        ORDER BY id desc
    </select>

    <select id="getByCode" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        WHERE
        is_available = 1 and is_deleted = 0
        and record_code = #{code}
        <if test="recordType != null">
            and record_type = #{recordType}
        </if>
    </select>

    <select id="getById" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        WHERE
        is_available = 1 and is_deleted = 0
        and id = #{id}
    </select>

    <!--更新入库单单状态为待处理状态，修改采购单-->
    <update id="updateToInitStatus" parameterType="java.lang.Integer">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@IN_ALLOCATION"/>
        update sc_warehouse_record
        set record_status=#{afterRecordStatus.status}
        where id=#{id} and record_status=#{beforeRecordStatus.status}
        AND is_deleted=0
        AND is_available=1
    </update>

    <!-- -->
    <update id="updateSyncTradeStatus">
        update `sc_warehouse_record`
        set `sync_trade_status` = 2
        where id = #{id}
        AND is_deleted = 0
        AND is_available = 1
    </update>

    <select id="queryWareHouseRecordList" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_available = 1 and is_deleted = 0
        and sync_wms_status = 1
        and business_type = #{businessType}
        and real_warehouse_id in
        <foreach item="item" index="index" collection="realWareHouseIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id asc
    </select>

    <!-- 批量更新wms同步状态为已同步状态
        0 - 无需同步
        1 - 未同步
        2 - 已同步
    -->
    <update id="updateRecordSyncStatusToSynchronized" parameterType="list">
        update sc_warehouse_record
        set sync_wms_status = 2, receiver_time= now()
        where record_code in
        <foreach collection="recordCodeList" item="recordCode" separator="," close=")" open="(">
            #{recordCode}
        </foreach>
        and sync_wms_status = 1
    </update>

    <!--更新出库单状态为已出库-->
    <update id="updateToOutAllocation">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@OUT_ALLOCATION"/>
        update `sc_warehouse_record` set `record_status` = #{afterRecordStatus.status},out_or_in_time = now()
        where `id` = #{id} and `record_status` = #{beforeRecordStatus.status}
    </update>

    <!--更新出库单状态由已锁定到已出库-->
    <update id="updateToOutFromLocked">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@LOCK"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@OUT_ALLOCATION"/>
        update `sc_warehouse_record` set `record_status` = #{afterRecordStatus.status}
        where `id` = #{id} and `record_status` = #{beforeRecordStatus.status}
    </update>

    <!--更新出库单状态由已出库到已取消-->
    <update id="updateToCancelFromOut">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@OUT_ALLOCATION"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        update `sc_warehouse_record` set `record_status` = #{afterRecordStatus.status}
        where `id` = #{id} and `record_status` = #{beforeRecordStatus.status}
    </update>

    <update id="updateCancelReservation">
        update `sc_warehouse_record` set `record_status` =2
        where `id` = #{id}
    </update>

    <!--更新出库单状态由已锁定到已取消-->
    <update id="updateToCancelFromLocked">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@LOCK"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        update `sc_warehouse_record` set `record_status` = #{afterRecordStatus.status}
        where `id` = #{id} and `record_status` = #{beforeRecordStatus.status}
    </update>


    <!--初始化状态 更新为 已入库-->
    <update id="updateToInAllocation">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@IN_ALLOCATION"/>
        update sc_warehouse_record
        set record_status = #{afterRecordStatus.status} ,
        <choose>
            <when test="outOrInTime != null ">
                out_or_in_time = #{outOrInTime}
            </when>
            <otherwise>
                out_or_in_time = now()
            </otherwise>
        </choose>
        where id=#{id} and record_status = #{beforeRecordStatus.status}
    </update>

    <!--更新do单为取消状态-->
    <update id="updateWarehouseRecordStatus">
        update sc_warehouse_record set record_status=#{status}
        where id=#{id}
    </update>

    <!--更新团购单据信息-->
    <update id="updateWarehouseRecordByGroupBy" parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        update sc_warehouse_record
        set real_warehouse_id=#{realWarehouseId},
        sync_wms_status=#{syncWmsStatus},
        tenant_id=null,
        virtual_warehouse_id=#{virtualWarehouseId}
        where record_code=#{recordCode}
        and  is_deleted = 0 and is_available = 1
        and real_warehouse_id !=#{realWarehouseId}
    </update>

    <update id="updateWarehouseRecordByWarehouseId" parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        update sc_warehouse_record
        set real_warehouse_id=#{realWarehouseId},
            sync_wms_status=#{syncWmsStatus},
            tenant_id=null,
            virtual_warehouse_id=#{virtualWarehouseId}
        where record_code=#{recordCode}
          and  is_deleted = 0 and is_available = 1
    </update>

    <update id="updateWarehouseRecordByWarehouseIdAndStatus" parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        update sc_warehouse_record
        set real_warehouse_id=#{realWarehouseId},
            sync_wms_status=#{syncWmsStatus},
            record_status=#{recordStatus},
            tenant_id=null,
            virtual_warehouse_id=#{virtualWarehouseId}
        where record_code=#{recordCode}
          and  is_deleted = 0 and is_available = 1
    </update>


    <!-- 根据id批量查询出入库单数据 -->
    <select id="getByIds" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from `sc_warehouse_record`
        <where>
            `id` in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND is_deleted = 0
            AND is_available = 1
        </where>
    </select>

    <!-- 根据id批量查询出入库单数据 -->
    <select id="queryWarehouseRecordByIds" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from `sc_warehouse_record`
        <where>
            `id` in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            AND is_deleted = 0
            AND is_available = 1
        </where>
    </select>

    <!-- 更新出库单状态为已取消，wms同步状态为无需同步 -->
    <update id="updateToCanceled">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status}
        where id = #{id}
    </update>

    <!-- 更新出库单状态为已取消，wms同步状态为无需同步，从已同步wms改为已取消 -->
    <update id="updateToCanceledFromHasSync">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@SYNCHRONIZED"/>
        <bind name="beforeForceSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@FORCESYNCH"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status},
        version_no = version_no + 1
        where id = #{id} and sync_wms_status in (#{beforeSyncWmsStatus.status} , #{beforeForceSyncWmsStatus.status})
    </update>

    <!-- 更新出库单状态为已取消，wms同步状态为无需同步 ，从未同步wms改为已取消-->
    <update id="updateToCanceledFromUnSync">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@UNSYNCHRONIZED"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status},
        version_no = version_no + 1
        where id = #{id} and sync_wms_status in (1,3,4,5)
    </update>

        <update id="updateToCanceledPurchaseReturn">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@UNSYNCHRONIZED"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status}
        where id = #{id}
    </update>


    <!-- 更新出库单状态为已取消，wms同步状态为无需同步 ，从回撤改为已取消，且执行这个sql前的状态一定是非取消状态-->
    <update id="updateToCanceledFromBack">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@BACKCANCLE"/>
        <bind name="beforeSyncWmsStatus2" value="@com.rome.stock.core.constant.WmsSyncStatusVO@STOPDELIVERY"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status},
        version_no = version_no + 1
        where id = #{id} and sync_wms_status in (#{beforeSyncWmsStatus.status},#{beforeSyncWmsStatus2.status})
        and record_status!= #{afterRecordStatus.status}
    </update>


    <!-- 更新出库单状态为已取消，wms同步状态为无需同步 -->
    <update id="updateToCanceledFromComplete">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@OUT_ALLOCATION"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status}
        where id = #{id}
         and record_status=#{beforeRecordStatus.status}
    </update>

    <!-- 更新叫货出库单状态为已取消，wms同步状态为无需同步 -->
    <update id="updateReplenishToCanceled">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@SYNCHRONIZED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update sc_warehouse_record
        set record_status = #{afterRecordStatus.status},
        sync_wms_status = #{afterSyncWmsStatus.status}
        where id = #{id}
        and record_status = #{beforeRecordStatus.status}
        and sync_wms_status != #{beforeSyncWmsStatus.status}
    </update>

    <!-- 更新调拨出库单状态为已取消，wms同步状态为无需同步 -->
    <update id="updateWhToCanceled">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@SYNCHRONIZED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update sc_warehouse_record
        set record_status = #{afterRecordStatus.status},
        sync_wms_status = #{afterSyncWmsStatus.status},
        cmp_status=0
        where id = #{id}
        and record_status = #{beforeRecordStatus.status}
        <!--and sync_wms_status != #{beforeSyncWmsStatus.status}-->
    </update>

    <update id="batchUpdateWhToCanceled">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@SYNCHRONIZED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update sc_warehouse_record
        set record_status = #{afterRecordStatus.status},
        sync_wms_status = #{afterSyncWmsStatus.status},
        cmp_status=0
        where id in
         <foreach collection="ids" close=")" item="id" open="(" separator=",">
            #{id}
        </foreach>
        and record_status = #{beforeRecordStatus.status}
        <!--and sync_wms_status != #{beforeSyncWmsStatus.status}-->
    </update>


    <!-- 更新调拨出库单状态为已转移，wms同步状态为无需同步 -->
    <update id="updateWhToTransfer">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@TRANSFER"/>
        <bind name="beforeSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@SYNCHRONIZED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update sc_warehouse_record
        set record_status = #{afterRecordStatus.status},
        sync_wms_status = #{afterSyncWmsStatus.status}
        where id = #{id}
        and record_status = #{beforeRecordStatus.status}
        <!--and sync_wms_status != #{beforeSyncWmsStatus.status}-->
    </update>

    <update id="updateRecordBatchStatus">
        <bind name="afterBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@COMPLETE"/>
        <bind name="beforeBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@INIT"/>
        update sc_warehouse_record set batch_status = #{afterBatchStatus.status}
        where record_code =#{recordCode} and batch_status=#{beforeBatchStatus.status}
    </update>

    <update id="updateRecordBatchStatusToInit">
        <bind name="afterBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@INIT"/>
        <bind name="beforeBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@NO_HANDLING"/>
        update sc_warehouse_record set batch_status = #{afterBatchStatus.status}
        where record_code =#{recordCode} and batch_status=#{beforeBatchStatus.status}
    </update>


    <update id="updateRecordBatchStatusToInitFromComplete">
        <bind name="afterBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@INIT"/>
        <bind name="beforeBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@COMPLETE"/>
        update sc_warehouse_record set batch_status = #{afterBatchStatus.status}
        where record_code =#{recordCode} and batch_status=#{beforeBatchStatus.status}
    </update>

    <update id="updateRecordBatchFailStatus">
        <bind name="afterBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@EXCEPTION"/>
        <bind name="beforeBatchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@INIT"/>
        update sc_warehouse_record set batch_status = #{afterBatchStatus.status}
        where id =#{id} and batch_status=#{beforeBatchStatus.status}
    </update>

    <update id="updateRecordSyncWmsFailTime">
        update
          sc_warehouse_record
        set
          sync_wms_fail_time= now()
        where
          record_code in
          <foreach collection="recordCodes" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
    </update>

    <select id="queryWarehouseBatchStatus" resultType="java.lang.Integer">
        select batch_status from sc_warehouse_record where record_code =#{recordCode}
    </select>

    <select id="queryWarehouseRecordListByModel"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        id,record_code, sap_order_code, record_status, record_type, real_warehouse_id, factory_code, create_time,
        tms_record_code,self_takeout,merchant_id, channel_code,creator,business_type,app_id
        from
        sc_warehouse_record
        where
        is_deleted = 0
        and is_available = 1
        and sync_wms_status= 1
        and business_type = #{businessType}
        and update_time &gt;= DATE_SUB(now(), INTERVAL 15 DAY)
        and record_status = 0
        and (sync_wms_fail_time is null or sync_wms_fail_time &lt;= DATE_SUB(now(), INTERVAL 5 MINUTE))
        and self_takeout = #{selfTakeout}
        <if test="recordCode != null and recordCode!=''">
            and record_code = #{recordCode}
        </if>
        <if test="warehouseIdList != null and warehouseIdList.size() > 0">
            and real_warehouse_id in
            <foreach collection="warehouseIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="recordTypeList !=null and recordTypeList.size()>0">
            and record_type in
            <foreach collection="recordTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and MOD(id,#{shardingTotalCount})=#{shardingItem}
    </select>

    <select id="queryWarehouseRecordListByModelForOnlineAccount"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        id,record_code, sap_order_code, record_status, record_type, real_warehouse_id, factory_code, create_time,
        tms_record_code,self_takeout,merchant_id,out_or_in_time,channel_code
        from
        sc_warehouse_record
        where
        is_deleted = 0
        and is_available = 1
        and business_type = #{businessType}
        and record_status = #{recordStatus}
        and out_or_in_time &gt;= date_format(#{startDate},'%Y-%m-%d 00:00:00')
        and out_or_in_time &lt;= date_format(#{startDate},'%Y-%m-%d 23:59:59')
        and record_type in
        <foreach collection="recordTypeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="warehouseIdList != null and warehouseIdList.size() > 0">
            and real_warehouse_id in
            <foreach collection="warehouseIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="queryWareHouseRecordListByBatchStatus"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        <bind name="batchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@INIT"/>
      <!--  <bind name="businessType"
              value="@com.rome.stock.core.constant.WarehouseRecordBusinessTypeVO@OUT_WAREHOUSE_RECORD"/>
              -->
        select id,record_code,record_type,real_warehouse_id,batch_status from sc_warehouse_record
        where batch_status = #{batchStatus.status}
    <!--  and business_type = #{businessType.type}  -->
       <![CDATA[ and create_time >= date_format(#{startDate},'%Y-%m-%d %H:%i:%s')]]>
       <![CDATA[ and create_time <= date_format(#{endDate},'%Y-%m-%d %H:%i:%s')]]>
       and is_available = 1 and is_deleted = 0
    </select>

<select id="getBySapCode" parameterType="java.lang.String"
       resultMap="WarehouseRecordMap">
   select
   <include refid="BASE_COLUMN"/>
   from sc_warehouse_record
   WHERE
   is_available = 1 and is_deleted = 0
   and sap_order_code = #{sapCode}
   order by id desc
</select>

    <select id="getUnCanceledBySapCode" parameterType="java.lang.String"
            resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        WHERE
        is_available = 1 and is_deleted = 0 and record_status != 2
        and sap_order_code = #{sapCode}
        order by id desc
    </select>

    <select id="getBySapCodeAndRwId" parameterType="java.lang.String"
            resultMap="WarehouseRecordMap">
      select
      <include refid="BASE_COLUMN"/>
      from sc_warehouse_record
      WHERE
      is_available = 1 and is_deleted = 0
      <if test="null != sapCode and '' != sapCode">
          and sap_order_code = #{sapCode}
      </if>
      and real_warehouse_id = #{rwId}
    </select>

<!--保存出入库单包含派车状态-->
    <insert id="insertWrWithDispatchInfo" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        insert into sc_warehouse_record(record_code,business_type,record_status,record_type,virtual_warehouse_id,
        real_warehouse_id,channel_type,channel_code,merchant_id,user_code,mobile,
        out_create_time,sync_wms_status,sap_order_code,
        sync_dispatch_status, tms_record_code, sync_transfer_status,
         sync_trade_status,batch_status, delivery_time, out_or_in_time,cmp_status,app_id,reasons
        <if test="null != tenantId">
            ,tenant_id
        </if>
        <if test="boxBatchTag !=  null ">
            ,box_batch_tag
        </if>
        <if test="null != creator">
            ,creator
        </if>
        )
        values(#{recordCode},#{businessType},#{recordStatus},#{recordType},#{virtualWarehouseId},#{realWarehouseId},
        #{channelType},#{channelCode},#{merchantId},#{userCode},#{mobile},
        #{outCreateTime},#{syncWmsStatus},#{sapOrderCode},
         #{syncDispatchStatus}, #{tmsRecordCode}, #{syncTransferStatus},
         #{syncTradeStatus},#{batchStatus}, #{deliveryTime}, #{outOrInTime}, #{cmpStatus},#{appId},#{reasons}
        <if test="null != tenantId">
            ,#{tenantId}
        </if>
        <if test="boxBatchTag !=  null ">
            ,#{boxBatchTag}
        </if>
        <if test="null != creator">
            ,#{creator}
        </if>
        )
    </insert>

  <!--  更新 sync_fulfillment_status 待推送销售中心状态-->
    <update id="updateFulfillmentStatus">
        update sc_warehouse_record set sync_fulfillment_status=10
        where id=#{id} and sync_fulfillment_status=0
    </update>

    <!--更新推送销售中心状态-->
    <update id="updateSyncTradeToBeStatus">
        update sc_warehouse_record set sync_trade_status=1
        where id=#{id} and  sync_trade_status=0
    </update>

    <!--更新第一次收货通知采购中心-->
    <update id="updateTenantIdByRecordCode">
        update sc_warehouse_record set tenant_id=10
        where record_code=#{recordCode}
    </update>

    <!--更新收货完成标识-->
    <update id="updateReceiptCompleted">
        update sc_warehouse_record set sync_fulfillment_status=30
        where record_code=#{recordCode}
    </update>


    <!--更新收货完成标识并设置出入库时间-->
    <update id="updateReceiptCompletedAndInTime">
        update sc_warehouse_record
        set sync_fulfillment_status=30,out_or_in_time=now()
        where record_code=#{recordCode}
    </update>


    <!--更新待推送采购中心-->
    <update id="updateSyncPurchaseToBeStatus">
        update sc_warehouse_record set sync_purchase_status=1
        where id=#{id} and  sync_purchase_status=0
    </update>

    <!--批量更新待推送采购中心-->
    <update id="updateSyncPurchaseStatusByRecordCodes">
          update sc_warehouse_record set sync_purchase_status=1
          <where>
              sync_purchase_status=0
              and record_code in
              <foreach collection="recodeCodeList" item="recordCode" separator="," open="(" close=")">
                  #{recordCode}
              </foreach>
          </where>
    </update>
    

    <!--推送采购中心成功-->
    <update id="updateSyncPurchaseSuccess">
        update sc_warehouse_record set sync_purchase_status=2
        where record_code=#{recordCode} and  sync_purchase_status=1
    </update>

    <!--更新派车同步状态为待派车状态-->
    <update id="updateDispatchStatusToNeedDispatch" parameterType="list">
        update sc_warehouse_record
        set sync_dispatch_status = 1
        where id = #{id}
        and sync_dispatch_status = 0
    </update>

    <!--批量更新wms同步状态为待同步状态,并更新sap单号-->
    <update id="updateSyncStatusAndSapCode">
        update sc_warehouse_record
        set sync_wms_status = 1,
        sap_order_code = #{sapOrderCode},
        logistics_code = #{logisticsCode},
        tms_record_code = #{tmsRecordCode}
        where id = #{id}
        and sync_wms_status in(0,1)
    </update>

    <update id="updateSyncWmsStatus">
        update sc_warehouse_record
        set sync_wms_status = 1
        where id = #{id} and sync_wms_status = 0 and record_status = 0
    </update>

    <update id="updateTmsMsg">
        update sc_warehouse_record
        set sap_order_code = #{sapOrderCode},
            sync_dispatch_status = #{syncDispatchStatus},
            logistics_code = #{logisticsCode},
            tms_record_code = #{tmsRecordCode}
        where id = #{id}
    </update>

    <!--更新sap、TmsCode单号-->
    <update id="updateTmsCodeAndSapCode">
        update sc_warehouse_record
        set sap_order_code = #{sapOrderCode},
        <if test="syncDispatchStatus!=null and '' !=syncDispatchStatus">
            sync_dispatch_status=#{syncDispatchStatus},
        </if>
        logistics_code = #{logisticsCode},
        tms_record_code = #{tmsRecordCode}
        where id = #{id}
    </update>


    <update id="updateAndSapCodeAndNoSyncStatus">
        update sc_warehouse_record
        set sap_order_code = #{sapOrderCode},tenant_id=null,
        tms_record_code = #{tmsRecordCode}
        where id = #{id}
        and sync_wms_status = 0
    </update>

    <!--批量更新wms同步状态为待同步状态,并更新sap单号-->
    <update id="updateSapCode">
        update sc_warehouse_record
        set
        sap_order_code = #{sapOrderCode},
        tms_record_code = #{tmsRecordCode}
        where id = #{id}
        and sync_wms_status = 2
    </update>

    <!--更新派车同步状态为已下发排车成功-->
    <update id="updateDispatchSucc">
        update sc_warehouse_record
        set sync_dispatch_status = 2
        where id = #{id}
        and sync_dispatch_status = 1
    </update>


    <!--更新sap交货单号-->
    <update id="updateDeliveryCode">
        update sc_warehouse_record
        set sap_order_code = #{sapCode}
        where id = #{id}
        and sap_order_code is null
    </update>

    <!--更新sap交货单号-->
    <update id="updateDeliveryCodeByCode">
        update sc_warehouse_record
        set sap_order_code = #{sapCode}
        where record_code = #{recordCode}
    </update>

    <!--分页查询待过账的出入库单-->
    <select id="getWaitTransferOrder" resultMap="WarehouseRecordMap">
        select
        a.id,record_code,sap_order_code,business_type,record_status,record_type,virtual_warehouse_id,
        real_warehouse_id,merchant_id,channel_type,channel_code,a.factory_code,
        delivery_time,receiver_time,a.create_time,a.update_time,a.creator,
        sync_wms_status,sync_dispatch_status,out_or_in_time,a.app_id,
        batch_status,sync_transfer_status,cmp_status,sync_trade_status,sync_wms_fail_time,self_takeout,sync_fulfillment_status

        from sc_warehouse_record a FORCE INDEX(idx_update_time_sync_transfer_status) ,`sc_real_warehouse`  b
        where a.sync_transfer_status = 1  <!-- 过账状态 0.无需过账 1.待过账 2.已过账 -->
        and a.record_status != 2
        and a.is_available = 1
        and a.is_deleted = 0
        and b.is_available =1
        and b.is_deleted = 0
        and a.`real_warehouse_id` = b.id
        <if test="realWarehouseType != null and realWarehouseType == 1">
            AND b.`real_warehouse_type` =1
        </if>
        <if test="realWarehouseType == null">
            AND b.`real_warehouse_type` !=1
        </if>
        and a.update_time > DATE_SUB(NOW(),INTERVAL 45 DAY)
        AND a.update_time &lt; DATE_SUB(NOW(),INTERVAL 2 MINUTE) <!-- 尽量避免过账跟取消回归并发，只过 已经超过2分钟的单子 -->
        AND a.id > #{minId}
        order by a.id asc
        limit #{maxResult}
    </select>




    <!--更新过账状态为已过账成功-->
    <update id="updateTransferSucc" parameterType="java.lang.Long">
        update sc_warehouse_record
        set sync_transfer_status = 2
        where id = #{id}
        and sync_transfer_status = 1
    </update>

    <!--更新过账状态为过账中-->
    <update id="updateTransferForProcessing" parameterType="java.lang.Long">
        update sc_warehouse_record
        set sync_transfer_status = 10  <!--过账中-->
        where id = #{id}
        and sync_transfer_status = 1
    </update>

    <!--更新过账状态从过账中到待成功-->
    <update id="updateWaitTransferFromProcessing" >
        update sc_warehouse_record
        set sync_transfer_status = 1
        where record_code = #{recordCode}
        and sync_transfer_status = 10  <!--过账中-->
    </update>

    <!--更新过账状态从过账中到过账成功-->
    <update id="updateSuccessForProcessing" >
        update sc_warehouse_record
        set sync_transfer_status = 2
        where record_code = #{recordCode}
        and sync_transfer_status in (1, 10)  <!--过账中-->
    </update>



    <!--分页查询待同步收货信息给屡单系统的单子：已同步合单并且已出库回调-->
    <select id="getWaitSyncDeliveryToFulfillmentOrder" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where sync_fulfillment_status = 1  <!--  0-无需同步 1-待同步交货信息 2-已同步 -->
        and  record_status = 11   <!-- 11 已出库 -->
        and is_available = 1
        and is_deleted = 0
        and create_time > DATE_SUB(NOW(),INTERVAL 30 DAY)
        limit #{page},#{maxResult}
    </select>


    <!--更新状态为已同步交货信息-->
    <update id="updateToHasSyncDelivery" parameterType="java.lang.Long">
        update sc_warehouse_record
        set sync_fulfillment_status = 2
        where id = #{id}
        and sync_fulfillment_status = 1
    </update>

    <!--更新状态为已同步交货信息-->
    <update id="updateToGroupPurchaseHasSyncDelivery" parameterType="java.lang.Long">
        update sc_warehouse_record
        set sync_fulfillment_status = 20
        where id = #{id}
        and sync_fulfillment_status = 10
    </update>



    <!--更新过账状态为待过账-->
    <update id="updateToWaitTransfer" parameterType="java.lang.Long">
        update sc_warehouse_record
        set sync_transfer_status = 1
        where id = #{id}
        and sync_transfer_status = 0
    </update>

    <!--更新cmpStatus标识-->
    <update id="updateCmp7Status" parameterType="java.lang.Long">
        update sc_warehouse_record
        set cmp_status = #{status}
        <if test="operator !=null">
            ,order_remark_user=#{operator}
        </if>
        where id = #{id}
    </update>

    <!--更新cmpStatus标识-->
    <update id="updateCmp7StatusAndSyncDispatchStatus">
        update sc_warehouse_record
        set cmp_status = #{status}
        where id = #{id}
    </update>

    <!--更新用户编码-->
    <update id="updateUserCode">
        update sc_warehouse_record set user_code=#{userCode}
        where id=#{id}
    </update>

    <select id="queryWaitWarehouseCmpList"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        <bind name="cmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@NEED_CMP"/>
        select
        id,record_code, sap_order_code, business_type, record_status, record_type, real_warehouse_id, factory_code,
        create_time
        from
        sc_warehouse_record
        where
        is_deleted = 0
        and is_available = 1
        and cmp_status = #{cmpStatus}
        and record_type in
        <foreach collection="typeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and `create_time` >  DATE_SUB(CURRENT_TIMESTAMP,INTERVAL #{subDay} DAY)
    </select>


    <!--查询补货单据结果待推送cmp的列表-->
    <select id="queryWaitCmpResultList"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        <bind name="cmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@NEED_CMP7"/>
        select
        id,record_code, sap_order_code, business_type, record_status, record_type, real_warehouse_id, factory_code,
        create_time
        from
        sc_warehouse_record
        where
        is_deleted = 0
        and record_status=12
        and is_available = 1
        and cmp_status = #{cmpStatus}
        and record_type in
        <foreach collection="typeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and `create_time` >  DATE_SUB(CURRENT_TIMESTAMP,INTERVAL #{subDay} DAY)
    </select>

        <!--查询待推送cmp7的单据-->
    <select id="queryWaitCmp7WarehouseCmpList" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        <bind name="dispatchStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@NEED_CMP7"/>
        select
        id,record_code, sap_order_code, business_type, record_status, record_type, real_warehouse_id, factory_code,
        create_time
        from
        sc_warehouse_record
        where
        is_deleted = 0
        and is_available = 1
        and sync_dispatch_status = #{dispatchStatus}
        and record_type in
        <foreach collection="typeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and `create_time` >  DATE_SUB(CURRENT_TIMESTAMP,INTERVAL #{subDay} DAY)
    </select>



    <update id="updateCmpStatusComplete" parameterType="java.lang.Long">
        <bind name="beforeCmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@NEED_CMP"/>
        <bind name="afterCmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@CMP_SUCCES"/>
        update sc_warehouse_record set cmp_status = #{afterCmpStatus} where id =#{id} and cmp_status =
        #{beforeCmpStatus}
    </update>

    <!--更新推送cmp7的状态-->
    <update id="updateCmp7StatusComplete" parameterType="java.lang.Long">
        <bind name="beforeCmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@NEED_CMP7"/>
        <bind name="afterCmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@CMP7_SUCCESS"/>
        update sc_warehouse_record set sync_dispatch_status = #{afterCmpStatus} where id =#{id}
        and sync_dispatch_status =#{beforeCmpStatus}
    </update>

    <!--更新门店补货、退货推送cmp结果成功状态-->
    <update id="updateShopResultSuccess" parameterType="java.lang.Long">
            <bind name="beforeCmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@NEED_CMP7"/>
        <bind name="afterCmpStatus" value="@com.rome.stock.common.constants.warehouse.WarehouseRecordConstant@CMP7_SUCCESS"/>
        update sc_warehouse_record set cmp_status=#{afterCmpStatus}
        where id=#{id} and  cmp_status=#{beforeCmpStatus}
    </update>

   <update id="updateUnSyncWmsRecord">
      update
        sc_warehouse_record
      set
       logistics_code = #{logisticsCode},
       tms_record_code= #{tmsCode}
      where
        is_deleted = 0
        and is_available = 1
        and record_code in
        <foreach collection="unSyncCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateSyncWmsRecord">
        update
          sc_warehouse_record
        set
          tms_record_code= #{tmsCode}
        where
          is_deleted = 0
          and is_available = 1
          and sync_wms_status= 2
          and record_code in
          <foreach collection="syncCodes" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
    </update>
    <update id="updateOutAllocation">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@OUT_ALLOCATION"/>
        update
          `sc_warehouse_record`
        set
          `record_status` = #{afterRecordStatus.status},
          <if test="orderConfirmTime != null and orderConfirmTime != ''">
              delivery_time = #{orderConfirmTime},
          </if>
        <choose>
            <when test="outOrInTime != null ">
                out_or_in_time = #{outOrInTime}
            </when>
            <otherwise>
                out_or_in_time = now()
            </otherwise>
        </choose>
        where
          `id` = #{recordId}
          and `record_status` = #{beforeRecordStatus.status}
    </update>

    <update id="updateInAllocation">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@INIT"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@IN_ALLOCATION"/>
        update
        `sc_warehouse_record`
        set
        `record_status` = #{afterRecordStatus.status},
        <if test="orderConfirmTime != null and orderConfirmTime != ''">
            delivery_time = #{orderConfirmTime},
        </if>
        <choose>
            <when test="outOrInTime != null ">
                out_or_in_time = #{outOrInTime}
            </when>
            <otherwise>
                out_or_in_time = now()
            </otherwise>
        </choose>
        where
        `id` = #{recordId}
        and `record_status` = #{beforeRecordStatus.status}
    </update>

    <update id="updateSyncWmsFailTimeAndStatus">
      update
          sc_warehouse_record
        set
          sync_wms_status = 1,
          sync_wms_fail_time= now(), receiver_time= null
        where
          record_code =#{recordCode}
          and sync_wms_status = 2
    </update>

    <select id="queryBySyncTradeStatus" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0 and sync_trade_status = 1
        and `create_time` >  DATE_SUB(CURRENT_TIMESTAMP,INTERVAL 7 DAY)
        limit #{page},#{maxResult}
    </select>

    <select id="queryAllBySyncTradeStatus" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0 and sync_trade_status = 1
        order by id
        limit #{page},#{maxResult}
    </select>



    <select id="queryWarehouseRecordListByCondition"
            resultType="com.rome.stock.core.api.dto.warehouserecord.SaleTobWarehouseRecordDTO">
        SELECT
        record.id,
        record.sap_order_code,
        record.record_code,
        record.record_status,
        record.record_type,
        record.tms_record_code,
        record.create_time,
        record.channel_code,
        record.real_warehouse_id outRealWarehouseId,
        record.sync_wms_status,
        record.sync_dispatch_status,
        record.sync_transfer_status,
        record.sync_wms_status,
        record.modifier,
        record.update_time
        FROM sc_warehouse_record record WHERE is_available = 1 AND is_deleted = 0
        <if test="condition.outRealWarehouseId != null">
            AND record.real_warehouse_id = #{condition.outRealWarehouseId}
        </if>
        <if test="condition.syncWmsStatus != null">
            AND record.sync_wms_status = #{condition.syncWmsStatus}
        </if>
        <if test="condition.recordStatus != null">
            AND record.record_status = #{condition.recordStatus}
        </if>
        <if test="condition.recordType != null">
            AND record.record_type = #{condition.recordType}
        </if>
        <if test="condition.tmsRecordCode != null and condition.tmsRecordCode != ''">
            AND record.tms_record_code = #{condition.tmsRecordCode}
        </if>
        <if test="condition.sapOrderCodes != null and condition.sapOrderCodes.size() > 0">
            AND sap_order_code in
            <foreach collection="condition.sapOrderCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.sapOrderCodeExist != null and condition.sapOrderCodeExist==0">
            AND record.sap_order_code is not null
        </if>
        <if test="condition.sapOrderCodeExist != null and condition.sapOrderCodeExist==1">
            AND record.sap_order_code is null
        </if>
        <if test="condition.ids != null and condition.ids.size() > 0">
            AND record.id IN
            <foreach collection="condition.ids" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.channelCodeList != null and condition.channelCodeList.size() > 0">
            AND record.channel_code IN
            <foreach collection="condition.channelCodeList" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.recordCodes != null and condition.recordCodes.size() > 0">
            AND record.record_code IN
            <foreach collection="condition.recordCodes" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        AND record.record_type IN
        <foreach collection="types" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        AND #{condition.startTime} &lt; record.create_time
        AND #{condition.endTime} &gt; record.create_time
        <if test="condition.skuCode!=null and '' !=condition.skuCode">
            and exists (select detail.warehouse_record_id
            from sc_warehouse_record_detail detail
            where record.id = detail.warehouse_record_id and detail.is_deleted=0
            and detail.sku_code=#{condition.skuCode})
        </if>
        ORDER BY id DESC
    </select>

    <select id="getRecordTypeByRecordCodes" resultMap="WarehouseRecordMap">
        SELECT
        <include refid="BASE_COLUMN"/>
        FROM sc_warehouse_record WHERE is_deleted = 0 AND is_available = 1
        <if test="recordCodes != null and recordCodes.size()>0">
            AND record_code IN
            <foreach collection="recordCodes" separator="," index="index" item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryWarehouseBatchList"
            resultType="java.lang.Long">
        <bind name="batchStatus" value="@com.rome.stock.core.constant.WarehouseRecordBatchStatusVO@INIT"/>
        <!--  <bind name="businessType"
              value="@com.rome.stock.core.constant.WarehouseRecordBusinessTypeVO@OUT_WAREHOUSE_RECORD"/>
              -->
        select id from sc_warehouse_record
        where batch_status = #{batchStatus.status}
         <!-- and business_type = #{businessType.type} -->
        <![CDATA[ and create_time >= date_format(#{startDate},'%Y-%m-%d %H:%i:%s')]]>
        <![CDATA[ and create_time <= date_format(#{endDate},'%Y-%m-%d %H:%i:%s')]]>
        and is_available = 1 and is_deleted = 0 limit 0,#{rows}
    </select>

    <select id="queryWarehouseRecordListByCode" resultMap="WarehouseRecordMap">
        SELECT
        <include refid="BASE_COLUMN"/>
        FROM sc_warehouse_record WHERE is_deleted = 0 AND is_available = 1
        <if test="sapOrderCodes != null and sapOrderCodes.size > 0">
            AND sap_order_code in
            <foreach collection="sapOrderCodes" item="sapOrderCode" open="(" close=")" separator=",">
                #{sapOrderCode}
            </foreach>
        </if>
        <if test="tmsRecordCode != null and tmsRecordCode != ''">
            AND tms_record_code = #{tmsRecordCode}
        </if>
    </select>

    <select id="queryNeedCancelWarehouseRecordList"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
      select
        <include refid="BASE_COLUMN" />
      from
        sc_warehouse_record
      where
        create_time &gt;= DATE_SUB(CURDATE(),INTERVAL 15 DAY)
        and create_time &lt; CURDATE()
        and business_type = 1
        and record_status != 2
        and sap_order_code IS NULL
        and record_type in
        <foreach collection="recordTypeList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </select>

    <!-- 根据SAP交货单号查询单据编码 -->
    <select id="queryRecordCodeBySapOrderCodes" resultType="java.lang.String">
        select DISTINCT record_code
        FROM sc_warehouse_record WHERE is_deleted = 0 AND is_available = 1
        AND sap_order_code IN
        <foreach collection="sapOrderCodes" item="sapOrderCode" open="(" close=")" separator=",">
        	#{sapOrderCode}
        </foreach>
    </select>

    <!-- 根据交货单号查询出库单号 -->
    <select id="queryRWCodeBySapOrderCode" resultType="java.lang.String">
    	select
    	DISTINCT record_code
    	FROM sc_warehouse_record WHERE is_deleted = 0 AND is_available = 1
        AND business_type = 1 AND record_type != 14
        AND sap_order_code = #{sapOrderCode}
        ORDER BY id DESC
        LIMIT 1
    </select>


  <!--  根据sapOrderCode 查询入库单号-->
    <select id="queryRecordCodeBySapRecordCode" resultType="java.lang.String">
        select  distinct  record_code from sc_warehouse_record
        where is_deleted = 0 AND is_available = 1
        and business_type=2 and record_status=12 and   sap_order_code = #{sapOrderCode}
        order by create_time desc
        limit 1
    </select>

    <select id="queryRecordCodeBySapRecordCodeV2" resultType="java.lang.String">
        select  distinct  record_code from sc_warehouse_record
        where is_deleted = 0 AND is_available = 1
        and  record_status in (12,11)and   sap_order_code = #{sapOrderCode}
        order by create_time desc
        limit 1
    </select>

    <!-- 修改wms下发状态为已回撤 -->
    <update id="updateWmsStatusToBackCancle">
        <bind name="beforeSyncWmsStatus1" value="@com.rome.stock.core.constant.WmsSyncStatusVO@UNSYNCHRONIZED"/>
        <bind name="beforeSyncWmsStatus2" value="@com.rome.stock.core.constant.WmsSyncStatusVO@SYNCHRONIZED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@BACKCANCLE"/>
        update
        sc_warehouse_record
        set
        sync_wms_status = #{afterSyncWmsStatus.status},
        modifier = #{userId}
        where
        id = #{id}
        and sync_wms_status in(#{beforeSyncWmsStatus1.status} , #{beforeSyncWmsStatus2.status})
        and record_status !=2
    </update>

    <!-- 更新出库单状态为已取消，且wms同步状态必须为已回撤 .0617sql改为不加前置状态校验，兼容旺店通的撤单,0709改为转给旺店通取消订单失败后的回滚用-->
    <update id="updateToCanceledById">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="beforSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@BACKCANCLE"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status} ,
        `sync_wms_status` = #{afterSyncWmsStatus.status}, version_no = version_no + 1
        where id = #{id}
    </update>



    <update id="updateRecordWmsSyncStatus">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@UNSYNCHRONIZED"/>
        update
        `sc_warehouse_record`
        set
        `sync_wms_status` = #{afterRecordStatus.status}
        where
        `id` = #{id}
        and `sync_wms_status` = #{beforeRecordStatus.status}
    </update>


    <sql id="wdtQueryWhere">
        <if test="userCode!=null and '' !=userCode">
            and record.user_code = #{userCode}
        </if>
        <!--<if test="recordCode!=null and '' !=recordCode">-->
            <!--and record.record_code = #{recordCode}-->
        <!--</if>-->

        <if test="realWarehouseId!=null ">
            and record.real_warehouse_id = #{realWarehouseId}
        </if>
        <if test="realWarehouseIdList!=null and realWarehouseIdList.size() > 0">
            and record.real_warehouse_id in
            <foreach collection="realWarehouseIdList" separator="," item="item" open="(" close=")">
              #{item}
            </foreach>
        </if>

        <if test="recordStatus!=null ">
            and record.record_status = #{recordStatus}
        </if>
        <if test="startTime!=null ">
            and record.create_time &gt;= #{startTime}
        </if>
        <if test="endTime!=null ">
            and record.create_time &lt;= #{endTime}
        </if>
        <if test="null != startPayTime and null != endPayTime">
            and pay_time between #{startPayTime} and #{endPayTime}
        </if>
        <if test="null != startOutOrInTime and null != endOutOrInTime">
            and out_or_in_time between #{startOutOrInTime} and #{endOutOrInTime}
        </if>
        <!--<if test="syncWmsStatus!=null ">-->
            <!--and record.sync_wms_status = #{syncWmsStatus}-->
        <!--</if>-->
        <if test="syncFulfillmentStatus!=null ">
            and record.sync_fulfillment_status = #{syncFulfillmentStatus}
        </if>
        <if test="recordCodeList != null and recordCodeList.size()>0 ">
            and record.record_code in
            <foreach item="item" index="index" collection="recordCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="channelCodeList != null and channelCodeList.size()>0 ">
            and record.channel_code in
            <foreach item="item" index="index" collection="channelCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="warehouseRecordIds != null and warehouseRecordIds.size()>0">
            and record.id in
            <foreach item="item" index="index" collection="warehouseRecordIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="syncWmsStatusList != null and syncWmsStatusList.size()>0">
            and record.sync_wms_status in
            <foreach item="item" index="index" collection="syncWmsStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="skuCode!=null and '' !=skuCode">
            and exists (select detail.record_code
            from sc_warehouse_record_detail detail
            where record.record_code = detail.record_code and detail.is_deleted=0
            and detail.sku_code=#{skuCode})
        </if>
        order by record.create_time desc ,record.id desc
        <if test="null !=pIndex and null !=num">
            limit #{pIndex},#{num}
        </if>
    </sql>

  <!--修改派车单号 -->
    <update id="updateTmsRecordCodeByRecordCode">
            update
            sc_warehouse_record
            set
            tms_record_code = #{expressCode}
            where
            record_code = #{recordCode}
            AND record_status not in (2,11)
            and is_available = 1
            and is_deleted = 0
    </update>

    <!--分页查询待同步收货信息给销售中心的单子：已同步合单并且已出库回调-->
    <select id="getWaitSyncDeliveryToGroupPurchase" resultType="java.lang.Long">
        select id
        from sc_warehouse_record
        where sync_fulfillment_status = 10  <!--  0-无需同步 1-待通知履单 2-已通知履单  10-待通知销售中心 20 已通知销售中心 -->
        and is_available = 1
        and is_deleted = 0
        and create_time > DATE_SUB(NOW(),INTERVAL 15 DAY)
        limit #{page},#{maxResult}
    </select>

    <!--团购仓出库明细-->
    <select id="queryGroupWarehouseRecordDetailTemplateList" resultType="com.rome.stock.core.api.dto.groupbuy.GroupWarehouseRecordDetailTemplate" parameterType="com.rome.stock.core.api.dto.qry.GroupWarehouseRecordCondition">
        select record_code
        from sc_warehouse_record_detail record
        where record.is_available = 1 and record.is_deleted = 0
        <if test="warehouseRecordCodes != null and warehouseRecordCodes.size()>0">
            and record.record_code in
            <foreach item="item" index="index" collection="warehouseRecordCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <update id="reservationReturninWarehouse" parameterType="long">
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@IN_ALLOCATION"/>
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@ INIT"/>
      update
       sc_warehouse_record
      set
        record_status =  #{afterRecordStatus.status},
        sync_fulfillment_status = 10,
        modifier = #{userId}
      where
        id = #{id}
        and
        record_status = #{beforeRecordStatus.status}
        and
        sync_fulfillment_status = 0
    </update>

    <select id="getWaitWhAllocationNotify" resultType="java.lang.String">
        select record_code
        from sc_warehouse_record
        where sync_trade_status = 1  <!--  0-无需同步 1-待通知订单中心 2-已通知订单中心-->
        and record_status = 11
        and is_available = 1
        and is_deleted = 0
        and business_type = 1
        and update_time &gt;= DATE_SUB(CURDATE(),INTERVAL 15 DAY)
    </select>

    <select id="queryWarehouseRecordByCodeList" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select <include refid="BASE_COLUMN" /> from sc_warehouse_record
        where
         is_available = 1
        and is_deleted = 0
        and record_code in
        <foreach collection="recordCodeList" item="recordCode" open="(" close=")" separator=",">
            #{recordCode}
        </foreach>

    </select>

    <update id="updateSyncOrderStatus">
        update `sc_warehouse_record`
        set `sync_trade_status` = 2
        where record_code = #{recordCode}
        and sync_trade_status=1
    </update>

    <update id="initSyncOrderStatus">
        update `sc_warehouse_record`
        set `sync_trade_status` = 0
        where record_code = #{recordCode}
          and sync_trade_status= 1
    </update>

    <select id="getWaitReturnNotifyToCmp" resultType="java.lang.String">
        select record_code
        from sc_warehouse_record
        where cmp_status = 1
        and business_type =2
        and record_status = 12
        and is_available = 1
        and is_deleted = 0
        and record_type  in(
        115,117,23,27)
        and create_time >= DATE_SUB(CURDATE(),INTERVAL 15 DAY)
    </select>

    <!--查询待推送的预约退货确认单接-->
   <select id="getWaitReturnReceiveToCmp" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
         select record_code,record_type
        from sc_warehouse_record
        where cmp_status = 10  <!--cmp7确认退货标识-->
        and business_type =1
        and record_status = 11
        and is_available = 1
        and is_deleted = 0
        and record_type  in(
        114,116,22,26,24)
        and create_time >= DATE_SUB(CURDATE(),INTERVAL 15 DAY)
    </select>

    <!--查询待推送cmp7的退货单-->
    <select id="getWaitReturnNotifyToCmp7" resultType="java.lang.String">
            select record_code
        from sc_warehouse_record
        where sync_dispatch_status = 10
        and business_type =2
        and record_status = 12
        and is_available = 1
        and is_deleted = 0
        and record_type  in(
        115,117,23,27)
        and create_time >= DATE_SUB(CURDATE(),INTERVAL 15 DAY)
    </select>

    <!--查询待推送采购中心的退供订单-->
    <select id="selectWaitNotifyToPurchase" resultMap="WarehouseRecordMap" >
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0 and sync_purchase_status = 1
        and  record_type in
        <foreach collection="recordTypeList" item="recordType" open="(" close=")" separator=",">
            #{recordType}
        </foreach>
        and `create_time` >  DATE_SUB(CURRENT_TIMESTAMP,INTERVAL 30 DAY)
        limit #{page},#{maxResult}
    </select>


       <!--查询收货完成且未推送采购中心质检完成的数据-->
       <select id="selectWaitQualityToPurchase" resultMap="WarehouseRecordMap" >
        select    w.record_code,w.sap_order_code
        from sc_warehouse_record  as w
        left join (select p.record_code from sc_rw_batch p where business_type=2
        and p.sync_purchase_status in(0,1) and p.is_deleted=0 and p.create_time>DATE_SUB(CURRENT_TIMESTAMP,INTERVAL 30 DAY)) as r
        on w.record_code=r.record_code
        where  w.sync_fulfillment_status=30
        and w. `create_time` >  DATE_SUB(CURRENT_TIMESTAMP,INTERVAL 30 DAY)
        and w.sync_purchase_status = 1
        and w.record_status=12
        and  w.record_type in
        <foreach collection="recordTypeList" item="recordType" open="(" close=")" separator=",">
            #{recordType}
        </foreach>
        and  w.is_deleted = 0
        and r.record_code is null
        order by w.sync_fulfillment_status,w. create_time
        limit #{page},#{maxResult}
    </select>


   <!--更新待推送cmp5/6及cmp7-->
    <update id="updateCmpStatusNeedPush">
        update `sc_warehouse_record`
        set `cmp_status` = 1,sync_dispatch_status=10
        where id = #{id}
        and cmp_status=0
    </update>
    
    <!--查询根据单据类型和小于指定的时间,主要用在冷热数据迁移-->
    <select id="selectWarehouseRecordByTypeEndTime" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where
        record_type=#{recordType} and create_time  <![CDATA[<= ]]> #{endTime} 
        and `is_deleted` = 0 and `is_available` = 1 limit #{pageSize}
    </select>
    <update id="updateActualQtyById">
        update `sc_warehouse_record_detail`
        set `actual_qty` = actual_qty+#{actualQty}
        where id = #{id} and plan_qty &gt; actual_qty
    </update>

    <update id="updateActualQtyByDetailId">
        update `sc_warehouse_record_detail`
        set `actual_qty` = #{actualQty}
        where id = #{id} and actual_qty&gt;0
    </update>


    <update id="updatePlanQtyByDetailId">
        update `sc_warehouse_record_detail`
        set `plan_qty` = #{planQty}
        where id = #{id}
    </update>

    <update id="batchUpdatePlanQtyByDetailId" parameterType="java.util.List">
        update `sc_warehouse_record_detail`
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plan_qty = case" suffix="end,">
                <foreach collection="detailList" item="item" index="index">
                    when id=#{item.id} then  #{item.planQty,jdbcType=DECIMAL}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="detailList" item="item1" open="(" close=")" separator=",">
            #{item1.id}
        </foreach>
    </update>

    <update id="batchUpdateDetailActualQtyByRecordCode">
      update sc_warehouse_record_detail
        set actual_qty = 0
        where record_code = #{recordCode}
    </update>
    <update id="updateActualQtyByPlanQty">
      update sc_warehouse_record_detail
        set actual_qty = plan_qty
        where record_code=#{recordCode}
    </update>
    <!--更新过账状态为待过账-->
    <update id="updateTransferFailure" parameterType="java.lang.String">
        update sc_warehouse_record
        set sync_transfer_status = 1
        where record_code = #{recordCode}
        and sync_transfer_status = 2
    </update>

    <update id="updateToCanceledFromCompleteIn">
        <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@IN_ALLOCATION"/>
        <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WarehouseRecordStatusVO@DISABLED"/>
        <bind name="afterSyncWmsStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
        update `sc_warehouse_record`
        set `record_status` = #{afterRecordStatus.status},
        `sync_wms_status` = #{afterSyncWmsStatus.status}
        where id = #{id}
        and record_status=#{beforeRecordStatus.status}
    </update>

    <!--查询根据大于指定的Id和创建时间的单据列表,主要用在单据在其他pool是否存在-->
    <select id="queryWarehouseRecordByGtId" resultMap="WarehouseRecordMap">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
         where id  <![CDATA[> ]]> #{id} AND create_time <![CDATA[<= ]]> #{endTime}
        and record_type in
        <foreach item="item" index="index" collection="recordTypes" open="(" separator="," close=")">
                #{item}
        </foreach>
        and `is_deleted` = 0 and `is_available` = 1 ORDER BY id asc limit #{pageSize}
    </select>

    <!--根据页面查询条件查询出库单-->
    <select id="queryOdyWarehouseRecordList" resultMap="WarehouseRecordMap"
            parameterType="com.rome.stock.core.api.dto.qry.SaleWarehouseRecordCondition">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record record
        where record.is_deleted = 0
        and record.business_type = 1
        and record.record_type in (99, 100) <!-- 1门店零售 46电商零售-->
        <include refid="wdtQueryWhere"/>
    </select>
    <select id="countUnSyncReceipt" resultType="java.lang.Integer">
        select count(1) from sc_receipt_record
        where warehouse_record_code=#{recordCode}
        and sync_order_status=1
        and is_deleted=0 and is_available=1
    </select>
    <select id="countReceiptByRecordCode" resultType="java.lang.Integer">
        select count(1) from sc_receipt_record
        where warehouse_record_code=#{recordCode}
        and is_deleted=0 and is_available=1
    </select>
    <select id="countUnCompleteSkuDetail" resultType="java.lang.Integer">
        select count(1) from sc_warehouse_record_detail
        WHERE record_code=#{recordCode} and plan_qty>actual_qty
		and is_deleted=0 and is_available=1
    </select>
    <select id="countShopRetailBySapOrderCode" resultType="java.lang.Integer">
        select count(1) from sc_warehouse_record
        where  sap_order_code=#{sapOrderCode}
        and record_type in (5,6)
    </select>
    <select id="selectUnOutDetail" resultType="java.lang.Integer">
        select count(1)
        from sc_warehouse_record_detail
        where  record_code=#{recordCode}
        and plan_qty > actual_qty
        and is_deleted=0 and is_available=1
    </select>

    <update id="updateVersionNo">
        update sc_warehouse_record
        set version_no = version_no + 1
        where record_code=#{recordCode} and version_no=#{versionNo}
        and is_deleted=0 and is_available=1
    </update>
    
    <!-- 物理删除后置单，根据单据列表 -->
    <delete id="deleteWarehouseRecordByRecordCodes">
        delete from sc_warehouse_record
        where  record_code in
        <foreach item="item" index="index" collection="deleteList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    
    <!-- 物理删除后置单明细，根据单据列表 -->
    <delete id="deleteWarehouseRecordDetailByRecordCodes">
        delete from sc_warehouse_record_detail
        where  record_code in
        <foreach item="item" index="index" collection="deleteList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 逻辑删除后置单，根据单据列表 -->
    <update id="updateDeleteWarehouseRecordByRecordCodes">
        update sc_warehouse_record set is_deleted=1,record_status=2,is_available=0
        where  record_code in
        <foreach item="item" index="index" collection="deleteList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    
    <!-- 逻辑删除后置单明细，根据单据列表 -->
    <update id="updateDeleteWarehouseRecordDetailByRecordCodes">
        update sc_warehouse_record_detail set is_deleted=1,is_available=0
        where  record_code in
        <foreach item="item" index="index" collection="deleteList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateVirtualWarehouseIdById">
        update sc_warehouse_record set virtual_warehouse_id = #{virtualWarehouseId}
        where id=#{id}
    </update>
    <update id="updateConfigurationToCancel">
        update sc_warehouse_record
        set record_status =#{afterStatus}
        where id =#{id} and record_status =#{beforeStatus}
    </update>
    <update id="updateRecordWmsSyncStatusByRecordCode">
            <bind name="beforeRecordStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@NO_REQUIRED"/>
            <bind name="afterRecordStatus" value="@com.rome.stock.core.constant.WmsSyncStatusVO@UNSYNCHRONIZED"/>
            update
            `sc_warehouse_record`
            set
            `sync_wms_status` = #{afterRecordStatus.status}
            where
            `record_code` = #{recordCode}
            and `sync_wms_status` = #{beforeRecordStatus.status}
    </update>
    <update id="updateSpecialTag">
        update sc_warehouse_record
        set self_takeout=#{selfTakeOut}
        where record_code =#{recordCode}
    </update>

    <update id="updateSyncOrderStatusSuccess">
        update `sc_warehouse_record`
        set `sync_trade_status` = 2
        where record_code = #{recordCode}
          and sync_trade_status in(0,1)
    </update>

    <!--根据仓库ID，查询调拨在途库存 -->
    <select id="queryOnRoadStockByRealWarehouseId" resultType="com.rome.stock.core.api.dto.frontrecord.SkuStock">
        SELECT
               sum(f.onRoadQty) onroadQty,
               f.skuCode,f.skuId,f.unitCode,f.real_warehouse_id
        from (SELECT
                 qty-ifnull(actual_qty,0) onRoadQty,
                 a.skuCode,a.skuId,a.unitCode,
                 a.sapOrderCode,
                 a.record_code,a.real_warehouse_id
              FROM
                   (
                   SELECT
                          sum( d.plan_qty - d.actual_qty ) qty,
                          d.sku_code skuCode,d.sku_id skuId,d.unit_code unitCode,
                          w.sap_order_code sapOrderCode,
                          w.record_code,
                          w.real_warehouse_id
                   FROM
                        sc_warehouse_record_detail d LEFT JOIN sc_warehouse_record w ON w.record_code = d.record_code
                   WHERE d.plan_qty > d.actual_qty AND w.record_type = 30 AND record_status != 2  and w.real_warehouse_id=#{realWarehouseId}
                    AND d.sku_code in
                    <foreach item="item" index="index" collection="skuCodeList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                   GROUP BY d.sku_code, w.sap_order_code,w.record_code,w.real_warehouse_id
                   ) a
                       LEFT JOIN
                           (SELECT
                                   sum(d.actual_qty) actual_qty,
                                   w.sap_order_code,
                                   d.sku_code
                            FROM
                                 sc_warehouse_record_detail d LEFT JOIN sc_warehouse_record w ON d.record_code = w.record_code
                            WHERE record_type IN ( 102, 103, 104, 105 ) AND record_status != 2
                            AND d.sku_code in
                            <foreach item="item" index="index" collection="skuCodeList" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            GROUP BY d.sku_code, w.sap_order_code
                           ) c ON c.sap_order_code = a.sapOrderCode AND c.sku_code = a.skuCode)  f
        WHERE f.onRoadQty>0
        group by f.skuCode,f.real_warehouse_id
    </select>
    <select id="queryInWarehouseBySapCodeAndRecordTypeList" resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where is_deleted = 0
        and sap_order_code = #{sapOrderCode}
        and business_type = 2
        and record_type in
        <foreach item="item" index="index" collection="recordTypeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 1
    </select>
    <select id="queryEntryRecordBySapOrderCode"
            resultType="com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo">
        select
        <include refid="BASE_COLUMN"/>
        from sc_warehouse_record
        where
        sap_order_code=#{sapOrderCode} and business_type=2
        and `is_deleted` = 0 and `is_available` = 1 limit 1
    </select>
    <select id="queryReceiveRecordCodeByPackageCode" resultType="java.lang.String">
        SELECT d.out_record_code from sc_warehouse_record w
        INNER JOIN sc_front_warehouse_record_relation r on r.record_code=w.record_code
        INNER JOIN sc_fr_inventory_adjust d on r.front_record_code=d.record_code
        WHERE w.record_code=#{recordCode}
          and w.is_available=1 and w.is_deleted=0
          and r.is_available=1 and r.is_deleted=0
          and d.is_available=1 and d.is_deleted=0
    </select>
</mapper>
