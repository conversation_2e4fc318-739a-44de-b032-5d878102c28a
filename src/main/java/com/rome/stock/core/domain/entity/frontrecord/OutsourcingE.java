package com.rome.stock.core.domain.entity.frontrecord;


import com.rome.stock.core.common.AlikAssert;
import com.rome.stock.core.constant.FrontRecordTypeVO;
import com.rome.stock.core.domain.repository.frontrecord.FrOutsourcingRepository;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Scope("prototype")
@Data
@EqualsAndHashCode(callSuper = false)
public class OutsourcingE extends AbstractFrontRecord {

	@Resource
	private FrOutsourcingRepository frOutsourcingRepository;

	/**
	 * 创建委外出库前置单
	 */
	public void addFrontRecord() {
		AlikAssert.notNull(this.getRecordType(), "999", "recordType为空");
		FrontRecordTypeVO frontRecordTypeVO = FrontRecordTypeVO.getFrontVOByType(this.getRecordType());
		AlikAssert.notNull(frontRecordTypeVO, "999", "recordType："+this.getRecordType()+"未配置枚举信息");
		//生成单据编号
		this.initFrontRecord(frontRecordTypeVO.getCode(), this.details);
		if (StringUtils.isBlank(this.getRemark())) {
			this.setRemark("");
		}
		//插入采购单据
		long id = frOutsourcingRepository.savePurchaseOrderRecord(this);
		this.setId(id);
		//前置单详情关联主数据
		this.details.forEach(detail -> detail.setFrontRecordDetail(this));
		//插入前置单单据详情
		frOutsourcingRepository.savePurchaseOrderRecordDetails(this.details);
	}

	/**
	 * 更新完成状态，
	 */
	public void updateOutAllocationStatus() {
		frOutsourcingRepository.updateOutAllocationStatus(this.getRecordCode());
	}


	/**
	 * 出向实体仓库id
	 */
	private Long realWarehouseId;

	/**
	 * 工产代码
	 */
	private String factoryCode;
	/**
	 * 工产名称
	 */
	private String factoryName;
	/**
	 * 供应商编码代码
	 */
	private String supplierCode;
	/**
	 * 供应商名称
	 */
	private String supplierName;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 供应商联系人
	 */
	private String supplierContact;

	/**
	 * 采购预约单号
	 */
	private String appointRecordCode;

	/**
	 * po采购单号
	 */
	private String purchaseOrderNo;


	private List<OutsourcingDetailE> details;
}
