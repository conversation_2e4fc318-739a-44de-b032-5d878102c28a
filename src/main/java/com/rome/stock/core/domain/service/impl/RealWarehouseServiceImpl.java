package com.rome.stock.core.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.warehouse.VirtualWarehouseTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseMaterialStorageTypeVO;
import com.rome.stock.core.api.dto.*;
import com.rome.stock.core.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.core.common.AlikAssert;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.common.RomeCollectionUtil;
import com.rome.stock.core.constant.RealWarehouseRankVO;
import com.rome.stock.core.constant.RealWarehouseTypeVO;
import com.rome.stock.core.domain.convertor.RealWarehouseAreaConvertor;
import com.rome.stock.core.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.core.domain.convertor.VirtualWarehouseConvertor;
import com.rome.stock.core.domain.entity.RealWarehouseE;
import com.rome.stock.core.domain.entity.RwRecordPoolE;
import com.rome.stock.core.domain.entity.VirtualWarehouseE;
import com.rome.stock.core.domain.entity.frontrecord.OnlineRetailE;
import com.rome.stock.core.domain.entity.frontrecord.WDTOnlineRetailE;
import com.rome.stock.core.domain.repository.*;
import com.rome.stock.core.domain.repository.frontrecord.FrSaleRepository;
import com.rome.stock.core.domain.repository.frontrecord.FrWDTSaleRepository;
import com.rome.stock.core.domain.repository.warehouserecord.KpRwRelationRepository;
import com.rome.stock.core.domain.service.RealWarehouseService;
import com.rome.stock.core.domain.service.VirtualWarehouseService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.infrastructure.dataobject.KpChannelVwTypeRelationDO;
import com.rome.stock.core.infrastructure.dataobject.KpRwRelationDo;
import com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO;
import com.rome.stock.core.infrastructure.dataobject.RealWarehouseWmsConfigDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.mapper.KpChannelVwTypeRelationMapper;
import com.rome.stock.core.infrastructure.mapper.RealWarehouseReturnConfigMapper;
import com.rome.stock.core.remote.base.dto.ChannelDTO;
import com.rome.stock.core.remote.base.dto.StoreDTO;
import com.rome.stock.core.remote.base.facade.ChannelFacade;
import com.rome.stock.core.remote.base.facade.ShopFacade;
import com.rome.stock.core.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.core.remote.item.facade.SkuFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RealWarehouseServiceImpl implements RealWarehouseService {

    @Autowired
    private EntityFactory entityFactory;
    @Autowired
    private RealWarehouseConvertor realWarehouseConvertor;
    @Autowired
    private RealWarehouseAreaConvertor realWarehouseAreaConvertor;
    @Autowired
    private RealWarehouseRepository realWarehouseRepository;
    @Autowired
    private VirtualWarehouseService virtualWarehouseService;
    @Autowired
    private SkuFacade skuFacade;
    @Autowired
    private ShopFacade shopFacade;
    @Resource
    private ZdeliverRelationRepository zdeliverRelationRepository;
    @Resource
    private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
    @Resource
    private ChannelFacade channelFacade;
    @Resource
    private RealWarehouseWmsConfigRepository realWarehouseWmsConfigRepository;
    @Resource
    private KpRwRelationRepository kpRwRelationRepository;
    @Resource
    private RealWarehouseAdditionRepository realWarehouseAdditionRepository;
    @Autowired
    private VirtualWarehouseConvertor virtualWarehouseConvertor;

    @Resource
    private KpChannelVwTypeRelationMapper kpChannelVwTypeRelationMapper;

    @Resource
    private VirtualWarehouseRepository virtualWarehouseRepository;

    @Resource
    private RealWarehouseReturnConfigMapper realWarehouseReturnConfigMapper;

    @Resource
    private FrWDTSaleRepository frWDTSaleRepository;
    @Resource
    private RwRecordPoolRepository rwRecordPoolRepository;
    @Resource
    private FrSaleRepository frSaleRepository;
    /**
     * 根据主键ID查询实仓信息
     *
     * @param id 主键ID
     * @return 实仓实体
     */
    @Override
    public RealWarehouse findByRealWarehouseId(Long id) {
        RealWarehouseE result = realWarehouseRepository.getRealWarehouseById(id);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据主键查询实仓信息
     * @param ids 主键
     * @return 实仓Dto
     */
    @Override
    public List<RealWarehouse> findByRealWarehouseIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<RealWarehouseE> result = realWarehouseRepository.queryWarehouseByIds(ids);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据仓库编码查询实仓信息
     *
     * @param code 仓库编码
     * @return 实仓实体
     */
    @Override
    public RealWarehouse findByRealWarehouseCode(String code) {
        RealWarehouseE result = realWarehouseRepository.getRealWarehouseByCode(code);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public RealWarehouse findByRealWarehouseOutCodeAndFactoryCode(String outCode, String factoryCode) {
        RealWarehouseE result = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(outCode, factoryCode);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public List<RealWarehouse> findByRealWarehouseOutCodeAndFactoryCodeList(List<QueryRealWarehouse> list){
        List<RealWarehouseE> result = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCodeList(list);
        return realWarehouseConvertor.entityToDto(result);
    }


    /**
     * 根据门店编码查询实仓信息
     *
     * @param shopCode 仓库编码
     * @return 实仓实体
     */
    @Override
    public RealWarehouse findByRealWarehouseShopCode(String shopCode) {
        List<RealWarehouseE> list = realWarehouseRepository.getRwListByShopCodes(Arrays.asList(shopCode));
        AlikAssert.isTrue(list != null && list.size() == 1, "999", "当前门店未绑定唯一的仓库");
        RealWarehouseE result = list.get(0);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据仓库id批量查询实仓信息
     * @param ids
     * @return
     */
    @Override
    public List<RealWarehouse> getRealWarehouseByIds(List<Long> ids){
        List<RealWarehouseE> list = realWarehouseRepository.getRealWarehouseByIds(ids);
        return realWarehouseConvertor.entityToDto(list);
    }

    @Override
    public List<RealWarehouse> findByRwListShopCodes(List<String> codes) {
        List<RealWarehouseE> list = realWarehouseRepository.getRwListByShopCodes(codes);
        return realWarehouseConvertor.entityToDto(list);
    }

    /**
     * 根据条件查询实仓信息
     *
     * @param paramDTO 查询条件参数Dto
     * @return 实仓Dto列表
     */
    @Override
    public PageInfo<RealWarehouse> findByRealWarehouseCondition(RealWarehouseParamDTO paramDTO) {
        if(StringUtils.isNotEmpty(paramDTO.getRealWarehouseCode())){
            String[]  realWarehouseCodeArr = paramDTO.getRealWarehouseCode().split("\\,");
            paramDTO.setRealWarehouseCodeList(Arrays.asList(realWarehouseCodeArr));
        }

        Page page = PageHelper.startPage(paramDTO.getPageIndex(), paramDTO.getPageSize());
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRealWarehouseByCondition(paramDTO);
        List<RealWarehouse> realWarehouseList = realWarehouseConvertor.entityToDto(realWarehouseEList);
        List<RealWarehouseWmsConfigDO> configDOList = new ArrayList<RealWarehouseWmsConfigDO>();
        //获取实仓ID集合
        List<Long> rwIds = realWarehouseList.stream().map(RealWarehouse :: getId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(rwIds)) {
        	configDOList = realWarehouseWmsConfigRepository.findWmsConfigByWarehouseIds(rwIds);
        }
        Map<Long, RealWarehouseWmsConfigDO> configDOMap = configDOList.stream().collect(Collectors.toMap(RealWarehouseWmsConfigDO :: getRealWarehouseId, Function.identity(), (v1, v2) -> v1));
        realWarehouseList.forEach(item -> {
        	if(configDOMap.containsKey(item.getId())) {
        		item.setHasConfig(1);
        	}else {
        		Integer realWarehouseType = item.getRealWarehouseType();
        		//仓库类型1非门店仓、15非虚拟物品仓、21非商家仓的仓库
        		if(!Integer.valueOf(1).equals(realWarehouseType) && !Integer.valueOf(15).equals(realWarehouseType) && !Integer.valueOf(21).equals(realWarehouseType)) {
        			item.setHasConfig(0);
        		}else {
        			item.setHasConfig(1);
        		}
        	}
        	//仓库类型名称
            if(null != RealWarehouseTypeVO.getTypeVOByType(item.getRealWarehouseType())){
                item.setRealWarehouseTypeName(RealWarehouseTypeVO.getTypeVOByType(item.getRealWarehouseType()).getDesc());
            }
            //仓库类型名称
            if(null != RealWarehouseRankVO.getTypeVOByRank(item.getRealWarehouseRank())){
                item.setRealWarehouseRankName(RealWarehouseRankVO.getTypeVOByRank(item.getRealWarehouseRank()).getDesc());
            }
        });
        PageInfo<RealWarehouse> pageInfo = new PageInfo<>(page.getResult());
        pageInfo.setList(realWarehouseList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    /**
     * 查询所有实仓信息
     *
     * @return 所有实仓信息列表
     */
    @Override
    public List<RealWarehouse> findRealWarehouseAllList() {
        List<RealWarehouseE> result = realWarehouseRepository.getRealWarehouseAllList();
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 新增实仓信息
     *
     * @param realWarehouseAddDTO 待新增实仓信息
     */
    @Override
    public Long addRealWarehouse(RealWarehouseAddDTO realWarehouseAddDTO) {
        //基本参数校验
        if (!validAddRealWarehouseDataInfo(realWarehouseAddDTO)) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        RealWarehouseTypeVO typeVO = RealWarehouseTypeVO.getTypeVOByType(realWarehouseAddDTO.getRealWarehouseType());
        if (null == typeVO) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        //检查是否存在相同编号的实仓信息
        RealWarehouseE resultRealWarehouseE = realWarehouseRepository.getRealWarehouseByCode(realWarehouseAddDTO.getRealWarehouseCode());
        if (null != resultRealWarehouseE) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC+"realWarehouseCode:"+realWarehouseAddDTO.getRealWarehouseCode()+"已存在");
        }
        if(realWarehouseAddDTO.getRealWarehouseStatus() == null) {
        	realWarehouseAddDTO.setRealWarehouseStatus(1);
        }
        if(realWarehouseAddDTO.getAllowNegtiveStock() == null) {
        	realWarehouseAddDTO.setAllowNegtiveStock(0);
        }
        RealWarehouseE realWarehouseE = realWarehouseConvertor.dtoToEntity(realWarehouseAddDTO);
        //非门店仓，门店编号都置为空
        if (!RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseE.getRealWarehouseType())) {
            realWarehouseE.setShopCode(null);
        }
        realWarehouseE.setCreator(realWarehouseAddDTO.getUserId());
        boolean executeResult = realWarehouseRepository.saveRealWarehouse(realWarehouseE);
        if (!executeResult) {
            throw new RomeException(ResCode.STOCK_ERROR_1004, ResCode.STOCK_ERROR_1004_DESC);
        }
        //查询实仓类型配置
        String realWarehouseTypeArrStr = BaseinfoConfiguration.getInstance().get("realwarhouse", "kpRelationTypeArr");
        List<Integer> realWarehouseTypeArr = JSON.parseArray(realWarehouseTypeArrStr, Integer.class);
        //设置鲲鹏渠道关联
        if(realWarehouseAddDTO.getRealWarehouseRank() != null &&
                realWarehouseTypeArr.contains(realWarehouseAddDTO.getRealWarehouseType())){
            executeResult = kpRwRelationRepository.saveRelation(realWarehouseE);
            if (!executeResult) {
                throw new RomeException(ResCode.STOCK_ERROR_1092, ResCode.STOCK_ERROR_1092_DESC);
            }
        }
        return realWarehouseE.getId();
    }

    /**
     * 修改实仓信息
     *
     * @param realWarehouse 待修改实仓信息实体
     */
    @Override
    public void updateRealWarehouse(RealWarehouse realWarehouse) {
        //基本参数校验
        if (!validUpdateRealWarehouseDataInfo(realWarehouse)) {
            throw new RomeException(ResCode.STOCK_ERROR_1005, ResCode.STOCK_ERROR_1005_DESC);
        }
        RealWarehouseTypeVO typeVO = RealWarehouseTypeVO.getTypeVOByType(realWarehouse.getRealWarehouseType());
        if (null == typeVO) {
            throw new RomeException(ResCode.STOCK_ERROR_1005, ResCode.STOCK_ERROR_1005_DESC);
        }
        RealWarehouseE realWarehouseE = realWarehouseConvertor.dtoToEntity(realWarehouse);
        realWarehouseE.setModifier(realWarehouse.getUserId());
        //非门店仓，门店编号都置为空
        if (!RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouse.getRealWarehouseType())) {
            realWarehouseE.setShopCode(null);
        }
        boolean executeResult = realWarehouseRepository.updateRealWarehouseByWhere(realWarehouseE);
        if (!executeResult) {
            throw new RomeException(ResCode.STOCK_ERROR_1005, ResCode.STOCK_ERROR_1005_DESC);
        }
        //查询实仓类型配置
        String realWarehouseTypeArrStr = BaseinfoConfiguration.getInstance().get("realwarhouse", "kpRelationTypeArr");
        List<Integer> realWarehouseTypeArr = JSON.parseArray(realWarehouseTypeArrStr, Integer.class);
        if(realWarehouse.getRealWarehouseRank() != null &&
                realWarehouseTypeArr.contains(realWarehouse.getRealWarehouseType())){
            this.createKpRwRelation(realWarehouseE);
        } else {
            kpRwRelationRepository.deleteByRealWarehouseId(realWarehouse.getId());
        }
    }

    /**
     * 创建鲲鹏关联关系
     * @param realWarehouseE
     */
    private void createKpRwRelation(RealWarehouseE realWarehouseE) {
        //判断是否存在.存在直接返回
        KpRwRelationDo kpRwRelationDo = kpRwRelationRepository.getByRealWarehouseId(realWarehouseE.getId());
        if (kpRwRelationDo != null) {
          return;
        }
        kpRwRelationRepository.saveRelation(realWarehouseE);
    }

    /**
     * 启用实仓
     *
     * @param realWarehouseId 主键ID
     */
    @Override
    public void enableRealWarehouse(Long realWarehouseId, Long userId) {
        realWarehouseRepository.updateRealWarehouseStatusEnable(realWarehouseId, userId);
    }

    /**
     * 停用实仓
     *
     * @param realWarehouseId 主键ID
     */
    @Override
    public void disableRealWarehouse(Long realWarehouseId, Long userId) {
        realWarehouseRepository.updateRealWarehouseStatusDisable(realWarehouseId, userId);
    }

    /**
     * 实仓分配虚仓
     *
     * @param paramDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotVirtualWarehouse(RealWarehouseParamDTO paramDTO) {
//		virtualWarehouseService.relateRealWarehouse(paramDTO.getVirtualWarehouseIdList(),
//				Long.parseLong(paramDTO.getModifier()), realWarehouseId);
        CoreRealStockOpDO stockDO = null;
        boolean isSuccess = false;
        try {
            for (VWIdSyncRateDTO dto : paramDTO.getVwIdSyncRateDTOList()) {
                virtualWarehouseService.relateRealWarehouse(dto.getId(), paramDTO.getUserId(),
                        paramDTO.getRealWarehouseId(), dto.getSyncRate());
            }
            //包装参数
            stockDO = new CoreRealStockOpDO();
            CoreRealStockOpDetailDO detailDO = new CoreRealStockOpDetailDO();
            detailDO.setRealWarehouseId(paramDTO.getRealWarehouseId());
            List<CoreRealStockOpDetailDO> detailDos = Arrays.asList(detailDO);
            stockDO.setDetailDos(detailDos);
            coreRealWarehouseStockRepository.updateVirtualSyncRate(stockDO);
            isSuccess = true;
        } catch (Exception e) {
            throw new RomeException(ResCode.STOCK_ERROR_1008, ResCode.STOCK_ERROR_1008_DESC);
        } finally {
            if (!isSuccess) {
                if (stockDO != null) {
                    RedisRollBackFacade.redisRollBack(stockDO);
                }
            }
        }
    }

    /**
     * 根据实仓主键ID查询实仓覆盖范围
     *
     * @param realWarehouse
     * @return
     */
    @Override
    public List<RealWarehouseArea> findAreaListByRealWarehouseId(Long realWarehouse) {
        List<RealWarehouseE> result = realWarehouseRepository.queryAreaListByRealWarehouseId(realWarehouse);
        return realWarehouseAreaConvertor.entityToDto(result);
    }

    /**
     * 分配实仓覆盖区域
     *
     * @param realWarehouseId 实仓主键ID
     * @param paramDTO        覆盖区域实体
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void allotRealWarehouseArea(Long realWarehouseId, RealWarehouseParamDTO paramDTO) {
        for (RealWarehouseAreaAddDTO area : paramDTO.getRealWarehouseAreaAddDTOList()) {
            area.setRealWarehouseId(realWarehouseId);
        }
        realWarehouseRepository.deleteRealWarehouseAreaByRWId(realWarehouseId, paramDTO.getUserId());
        if (null != paramDTO.getRealWarehouseAreaAddDTOList() && !paramDTO.getRealWarehouseAreaAddDTOList().isEmpty()) {
            for (RealWarehouseAreaAddDTO areaAddDTO : paramDTO.getRealWarehouseAreaAddDTOList()) {
                areaAddDTO.setCreator(paramDTO.getUserId());
            }
        }
        boolean executeResult = realWarehouseRepository.insertRealWarehouseArea(paramDTO.getRealWarehouseAreaAddDTOList());
        if (!executeResult) {
            throw new RomeException(ResCode.STOCK_ERROR_1009, ResCode.STOCK_ERROR_1009_DESC);
        }
    }

    /**
     * 实体仓库实例信息校验
     *
     * @param realWarehouse
     * @return 校验结果
     */
    private boolean validUpdateRealWarehouseDataInfo(RealWarehouse realWarehouse) {
        if (null == realWarehouse ||
                null == realWarehouse.getId() || 0 == realWarehouse.getId()) {
            return false;
        }
        if (StringUtils.isBlank(realWarehouse.getFactoryCode()) ||
                StringUtils.isBlank(realWarehouse.getRealWarehouseName()) ||
                StringUtils.isBlank(realWarehouse.getRealWarehouseAddress()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCountryCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseProvinceCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCityCode()) ||
                null == realWarehouse.getRealWarehouseType()) {
            return false;
        }
        if (RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouse.getRealWarehouseType())) {
            if (StringUtils.isBlank(realWarehouse.getShopCode())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 实体仓库基本信息校验
     *
     * @param realWarehouseAddDTO
     * @return 校验结果
     */
    private boolean validAddRealWarehouseDataInfo(RealWarehouseAddDTO realWarehouseAddDTO) {
        if (realWarehouseAddDTO == null ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCode()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseOutCode()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getFactoryCode()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseName()) ||
                StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseAddress()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCountryCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseProvinceCode()) ||
//				StringUtils.isBlank(realWarehouseAddDTO.getRealWarehouseCityCode()) ||
                null == realWarehouseAddDTO.getRealWarehouseType()
                ) {
            return false;
        }
        if (RealWarehouseTypeVO.RW_TYPE_1.getType().equals(realWarehouseAddDTO.getRealWarehouseType())) {
            if (StringUtils.isBlank(realWarehouseAddDTO.getShopCode())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<RealWarehouse> findListByRealWarehouseCode(List<String> codes) {
        if(CollectionUtils.isEmpty(codes)){
            return new ArrayList<>();
        }
        List<RealWarehouseE> result = realWarehouseRepository.getRealWarehousesByCode(codes);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public RealWarehouse findByRwCodeAndFactoryCode(String realWarehouseCode, String factoryCode) {
        RealWarehouseE result = realWarehouseRepository.getByRwCodeAndFactoryCode(realWarehouseCode, factoryCode);
        return realWarehouseConvertor.entityToDto(result);
    }

    @Override
    public List<RealWarehouse> queryWhByConditionForAdmin(RealWarehouseParamDTO paramDTO) {
        AlikAssert.isNotNull(paramDTO, ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC);
        List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRealWarehouseByCondition(paramDTO);
        return realWarehouseConvertor.entityToDto(realWarehouseEList);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCode(String factoryCode) {
        List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(factoryCode);
        return list;
    }

    /**
     * 根据工厂code查询仓库-非门店
     *
     * @param factoryCode
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeNoShop(String factoryCode) {
        //根据类型判断设置仓库类型
        Map<Integer, String> realWarehouseTypeList = RealWarehouseTypeVO.getRealWarehouseTypeList();
        realWarehouseTypeList.remove(RealWarehouseTypeVO.RW_TYPE_1.getType());
        Set<Integer> typeSet = realWarehouseTypeList.keySet();
        List<Integer> types = new ArrayList<>(typeSet);
        return queryRealWarehouseByFactoryCodeAndRWType(factoryCode, types);
    }

    /**
     * 查询非门店仓
     *
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseNoShop() {
        //根据类型判断设置仓库类型
        Map<Integer, String> realWarehouseTypeList = RealWarehouseTypeVO.getRealWarehouseTypeList();
        realWarehouseTypeList.remove(RealWarehouseTypeVO.RW_TYPE_1.getType());
        Set<Integer> typeSet = realWarehouseTypeList.keySet();
        List<Integer> types = new ArrayList<>(typeSet);
        return getRealWarehouseFactory(types);
    }

    @Override
    public List<RealWarehouse> queryRWByCondition(QueryAreaRWarehouse queryRealWarehouse) {
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRWByCondition(queryRealWarehouse);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    /**
     * 根据门店编码和仓库类型查询仓库，必须要求指定类型的仓库在对应的工厂下是唯一的
     *
     * @param shopCode        门店编码
     * @param warehouseTypeVO 仓库类型
     * @return
     */
    @Override
    public RealWarehouse queryRealWarehouseByShopCodeAndType(String shopCode, RealWarehouseTypeVO warehouseTypeVO) {
        //if (!warehouseTypeVO.getUnique()) {
        //	throw new RomeException(ResCode.STOCK_ERROR_1046,ResCode.STOCK_ERROR_1046_DESC);
        //}
        StoreDTO storeDTO = shopFacade.searchByCode(shopCode);
        List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(storeDTO.getDeliveryFactory());
        for (RealWarehouse warehouse : list) {
            if (warehouseTypeVO.getType().equals(warehouse.getRealWarehouseType())) {
                return warehouse;
            }
        }
        return null;
    }

    /**
     * 根据门店号以及是否加盟查询转置仓
     * @param shopCode
     * @param isJoin 是否加盟门店，对于加盟门店的话，配置表必须要有，对于直营的，不一定有配置
     * @return
     */
    @Override
    public RealWarehouse queryRealWarehouseByShopCodeAndTypeForJoinConvert(String shopCode,boolean isJoin) {
        StoreDTO storeDTO = shopFacade.searchByCode(shopCode);
        String deliverFactory = zdeliverRelationRepository.getDeliverFactory(storeDTO.getDeliveryFactory());
        if (!isJoin && StringUtils.isBlank(deliverFactory)) {
            //如果是直营的话，在配置表无数据的，就用商品返回的工厂即可
            deliverFactory = storeDTO.getDeliveryFactory();
        }
        if (StringUtils.isNotBlank(deliverFactory)) {
            List<RealWarehouse> list = realWarehouseRepository.queryRealWarehouseByFactoryCode(deliverFactory);
            for (RealWarehouse warehouse : list) {
                if (RealWarehouseTypeVO.RW_TYPE_24.getType().equals(warehouse.getRealWarehouseType())) {
                    return warehouse;
                }
            }
        }
        return null;
    }


    /**
     * 获取加盟门店的退货仓库
     *
     * @param shopCode
     * @return
     */
    @Override
    public RealWarehouse getJoinReturnWarehouse(String shopCode) {
        RealWarehouse realWarehouse = null;
        StoreDTO storeDTO = shopFacade.searchByCode(shopCode);
        //获取加盟门店的z工厂
        if (storeDTO == null) {
           return realWarehouse;
        }
        return realWarehouseRepository.querySingleRealWarehouseByFactoryCodeAndRwType(storeDTO.getActualWarehouse(), RealWarehouseTypeVO.RW_TYPE_12.getType());
    }

    /**
     * 根据仓库内部编号查询仓库信息
     *
     * @param code
     * @return
     */
    @Override
    public RealWarehouse queryRealWarehouseByInCode(String code) {
        RealWarehouseE result = realWarehouseRepository.queryRealWarehouseByInCode(code);
        return realWarehouseConvertor.entityToDto(result);
    }

    /**
     * 根据实仓id查询实仓的所有skuId
     *
     * @param realWarehouseId
     * @return
     */
    @Override
    public List<RealWarehouseStockDTO> querySkuIdByWhId(String realWarehouseId) {
        List<RealWarehouseStockDTO> stockList = realWarehouseRepository.querySkuIdByWhId(Long.parseLong(realWarehouseId));
        //遍历集合根据skuId获取skuName和skuCode
        List<Long> skuIds = RomeCollectionUtil.getValueList(stockList, "skuId");
        List<SkuInfoExtDTO> skuInfoExtDTOS = skuFacade.skusBySkuId(skuIds);
        Map<Long, SkuInfoExtDTO> skuInfoExtDTOMap = RomeCollectionUtil.listforMap(skuInfoExtDTOS, "id");

        for (RealWarehouseStockDTO dto : stockList) {
            if (skuInfoExtDTOMap.containsKey(dto.getSkuId())) {
                dto.setSkuCode(skuInfoExtDTOMap.get(dto.getSkuId()).getSkuCode());
                dto.setSkuName(skuInfoExtDTOMap.get(dto.getSkuId()).getName());
            }
        }
        return stockList;
    }

    /**
     * 根据仓库类型查询仓库
     *
     * @param types
     * @return
     */
    @Override
    public List<RealWarehouse> getRealWarehouseFactory(List<Integer> types) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.getRealWarehouseFactory(types);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    /**
     * 根据工厂code和仓库类型查询仓库信息
     *
     * @param factoryCode
     * @param types
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeAndRWType(String factoryCode, List<Integer> types) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryRealWarehouseByFactoryCodeAndRWType(factoryCode,null, types);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }

    /**
     * 根据仓库类型查仓库
     *
     * @param type
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehouseByRWType(Integer type) {
        List<RealWarehouseE> warehouseES = realWarehouseRepository.queryRealWarehouseByRWType(type);
        return realWarehouseConvertor.entityToDto(warehouseES);
    }


    /**
     * 根据批量工厂编码查询实仓信息
     *
     * @param factoryCodes
     * @return
     */
    @Override
    public List<RealWarehouse> queryRealWarehousesByFactoryCodes(List<String> factoryCodes) {
        if (factoryCodes == null || factoryCodes.size() == 0) {
            return new LinkedList<RealWarehouse>();
        }
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRealWarehousesByFactoryCodes(factoryCodes);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    @Override
    public RealWarehouse queryVirtualSkuRealWarehouse() {
        return realWarehouseConvertor.entityToDto(realWarehouseRepository.queryVirtualSkuRealWarehouse());
    }

	@Override
	public Map<String, String> queryDefaultRealWarehouseByShopCode(String shopCode) {
		List<RealWarehouseE> realWarehouseEList = realWarehouseRepository.getRwListByShopCodes(Arrays.asList(shopCode));
		if(CollectionUtils.isEmpty(realWarehouseEList)) {
			log.error("根据门店编码[{}]查询门店仓不存在", shopCode);
			throw new RomeException(ResCode.STOCK_ERROR_1010, ResCode.STOCK_ERROR_1010_DESC);
		}
		StoreDTO storeDTO = shopFacade.queryStoreByCode(shopCode);
		if(storeDTO == null) {
			log.error("根据门店编码[{}]查询门店不存在", shopCode);
			throw new RomeException(ResCode.STOCK_ERROR_1037, ResCode.STOCK_ERROR_1037_DESC);
		}
		Map<String, String> map = null;
		if("3".equals(storeDTO.getStoreProperties())) {//加盟
			if(StringUtils.isBlank(storeDTO.getFranchisee())) {
        		log.error("门店[{}]所属加盟商不存在", shopCode);
        		throw new RomeException(ResCode.STOCK_ERROR_4018, ResCode.STOCK_ERROR_4018_DESC);
        	}
        	//根据加盟商编码查询加盟商渠道
			List<ChannelDTO> channelDTOs = channelFacade.queryChannelByCodeAndType("120", storeDTO.getFranchisee(), "3");
			if(CollectionUtils.isEmpty(channelDTOs)) {
				log.error("根据加盟商编码[{}]查询加盟商渠道不存在", storeDTO.getFranchisee());
				throw new RomeException(ResCode.STOCK_ERROR_5028, ResCode.STOCK_ERROR_5028_DESC);
			}
			List<String> channelCodes = channelDTOs.stream().distinct().map(ChannelDTO :: getChannelCode).collect(Collectors.toList());
			map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelCodes.get(0));
			if(MapUtils.isEmpty(map)) {
				String channelCodeJM = storeDTO.getCode() + "_120";
				//根据公司编码查询加盟商渠道
				ChannelDTO channelDTO = channelFacade.queryChannelGroupByCode(channelCodeJM);
				if(channelDTO != null) {
					map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelDTO.getChannelCode());
				}
			}
        } else {//非加盟门店当作直营门店处理
        	String channelCodeZY = storeDTO.getCode() + "_119";
			map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelCodeZY);
			if(MapUtils.isEmpty(map)) {
				//根据公司编码查询直营渠道
				ChannelDTO channelDTO = channelFacade.queryChannelGroupByCode(channelCodeZY);
				if(channelDTO != null) {
					map = realWarehouseRepository.queryRealWarehouseByChannelCode(channelDTO.getChannelCode());
				}
			}
        }
		return map;
	}

	/**
	 * 查询实仓信息列表（排除门店）
	 */
	@Override
	public List<RealWarehouse> queryRWList(@Nullable String nameOrCode) {
		List<RealWarehouseE> result = realWarehouseRepository.queryRWList(nameOrCode);
        return realWarehouseConvertor.entityToDto(result);
	}

    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeAndType(String factoryCode, Integer type) {
	    if(StringUtils.isEmpty(factoryCode)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "工厂编号不能为空");
        }
        if(null == type){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库类型不能为空");
        }
	    int queryType=RealWarehouseTypeVO.RW_TYPE_14.getType();
	    //0.发货仓（不需要包装）；1.包装仓
	    if(type==1){
            queryType=RealWarehouseTypeVO.RW_TYPE_2.getType();
        }
        List<RealWarehouseE> warehouseEList=realWarehouseRepository.queryRealWarehouseByFactoryCodeAndRWType(factoryCode,null,Arrays.asList(queryType));
        return realWarehouseConvertor.entityToDto(warehouseEList);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByFactoryCodeAndRealWarehouseType(String factoryCode, String supplierCode,Integer type) {
        if(null == type){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "仓库类型不能为空");
        }
        List<RealWarehouseE> warehouseE=realWarehouseRepository.queryRealWarehouseByFactoryCodeAndRWType(factoryCode,supplierCode,Arrays.asList(type));
        return realWarehouseConvertor.entityToDto(warehouseE);
    }

    @Override
    public List<Long> queryRealWarehouseIdByShopCodes(List<String> shopCodes) {
	    if(CollectionUtils.isEmpty(shopCodes)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "门店编号不能为空");
        }
        List<RealWarehouse> list=this.findByRwListShopCodes(shopCodes);
        List<Long> res=list.stream().map(RealWarehouse :: getId).distinct().collect(Collectors.toList());
        return res;
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByShopCodes(List<String> shopCodes) {
        if(CollectionUtils.isEmpty(shopCodes)){
            throw new RomeException(ResCode.STOCK_ERROR_1002, "门店编号不能为空");
        }
        return this.findByRwListShopCodes(shopCodes);
    }

    @Override
    public RealWarehouse queryRealWarehouseByVmCode(String virtualWarehouseCode) {
        VirtualWarehouse virtualWarehouse = virtualWarehouseService.getVirtualWarehouseByCode(virtualWarehouseCode);
        AlikAssert.isNotNull(virtualWarehouse,ResCode.STOCK_ERROR_1002,"未查到虚仓code为"+virtualWarehouseCode+"的虚仓");
        RealWarehouseE realWarehouse = realWarehouseRepository.getRealWarehouseById(virtualWarehouse.getRealWarehouseId());
        return realWarehouseConvertor.entityToDto(realWarehouse);
    }

    @Override
    public RealWarehouse queryRealWarehouseByRealWarehouseCode(String realWarehouseCode) {
        RealWarehouseDO realWarehouseDO = realWarehouseRepository.queryRealWarehouseByRealWarehouseCode(realWarehouseCode);
        RealWarehouse realWarehouse = realWarehouseConvertor.doToDTO(realWarehouseDO);
        if(realWarehouse != null) {
            List<VirtualWarehouse> virtualWarehouseList = virtualWarehouseConvertor.entityListToDtoList(virtualWarehouseRepository.queryByRealWarehouseId(realWarehouse.getId()));
            for (VirtualWarehouse dto : virtualWarehouseList) {
                dto.setVirtualWarehouseTypeName(VirtualWarehouseTypeVO.getDescByType(dto.getVirtualWarehouseType()));
            }
            realWarehouse.setVirtualWarehouseList(virtualWarehouseList);
        }

        return realWarehouse;
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByRealWarehouseCodeList(List<String> realWarehouseCodeList) {
        List<RealWarehouseDO> realWarehouseDOList = realWarehouseRepository.queryRealWarehouseByRealWarehouseCodeList(realWarehouseCodeList);
        return realWarehouseConvertor.dosToDTOs(realWarehouseDOList);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByChannelCode(String channelCode) {
        List<VirtualWarehouseE> vwListByChannelCode = virtualWarehouseService.getVwListByChannelCode(channelCode);
        if(CollectionUtils.isNotEmpty(vwListByChannelCode)){
            List<Long> realWarehouseIdList = vwListByChannelCode.stream().map(x -> x.getRealWarehouseId()).distinct().collect(Collectors.toList());
            List<RealWarehouseE> realWarehouseList = realWarehouseRepository.getRealWarehouseByIds(realWarehouseIdList);
            if(CollectionUtils.isNotEmpty(realWarehouseList)) {
            	List<RealWarehouse> list = realWarehouseConvertor.entityToDto(realWarehouseList);
            	// key>>> realWarehouseId
            	Map<Long, VirtualWarehouseE> vwMap = RomeCollectionUtil.listforMap(vwListByChannelCode, "realWarehouseId");
            	VirtualWarehouseE v;
            	for(RealWarehouse dto : list) {
            		v = vwMap.get(dto.getId());
            		if(v != null) {
            			VirtualWarehouse virtualWarehouse = virtualWarehouseConvertor.entityToDto(v);
            			virtualWarehouse.setVirtualWarehouseTypeName(VirtualWarehouseTypeVO.getDescByType(virtualWarehouse.getVirtualWarehouseType()));
            			dto.setVirtualWarehouseList(Collections.singletonList(virtualWarehouse));
            		}
            	}
            	return list;
            }
        }
        return null;
    }

    @Override
    public List<StoreDTO> queryFactoryByRwType(Integer realWarehouseType) {
        return this.queryFactoryByRwTypeList(Arrays.asList(realWarehouseType));
    }

    @Override
    public List<StoreDTO> queryFactoryByRwTypeList(List<Integer> realWarehouseTypeList) {
	    if (CollectionUtils.isEmpty(realWarehouseTypeList)) {
	        return Collections.EMPTY_LIST;
        }
        List<RealWarehouseDO> realWarehouseDOList = realWarehouseRepository.queryFactoryByRwTypeList(realWarehouseTypeList);
	    if (CollectionUtils.isEmpty(realWarehouseDOList)) {
            return Collections.EMPTY_LIST;
        }
        List<String> factoryCodes = new ArrayList<>();
        realWarehouseDOList.forEach(realWarehouseE -> factoryCodes.add(realWarehouseE.getFactoryCode()));
        List<StoreDTO> storeDTOList = shopFacade.searchByCodeList(factoryCodes);
        return storeDTOList;
    }

    @Override
    public void addOrUpdateInformation(RealWarehouseAddition realWarehouseAdditionDTO) {
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(realWarehouseAdditionDTO.getRealWarehouseId());
        AlikAssert.isNotNull(realWarehouseE, ResCode.STOCK_ERROR_4002, ResCode.STOCK_ERROR_4002_DESC);
        if(RealWarehouseTypeVO.CROSS_TYPE_27.getType().equals(realWarehouseE.getRealWarehouseType())) {
            //跨境仓库校验
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getName())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "保税仓名称不能为空");
            }
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getStoreCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "保税仓编号不能为空");
            }
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getAreaCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "保税区编号不能为空");
            }
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getAreaName())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "保税区名称不能为空");
            }
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getCustomsCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "海关编号不能为空");
            }
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getCustomsName())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "海关名称不能为空");
            }
        }else if(RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29.getType().equals(realWarehouseE.getRealWarehouseType())){
            if (StringUtils.isEmpty(realWarehouseAdditionDTO.getStoreCode())) {
                throw new RomeException(ResCode.STOCK_ERROR_1017, "供应商编号不能为空");
            }
        }
        RealWarehouseAddition realWarehouseAddition= realWarehouseAdditionRepository.getByRealWarehouseId(realWarehouseAdditionDTO.getRealWarehouseId());
        if (null == realWarehouseAddition && null == realWarehouseAdditionDTO.getId()) {
            int j=realWarehouseAdditionRepository.addRealWarehouseAddition(realWarehouseAdditionDTO);
            if(j==0){
                throw new RomeException(ResCode.STOCK_ERROR_1017,"新增数据失败");
            }
        }else{
        	realWarehouseAdditionDTO.setId(realWarehouseAddition.getId());
            int j=realWarehouseAdditionRepository.updateRealWarehouseAddition(realWarehouseAdditionDTO);
            if(j==0){
                throw new RomeException(ResCode.STOCK_ERROR_1017,"修改数据失败");
            }
        }
    }

    @Override
    public RealWarehouseAddition getByRealWarehouseId(Long realWarehouseId) {
        RealWarehouseAddition realWarehouseAddition= realWarehouseAdditionRepository.getByRealWarehouseId(realWarehouseId);
	    return realWarehouseAddition;
    }

    @Override
    public List<RealWarehouse> queryCompanyCodesByFactoryCodes(List<String> factoryCodes) {
        if(CollectionUtils.isEmpty(factoryCodes)){
            return new ArrayList<>();
        }
        factoryCodes=factoryCodes.stream().distinct().collect(Collectors.toList());
        List<RealWarehouse> realWarehouseList=realWarehouseRepository.queryCompanyCodesByFactoryCodes(factoryCodes);
        return realWarehouseList;
    }


    /**
     * 同步实仓信息mdm
     * @param list
     * @return
     */
    @SuppressWarnings("unchecked")
	@Override
    @Transactional(rollbackFor = Exception.class)
    public void syncRealWarehouseMdm(List<RealWarehouseMdmDTO> list) {
        //基本参数校验
    	validSyncMdmInfo(list);
        // 查询是否存在的
    	List<String> realWarehouseCodeList = new ArrayList<>(list.size());
    	for(RealWarehouseMdmDTO dto : list) {
    		realWarehouseCodeList.add(dto.getRealWarehouseDTO().getRealWarehouseCode());
    	}
    	List<RealWarehouseE> existDbList = realWarehouseRepository.getRealWarehousesByCode(realWarehouseCodeList);
    	RealWarehouseE temp;
    	// 要更新的
    	List<RealWarehouseMdmDTO> updateList = new ArrayList<>();
    	for (int i = list.size() - 1; i >= 0; i--) {
    		temp = getDataDbByRealWarehouseCode(existDbList, list.get(i).getRealWarehouseDTO().getRealWarehouseCode());
    		if(temp != null) {
    			list.get(i).getRealWarehouseDTO().setId(temp.getId());
				updateList.add(list.get(i));
				list.remove(i);
    		}
		}
    	// 新增仓库信息处理 start
    	//查询实仓类型，是否需要创建虚仓配置
        String realWarehouseTypeArrStr = BaseinfoConfiguration.getInstance().get("mdm.sync", "realType.createVirualArr");
        List<Integer> realWarehouseTypeArr = realWarehouseTypeArrStr == null ? Collections.EMPTY_LIST :JSON.parseArray(realWarehouseTypeArrStr, Integer.class);
        // 需要创建虚仓相关的列表
        List<RealWarehouseMdmDTO> createByVirtualList = new ArrayList<>();
        RealWarehouseAddDTO realWarehouseDTO;
        for(RealWarehouseMdmDTO dto : list) {
        	realWarehouseDTO = dto.getRealWarehouseDTO();
        	//   创实仓
    		Long realWarehouseId = addRealWarehouse(realWarehouseDTO);
    		if(StringUtils.isNotEmpty(dto.getReturnWarehouseCode())){
                //创建发货仓对应的退货仓
                realWarehouseRepository.addOrUpdateReturnWarehouseConfig(realWarehouseDTO.getFactoryCode(),realWarehouseDTO.getRealWarehouseCode(),dto.getReturnWarehouseCode());
            }
    		realWarehouseDTO.setId(realWarehouseId);
    		// 跨境电商仓和云店供应商仓附加信息
    		if(RealWarehouseTypeVO.CROSS_TYPE_27.getType().equals(realWarehouseDTO.getRealWarehouseType())
    				|| RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29.getType().equals(realWarehouseDTO.getRealWarehouseType())) {
    			dto.getRealWarehouseAddition().setRealWarehouseId(realWarehouseId);
    			addOrUpdateInformation(dto.getRealWarehouseAddition());
    		}
    		if(realWarehouseTypeArr.contains(realWarehouseDTO.getRealWarehouseType())) {
    			createByVirtualList.add(dto);
    		}
    	}
        // 创建虚仓相关的
        if(createByVirtualList.size() > 0) {
        	VirtualWarehouse virtualWarehouse;
        	for(RealWarehouseMdmDTO dto : createByVirtualList) {
        		realWarehouseDTO = dto.getRealWarehouseDTO();
        		virtualWarehouse = new VirtualWarehouse();
                virtualWarehouse.setRealWarehouseId(realWarehouseDTO.getId());
                virtualWarehouse.setSyncRate(100);
                virtualWarehouse.setCreator(-1L);
                virtualWarehouse.setModifier(-1L);
                virtualWarehouse.setVirtualWarehouseName(realWarehouseDTO.getRealWarehouseName());
                virtualWarehouse.setVirtualWarehouseCode(realWarehouseDTO.getRealWarehouseCode());
                virtualWarehouseService.saveVirtualWarehouse(virtualWarehouse);
        	}
        }
        // 新增仓库信息处理 end

        // 要更新的 start
    	if(updateList.size() > 0) {
    		for(RealWarehouseMdmDTO dto : updateList) {
    			// 跨境电商仓和云店供应商仓附加信息
        		if(RealWarehouseTypeVO.CROSS_TYPE_27.getType().equals(dto.getRealWarehouseDTO().getRealWarehouseType())
        				|| RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29.getType().equals(dto.getRealWarehouseDTO().getRealWarehouseType())) {
        			dto.getRealWarehouseAddition().setRealWarehouseId(dto.getRealWarehouseDTO().getId());
        			addOrUpdateInformation(dto.getRealWarehouseAddition());
        		}
        		// 更新仓库信息
    			updateRealWarehouse(realWarehouseConvertor.addDtoToDto(dto.getRealWarehouseDTO()));
                if(StringUtils.isNotEmpty(dto.getReturnWarehouseCode())){
                    realWarehouseRepository.addOrUpdateReturnWarehouseConfig(dto.getRealWarehouseDTO().getFactoryCode(),dto.getRealWarehouseDTO().getRealWarehouseCode(),dto.getReturnWarehouseCode());
    		    }
    		}
    	}
    	// 要更新的 end
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByKpChannelCodeAll(String kpChannelCode) {
        if(StringUtils.isBlank(kpChannelCode)){
            throw new RomeException(ResCode.STOCK_ERROR_9075, ResCode.STOCK_ERROR_9075_DESC);
        }
        //根据鲲鹏渠道查询鲲鹏渠道与虚仓类型关系表获取仓库类型
        List<KpChannelVwTypeRelationDO> kpChannelVwTypeRelationDOList = kpChannelVwTypeRelationMapper.queryByKpChannelCodeList(Arrays.asList(kpChannelCode));
        if(CollectionUtils.isEmpty(kpChannelVwTypeRelationDOList)){
            return  new ArrayList<>();
        }
        //获取虚仓类
        List<Integer> virtualWarehouseTypeList = kpChannelVwTypeRelationDOList.stream().map(KpChannelVwTypeRelationDO::getVirtualWarehouseType).distinct().collect(Collectors.toList());
        //根据虚仓类型查询虚拟仓库表得到实仓id
        List<VirtualWarehouseE> virtualWarehouseList = virtualWarehouseRepository.queryByTypeList(virtualWarehouseTypeList);
        if(CollectionUtils.isEmpty(virtualWarehouseList)){
            return  new ArrayList<>();
        }
        //获取实仓id
        List<Long> realWarehouseIdList = virtualWarehouseList.stream().map(VirtualWarehouseE::getRealWarehouseId).distinct().collect(Collectors.toList());
        //根据实仓id列表查询实体仓库表得到实仓列表
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryWarehouseByIds(realWarehouseIdList);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    @Override
    public PageInfo<RealWarehouse> queryRealWarehouseByKpChannelCodePage(QueryRealWarehoureKpDTO queryRealWarehoureKpDTO) {
        String kpChannelCode = queryRealWarehoureKpDTO.getKpChannelCode();
        if(StringUtils.isBlank(kpChannelCode)){
            throw new RomeException(ResCode.STOCK_ERROR_9075, ResCode.STOCK_ERROR_9075_DESC);
        }
        //根据鲲鹏渠道查询鲲鹏渠道与虚仓类型关系表获取仓库类型
        List<KpChannelVwTypeRelationDO> kpChannelVwTypeRelationDOList = kpChannelVwTypeRelationMapper.queryByKpChannelCodeList(Arrays.asList(kpChannelCode));
        if(CollectionUtils.isEmpty(kpChannelVwTypeRelationDOList)){
            return  new PageInfo();
        }
        //获取虚仓类
        List<Integer> virtualWarehouseTypeList = kpChannelVwTypeRelationDOList.stream().map(KpChannelVwTypeRelationDO::getVirtualWarehouseType).distinct().collect(Collectors.toList());
        //根据虚仓类型查询虚拟仓库表得到实仓id
        List<VirtualWarehouseE> virtualWarehouseList = virtualWarehouseRepository.queryByTypeList(virtualWarehouseTypeList);
        if(CollectionUtils.isEmpty(virtualWarehouseList)){
            return  new PageInfo<>();
        }
        //获取实仓id
        List<Long> realWarehouseIdList = virtualWarehouseList.stream().map(VirtualWarehouseE::getRealWarehouseId).distinct().collect(Collectors.toList());
        Page page = PageHelper.startPage(queryRealWarehoureKpDTO.getPageIndex(), queryRealWarehoureKpDTO.getPageSize());
        queryRealWarehoureKpDTO.setIdList(realWarehouseIdList);
        List<RealWarehouseE>  realWarehouseEList= realWarehouseRepository.queryWarehouseByIdPage(queryRealWarehoureKpDTO);
        List<RealWarehouse> realWarehouseList = realWarehouseConvertor.entityToDto(realWarehouseEList);
        PageInfo<RealWarehouse> pageInfo = new PageInfo<>(page.getResult());
        pageInfo.setList(realWarehouseList);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseLimitWarehouse(List<String> factoryCodes) {
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRealWarehouseLimitWarehouse(factoryCodes);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    @Override
    public RealWarehouse queryReturnWarehouseByParams(QueryReturnWarehouseDTO queryReturnWarehouseDTO) {
        //取值优先级顺序：渠道+自定义类型+工厂+仓库 >工厂+类型+渠道＞工厂+仓库+渠道 >工厂+仓库+类型＞工厂+渠道 ＞工厂+类型 ＞工厂+仓库 ＞工厂
        if (StringUtils.isEmpty(queryReturnWarehouseDTO.getFactoryCode())) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "发货工厂编号不能为空");
        }
        RealWarehouseDO realWarehouseDo = null;
        QueryReturnWarehouseDTO temp;
        if (StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRomeChannelCode()) && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRealWarehouseCode())
                && !Objects.isNull(queryReturnWarehouseDTO.getRecordType())) {
            //渠道+自定义类型+工厂+仓库
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(queryReturnWarehouseDTO);
        }
        if (Objects.isNull(realWarehouseDo) && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRomeChannelCode())
                && !Objects.isNull(queryReturnWarehouseDTO.getRecordType())) {
            //工厂+类型+渠道
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setRecordType(queryReturnWarehouseDTO.getRecordType());
            temp.setRomeChannelCode(queryReturnWarehouseDTO.getRomeChannelCode());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo) && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRomeChannelCode())
                && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRealWarehouseCode())) {
            //工厂+仓库+渠道
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setRealWarehouseCode(queryReturnWarehouseDTO.getRealWarehouseCode());
            temp.setRomeChannelCode(queryReturnWarehouseDTO.getRomeChannelCode());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo) && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRealWarehouseCode())
                && !Objects.isNull(queryReturnWarehouseDTO.getRecordType())) {
            //工厂+仓库+类型
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setRealWarehouseCode(queryReturnWarehouseDTO.getRealWarehouseCode());
            temp.setRecordType(queryReturnWarehouseDTO.getRecordType());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo) && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRomeChannelCode())) {
            //工厂+渠道
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setRomeChannelCode(queryReturnWarehouseDTO.getRomeChannelCode());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo) && !Objects.isNull(queryReturnWarehouseDTO.getRecordType())) {
            //工厂+类型
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setRecordType(queryReturnWarehouseDTO.getRecordType());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo) && StringUtils.isNotEmpty(queryReturnWarehouseDTO.getRealWarehouseCode())) {
            //工厂+仓库
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            temp.setRealWarehouseCode(queryReturnWarehouseDTO.getRealWarehouseCode());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo)) {
            //工厂
            temp = new QueryReturnWarehouseDTO();
            temp.setFactoryCode(queryReturnWarehouseDTO.getFactoryCode());
            realWarehouseDo = realWarehouseReturnConfigMapper.queryReturnWarehouseByParams(temp);
        }
        if (Objects.isNull(realWarehouseDo)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001,"退货仓配置不存在");
        }
        return realWarehouseConvertor.doToDTO(realWarehouseDo);
    }

    @Override
    public List<RealWarehouse> queryRealWarehouseByOutCodeAndType(RealWarehouseOutParamDTO realWarehouseOutParamDTO) {
        List<RealWarehouseE> realWarehouseES = realWarehouseRepository.queryRealWarehouseByOutCodeAndType(realWarehouseOutParamDTO);
        return realWarehouseConvertor.entityToDto(realWarehouseES);
    }

    @Override
    public List<RealWarehouse> queryDeliveryWarehouseByOutRecordCode(String outRecordCode) {
        WDTOnlineRetailE frWDTSaleDO=frWDTSaleRepository.queryByOutRecordCode(outRecordCode);
        String frontRecordCode;
        if(Objects.isNull(frWDTSaleDO)){
            OnlineRetailE onlineRetailE=frSaleRepository.queryOnlineRetailByOutRecordCode(outRecordCode);
            if(Objects.isNull(onlineRetailE)){
                throw new RomeException(ResCode.STOCK_ERROR_1001,"交易订单号不存在");
            }else{
                frontRecordCode=onlineRetailE.getRecordCode();
            }
        }else{
            frontRecordCode=frWDTSaleDO.getRecordCode();
        }
        List<RwRecordPoolE> rwRecordPoolDoList=rwRecordPoolRepository.queryNotCanceledByFRId(frontRecordCode);
        if(CollectionUtils.isEmpty(rwRecordPoolDoList)){
            return Lists.newArrayList();
        }
        List<Long> realWarehouseIdList=rwRecordPoolDoList.stream().map(RwRecordPoolE::getRealWarehouseId).distinct().collect(Collectors.toList());
        List<RealWarehouseE> realWarehouseEList=realWarehouseRepository.getRealWarehouseByIds(realWarehouseIdList);
        return realWarehouseConvertor.entityToDto(realWarehouseEList);
    }

    @Override
    public String queryWDTWarehouseCode(String factoryCode, String warehouseCode) {
        if (StringUtils.isEmpty(factoryCode)) {
            //如果没有传仓库,直接返回原始值
            return warehouseCode;
        }
        String baseInfo = BaseinfoConfiguration.getInstance().get("wdt_warehouse_codes", "old_warehouse_codes");
        if (Objects.isNull(baseInfo)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "未配置旺店通仓库编码映射关系");
        }
        String list[] = baseInfo.split(",");
        List<String> baseInfoList = Arrays.asList(list);
        if (org.springframework.util.CollectionUtils.isEmpty(baseInfoList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "未配置旺店通仓库编码映射关系");
        }
        String realWarehouseCode = factoryCode + "-" + warehouseCode;
        if (!baseInfoList.contains(realWarehouseCode)) {
            //新配置的仓库,需要使用仓库全编码:X051-A001
            RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByCode(warehouseCode);
            if(Objects.isNull(realWarehouseE)){
                throw new RomeException(ResCode.STOCK_ERROR_1001, "实仓不存在:" + warehouseCode);
            }
            //如果是WDT回调库存,需要返回外部编号:A001
            return realWarehouseE.getRealWarehouseOutCode();
        }
        return warehouseCode;
    }

    @Override
    public RealWarehouse queryWarehouseByFactoryCodeAndWarehouseCode(String factoryCode, String realWarehouseOutCode) {
        RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseByOutCodeAndFactoryCode(realWarehouseOutCode,factoryCode);
        return realWarehouseConvertor.entityToDto(realWarehouseE);
    }

    @Override
    public List<String> querySpecialWarehouseList() {
        return realWarehouseRepository.querySpecialWarehouseList();
    }


    /**
     * 基本参数校验-同步实仓信息mdm
     * @param list
     */
    private void validSyncMdmInfo(List<RealWarehouseMdmDTO> list) {
    	if(list == null || list.size() == 0) {
    		throw new RomeException(ResCode.STOCK_ERROR_1002, ResCode.STOCK_ERROR_1002_DESC + ",一条数据没传无需处理");
    	}
    	for(RealWarehouseMdmDTO dto : list) {
    		if(dto.getRealWarehouseDTO() == null) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,基本信息不能为空");
    		}
    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getRealWarehouseCode())) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,仓库编码不能为空");
    		}
    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getRealWarehouseOutCode())) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,仓库外部编号不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
    		}
    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getFactoryCode())) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,工厂编码不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
    		}
    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getRealWarehouseName())) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,仓库名称不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
    		}
    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getRealWarehouseAddress())) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,详细地址不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
    		}
    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getCompanyCode())) {
    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,公司编码不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
    		}
    		if(Objects.nonNull(dto.getRealWarehouseDTO().getMaterialStorageType()) &&
                    !Objects.equals(dto.getRealWarehouseDTO().getMaterialStorageType(), WarehouseMaterialStorageTypeVO.TRANSPORT_UNIT.getType()) &&
                            !Objects.equals(dto.getRealWarehouseDTO().getMaterialStorageType(), WarehouseMaterialStorageTypeVO.BASIC_UNIT.getType())){
                throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,物资存储类型参数错误" + dto.getRealWarehouseDTO().getMaterialStorageType());
            }
//    		if(StringUtils.isBlank(dto.getRealWarehouseDTO().getCostCenterCode())) {
//    			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,成本中心编码不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
//    		}
    		// 处理获取mdm到中台的映射关系
    		RealWarehouseTypeVO typeVO = RealWarehouseTypeVO.getTypeVOByType(mdmRealWarehouseTypeMapToZt(dto));
            if (null == typeVO) {
                throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,仓库类型不存在或非法" + dto.getRealWarehouseDTO().getRealWarehouseCode());
            }
            if(typeVO == RealWarehouseTypeVO.RW_TYPE_21 || typeVO == RealWarehouseTypeVO.RW_TYPE_15 || typeVO == RealWarehouseTypeVO.RW_TYPE_1
            		|| typeVO == RealWarehouseTypeVO.CLOUD_SUPPLIER_TYPE_29) {
            	throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息," + typeVO.getDesc() + "不支持" + dto.getRealWarehouseDTO().getRealWarehouseCode());
            }
            if(typeVO == RealWarehouseTypeVO.CROSS_TYPE_27) {
            	if(dto.getRealWarehouseAddition() == null) {
        			throw new RomeException(ResCode.STOCK_ERROR_1004, "同步实仓信息,跨境电商仓和云店供应商仓附加信息不能为空" + dto.getRealWarehouseDTO().getRealWarehouseCode());
        		}
            	dto.getRealWarehouseAddition().setModifier(-1L);
            	dto.getRealWarehouseAddition().setCreator(-1L);
            }
            dto.getRealWarehouseDTO().setUserId(-1L);
    	}
    }

    /**
     * 获取数据，根据仓库编码
     * @param existDbList
     * @param realWarehouseCode
     * @return
     */
    private RealWarehouseE getDataDbByRealWarehouseCode(List<RealWarehouseE> existDbList, String realWarehouseCode) {
    	if(existDbList == null || existDbList.size() == 0) {
    		return null;
    	}
    	for(RealWarehouseE dto : existDbList) {
    		if(dto.getRealWarehouseCode().equals(realWarehouseCode)) {
    			return dto;
    		}
    	}
    	return null;
    }

    /**
     * 获取mdm到中台的映射关系
     * @param dto
     * @return
     */
    private Integer mdmRealWarehouseTypeMapToZt(RealWarehouseMdmDTO dto) {
    	if(StringUtils.isBlank(dto.getMdmRealWarehouseType()) || dto.getRealWarehouseDTO() == null) {
    		return null;
    	}
    	String realWarehouseTypeStr = BaseinfoConfiguration.getInstance().get("mdm.synctype", dto.getMdmRealWarehouseType());
    	if(realWarehouseTypeStr == null) {
    		log.error("mdm同步实仓信息,仓库类型【%s】没有配置或者传入不支持的类型，仓库编码：【%s】", dto.getMdmRealWarehouseType(), dto.getRealWarehouseDTO().getRealWarehouseCode());
    		return null;
    	}
    	Integer realWarehouseType = realWarehouseTypeStr == null ? null : Integer.valueOf(realWarehouseTypeStr);
    	dto.getRealWarehouseDTO().setRealWarehouseType(realWarehouseType);
    	return realWarehouseType;
    }
}
