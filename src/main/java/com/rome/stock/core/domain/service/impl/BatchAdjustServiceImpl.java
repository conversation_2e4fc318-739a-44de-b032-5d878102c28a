package com.rome.stock.core.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.BatchStockTypeVO;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.BatchStockUtils;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.api.dto.BoxStockPreLockDTO;
import com.rome.stock.core.api.dto.RealWarehouse;
import com.rome.stock.core.api.dto.ShopBatchConfigDTO;
import com.rome.stock.core.api.dto.frontrecord.*;
import com.rome.stock.core.common.AlikAssert;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.common.RomeCollectionUtil;
import com.rome.stock.core.common.SkuQtyUnitTools;
import com.rome.stock.core.constant.FrontRecordDetailStatusVO;
import com.rome.stock.core.constant.WarehouseRecordBusinessTypeVO;
import com.rome.stock.core.domain.convertor.frontrecord.FrBatchAdjustConvertor;
import com.rome.stock.core.domain.entity.frontrecord.BatchAdjustRecordDetailE;
import com.rome.stock.core.domain.entity.frontrecord.BatchAdjustRecordE;
import com.rome.stock.core.domain.entity.warehouserecord.BatchAdjustWarehouseRecordE;
import com.rome.stock.core.domain.repository.BatchStockToolRepository;
import com.rome.stock.core.domain.repository.frontrecord.FrBatchAdjustRepository;
import com.rome.stock.core.domain.service.BatchAdjustService;
import com.rome.stock.core.domain.service.BoxBatchStockService;
import com.rome.stock.core.domain.service.RealWarehouseService;
import com.rome.stock.core.facade.BatchStockFacade;
import com.rome.stock.core.infrastructure.dataobject.*;
import com.rome.stock.core.infrastructure.mapper.BatchStockMapper;
import com.rome.stock.core.infrastructure.mapper.RealWarehouseMapper;
import com.rome.stock.core.infrastructure.mapper.frontrecord.FrBatchAdjustDetailMapper;
import com.rome.stock.core.remote.item.dto.SkuInfoExtDTO;
import com.rome.stock.core.remote.item.facade.SkuFacade;
import com.rome.stock.core.remote.user.dto.EmployeeInfoDTO;
import com.rome.stock.core.remote.user.facade.EmployeeFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import com.rome.stock.wms.config.BaseinfoProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类BatchAdjustServiceimpl的实现描述：批次调整实现
 *
 * <AUTHOR> 2022/7/12 15:02
 */
@Slf4j
@Service
public class BatchAdjustServiceImpl implements BatchAdjustService {

    @Resource
    private EntityFactory entityFactory;

    @Resource
    private RealWarehouseService realWarehouseService;

    @Resource
    private FrBatchAdjustConvertor frBatchAdjustConvertor;

    @Resource
    private FrBatchAdjustRepository frBatchAdjustRepository;

    @Resource
    private SkuFacade skuFacade;

    @Resource
    private SkuQtyUnitTools skuQtyUnitTools;

    @Resource
    private BoxBatchStockService boxBatchStockService;

    @Resource
    private BatchStockToolRepository batchStockToolRepository;
    @Autowired
    private RealWarehouseMapper realWarehouseMapper;
    @Resource
    private BatchStockMapper batchStockMapper;
    @Autowired
    private FrBatchAdjustDetailMapper frBatchAdjustDetailMapper;
    @Resource
    private EmployeeFacade employeeFacade;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAdjust(List<WmsBatchAdjustDTO> rwBatchList, Integer businessType) {
            AlikAssert.isNotNull(rwBatchList, ResCode.STOCK_ERROR_1001, "调整wms的批次信息为空");
            WmsBatchAdjustDTO headInfo  = rwBatchList.get(0);
            //幂等判断
            //查询前置单是否存在
            BatchAdjustRecordE batchAdjustRecordE = frBatchAdjustRepository.queryFrontRecordByOutCode(headInfo.getOutRecordCode());
            if (batchAdjustRecordE != null){
                return;
            }
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(headInfo.getOutWarehouseCode(), headInfo.getFactoryCode());
            AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_9055,ResCode.STOCK_ERROR_9055_DESC);
            if(!BatchStockFacade.isSupportRealWarehouse(realWarehouse.getId(), realWarehouse.getRealWarehouseType())) {
                log.warn(BatchStockFacade.callByKibanaLogInterfaceLog(headInfo.getOutRecordCode(), "saveBatchAdjust", "", realWarehouse.getRealWarehouseCode() + "，中台暂未开启批次", true, "批次调整"));
                return;
            }
            //创建前置单
            BatchAdjustRecordE adjustRecordE = entityFactory.createEntity(BatchAdjustRecordE.class);
            adjustRecordE.setBusinessType(businessType);
            adjustRecordE.setRealWarehouseId(realWarehouse.getId());
            adjustRecordE.setOutRecordCode(headInfo.getOutRecordCode());
            adjustRecordE.setCreatorCode(headInfo.getCreatorCode());
            adjustRecordE.setRemark(headInfo.getRemark());
            List<BatchAdjustRecordDetailE> frontDetails = frBatchAdjustConvertor.dtoToEntity(rwBatchList);
            this.setSkuId(frontDetails);
            skuQtyUnitTools.convertRealToBasic(frontDetails);
            adjustRecordE.setFrontRecordDetails(frontDetails);
            adjustRecordE.addFrontRecord();
            //处理前置单明细
            List<BatchStockOpDO> boxStocklist = new ArrayList<>();
            List<BatchStockOpDO> mixStocklist = new ArrayList<>();
            for (BatchAdjustRecordDetailE frontDetail : frontDetails) {
                BatchStockOpDO batchStockDTO = new BatchStockOpDO();
                batchStockDTO.setSkuId(frontDetail.getSkuId());
                batchStockDTO.setSkuCode(frontDetail.getSkuCode());
                batchStockDTO.setSkuQty(frontDetail.getBasicSkuQty());
                batchStockDTO.setBatchCode(frontDetail.getBatchCode());
                batchStockDTO.setProductDate(frontDetail.getProductDate());
                batchStockDTO.setRealWarehouseId(realWarehouse.getId());
                batchStockDTO.setRealWarehouseType(realWarehouse.getRealWarehouseType());
                batchStockDTO.setRecordCode(adjustRecordE.getRecordCode());
                batchStockDTO.setValidity(frontDetail.getValidity());
                if(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus().equals(frontDetail.getAdjustType())) {
                    batchStockDTO.setRecordType(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_OUT_RECORD.getType());
                    batchStockDTO.setStockType(BatchStockTypeVO.STOCK_DECREASE.getType());
                }else{
                    batchStockDTO.setRecordType(WarehouseRecordTypeVO.BATCH_PROFIT_ADJUST_IN_RECORD.getType());
                    batchStockDTO.setStockType(BatchStockTypeVO.STOCK_INCREASE.getType());
                }
                if (Objects.equals(frontDetail.getBoxBatchTag(), 1)) {
                    boxStocklist.add(batchStockDTO);
                } else {
                    mixStocklist.add(batchStockDTO);
                }
            }

            BoxStockPreLockDTO preLockDTO = boxBatchStockService.getPreLockDTO(realWarehouse.getId(), null);
            if (preLockDTO.isOpenBox()) {
                if (CollectionUtils.isNotEmpty(boxStocklist)) {
                    BatchStockDataOpDO batchStockDataOpDO=new BatchStockDataOpDO();
                    batchStockDataOpDO.setDetailDos(boxStocklist);
                    // 分为两次操作，原箱操作一次，非原箱操作一次
                    batchStockToolRepository.batchStockRealtime(batchStockDataOpDO);
                    List<BatchStockChangeFlowDO> boxFlowList = batchStockDataOpDO.getFlowList();
                    //通过流水来处理原箱库存
                    boxBatchStockService.operateBoxStockByFlowList(boxFlowList, 1);
                }
                if (CollectionUtils.isNotEmpty(mixStocklist)) {
                    BatchStockDataOpDO batchStockDataOpDO=new BatchStockDataOpDO();
                    batchStockDataOpDO.setDetailDos(mixStocklist);
                    // 分为两次操作，原箱操作一次，非原箱操作一次
                    batchStockToolRepository.batchStockRealtime(batchStockDataOpDO);
                    List<BatchStockChangeFlowDO> mixFlowList = batchStockDataOpDO.getFlowList();
                    //通过流水来处理原箱库存
                    boxBatchStockService.operateBoxStockByFlowList(mixFlowList, 0);
                }
            } else {
                BatchStockDataOpDO batchStockDataOpDO=new BatchStockDataOpDO();
                List<BatchStockOpDO> allDetaislist = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(boxStocklist)) {
                    allDetaislist.addAll(boxStocklist);
                }
                if (CollectionUtils.isNotEmpty(mixStocklist)) {
                    allDetaislist.addAll(mixStocklist);
                }
                batchStockDataOpDO.setDetailDos(allDetaislist);
                // 分为两次操作，原箱操作一次，非原箱操作一次
                batchStockToolRepository.batchStockRealtime(batchStockDataOpDO);
            }




    }

    @Override
    public void saveBatchForInvetoryAdjust(List<WmsBatchAdjustDTO> rwBatchList, Long rwId, String outRecordCode, String inRecordCode) {
        List<BatchAdjustRecordDetailE> frontDetails = frBatchAdjustConvertor.dtoToEntity(rwBatchList);
        // 调用前需要自己填充skuId
//        this.setSkuId(frontDetails);
        skuQtyUnitTools.convertRealToBasic(frontDetails);
        //处理前置单明细
        List<BatchAdjustRecordDetailE> outFrontDetail = new ArrayList<>();
        List<BatchAdjustRecordDetailE> inFrontDetail = new ArrayList<>();
        for (BatchAdjustRecordDetailE frontDetail : frontDetails) {
            if(FrontRecordDetailStatusVO.WAREHOUSE_ADJUST_DISCREASE.getStatus().equals(frontDetail.getAdjustType())) {
                outFrontDetail.add(frontDetail);
            }else{
                inFrontDetail.add(frontDetail);
            }
        }
        //根据前置单和明细生成出入库单
        BatchAdjustWarehouseRecordE outWarehouseRecord = entityFactory.createEntity(BatchAdjustWarehouseRecordE.class);
        if(CollectionUtils.isNotEmpty(outFrontDetail)){
            //保存批次信息
            outWarehouseRecord.setBusinessType(WarehouseRecordBusinessTypeVO.OUT_WAREHOUSE_RECORD.getType());
            outWarehouseRecord.setRecordCode(outRecordCode);
            outWarehouseRecord.setRealWarehouseId(rwId);
            outWarehouseRecord.saveRwBatch(outFrontDetail);
        }
        BatchAdjustWarehouseRecordE inWarehouseRecord = entityFactory.createEntity(BatchAdjustWarehouseRecordE.class);
        if(CollectionUtils.isNotEmpty(inFrontDetail)){
            //保存批次信息
            inWarehouseRecord.setBusinessType(WarehouseRecordBusinessTypeVO.IN_WAREHOUSE_RECORD.getType());
            inWarehouseRecord.setRecordCode(inRecordCode);
            inWarehouseRecord.setRealWarehouseId(rwId);
            inWarehouseRecord.saveRwBatch(inFrontDetail);
        }
    }

    @Override
    @Transactional
    public List<SkuDiffResultDTO> inventoryBatch(SpecialShopBatchAdjustDTO param) {
        // 生成唯一的出库记录编码
        String outRecordCode = param.getShopCode() + "-" + UUID.randomUUID().toString().replace("-", "").substring(0, 10);
        param.setOutRecordCode(outRecordCode);

        // 查询门店对应的真实仓库
        List<RealWarehouseDO> shopList = realWarehouseMapper.queryByShopCodeList(Collections.singletonList(param.getShopCode()));
        if (CollectionUtils.isEmpty(shopList)) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "未查询到对应的门店信息");
        }
        RealWarehouseDO realWarehouse = shopList.get(0);
        param.setRealWarehouseId(realWarehouse.getId());

        // 查询批次库存信息
        List<String> skuCodeList = param.getRwBatchList().stream().map(WmsBatchAdjustDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<BatchStockDO> batchStockDOList = batchStockMapper.selectBySkuCodeListAndRealWarehouseId(skuCodeList, realWarehouse.getId());
        Map<String, List<BatchStockDO>> batchStockMap = batchStockDOList.stream().collect(Collectors.groupingBy(BatchStockDO::getSkuCode));
        // 执行容差检验
        List<SkuDiffResultDTO> skuToleranceList = this.tryInventoryBatch(param, batchStockMap);
        Map<String,BigDecimal>  skuToleranceMap = skuToleranceList.stream().collect(Collectors.toMap(SkuDiffResultDTO::getSkuCode, SkuDiffResultDTO::getDiff, (v1, v2) -> v1));
        // 找出超出容差的 SKU
        Map<String, BigDecimal> exceededSkuMap = skuToleranceList.stream().filter(SkuDiffResultDTO::isExceeded).collect(Collectors.toMap(SkuDiffResultDTO::getSkuCode, SkuDiffResultDTO::getDiff, (v1, v2) -> v1));
        if(MapUtils.isNotEmpty(exceededSkuMap)){
            log.error("存在超出容差值的批次,请检查,单号:{},超出容差列表:{}", param.getOutRecordCode(), JSON.toJSONString(exceededSkuMap));
            return skuToleranceList;
        }
        //param.getRwBatchList(),使用物料进行分组
        Map<String,List<WmsBatchAdjustDTO>>  batchDTOListMap= param.getRwBatchList().stream().collect(Collectors.groupingBy(WmsBatchAdjustDTO::getSkuCode));
        for (SkuDiffResultDTO skuDiffResultDTO : skuToleranceList) {
            String skuCode = skuDiffResultDTO.getSkuCode();
            if(!batchDTOListMap.containsKey(skuCode)){
                continue;
            }
            List<WmsBatchAdjustDTO> skuBatchList = batchDTOListMap.get(skuCode);
            // 按照生产日期,倒排，找出最差批次 (日期最早或null的批次)
            skuBatchList.sort((o1, o2) -> {
                if (o1.getProductDate() == null && o2.getProductDate() == null) {
                    return 0;
                }
                if (o1.getProductDate() == null) {
                    return 1;
                }
                if (o2.getProductDate() == null) {
                    return -1;
                }
                return o2.getProductDate().compareTo(o1.getProductDate());
            });
            //最差批次
            WmsBatchAdjustDTO worstBatch = skuBatchList.get(skuBatchList.size() - 1);
            if(!skuToleranceMap.containsKey(skuCode) || skuToleranceMap.get(skuCode).compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            //把容差的值加到最差批次上--反向操作
            BigDecimal diffQty = skuToleranceMap.getOrDefault(skuCode, BigDecimal.ZERO);
            log.info("容差处理 - SKU:{}, 容差差异:{}, 最差批次生产日期:{}", skuCode, diffQty, worstBatch.getProductDate());
            
            if(diffQty.compareTo(BigDecimal.ZERO)<=0){
                // diffQty是负数，需要增加批次数量来抵消差异
                BigDecimal originalQty = worstBatch.getSkuQty();
                worstBatch.setSkuQty(worstBatch.getSkuQty().add(diffQty.abs()));
                log.info("容差调整 - SKU:{}, 最差批次数量从{}调整到{}", skuCode, originalQty, worstBatch.getSkuQty());
            }else{
                // diffQty是正数，需要减少批次数量来抵消差异
                //需要按照批次数量,增加库存的明细上逐个扣减
                log.info("容差调整 - SKU:{}, 开始扣减批次数量，总需扣减:{}", skuCode, diffQty);
                for (WmsBatchAdjustDTO wmsBatchAdjustDTO : skuBatchList) {
                    if(diffQty.compareTo(BigDecimal.ZERO)>0){
                        BigDecimal originalQty = wmsBatchAdjustDTO.getSkuQty();
                        if(diffQty.compareTo(wmsBatchAdjustDTO.getSkuQty())>=0){
                            diffQty = diffQty.subtract(wmsBatchAdjustDTO.getSkuQty());
                            wmsBatchAdjustDTO.setSkuQty(BigDecimal.ZERO);
                            log.info("容差调整 - SKU:{}, 批次{}数量从{}扣减到0，剩余需扣减:{}", skuCode, wmsBatchAdjustDTO.getProductDate(), originalQty, diffQty);
                        }else{
                            wmsBatchAdjustDTO.setSkuQty(wmsBatchAdjustDTO.getSkuQty().subtract(diffQty));
                            log.info("容差调整 - SKU:{}, 批次{}数量从{}扣减到{}，完成扣减", skuCode, wmsBatchAdjustDTO.getProductDate(), originalQty, wmsBatchAdjustDTO.getSkuQty());
                            diffQty = BigDecimal.ZERO;
                        }
                    }
                }
            }
        }
        // 在容差范围内，处理调整
        List<WmsBatchAdjustDTO> resultList = new ArrayList<>();
        // 处理每个SKU的批次调整
        for (String skuCode : skuCodeList) {
            List<WmsBatchAdjustDTO> skuTargetBatches = param.getRwBatchList().stream().filter(batch -> skuCode.equals(batch.getSkuCode())).collect(Collectors.toList());
            List<BatchStockDO> existingBatches = batchStockMap.getOrDefault(skuCode, new ArrayList<>());
            // 先处理目标批次和实际批次的匹配调整
            for (WmsBatchAdjustDTO targetBatch : skuTargetBatches) {
                if (Objects.isNull(targetBatch.getProductDate())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "批次生产日期不能为空");
                }
                if (StringUtils.isEmpty(targetBatch.getUnitCode())) {
                    throw new RomeException(ResCode.STOCK_ERROR_1001, "单位不能为空");
                }
                Date productDate = targetBatch.getProductDate();
                BigDecimal targetQty = targetBatch.getSkuQty();
                // 找出匹配生产日期的批次
                List<BatchStockDO> matchingBatches = existingBatches.stream().filter(batch -> Objects.equals(productDate, batch.getProductDate())).collect(Collectors.toList());
                BigDecimal currentQty = matchingBatches.stream().map(BatchStockDO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal diffQty = targetQty.subtract(currentQty);
                // 添加调试日志
                log.info("批次匹配检查 - SKU:{}, 生产日期:{}, 目标数量:{}, 当前数量:{}, 差异:{}", skuCode, productDate, targetQty, currentQty, diffQty);
                // 如果有差异，生成调整记录
                if (diffQty.compareTo(BigDecimal.ZERO) != 0) {
                    WmsBatchAdjustDTO adjustmentDTO = new WmsBatchAdjustDTO();
                    BeanUtils.copyProperties(targetBatch, adjustmentDTO);
                    adjustmentDTO.setFactoryCode(realWarehouse.getFactoryCode());
                    adjustmentDTO.setOutWarehouseCode(realWarehouse.getRealWarehouseOutCode());
                    adjustmentDTO.setCreatorCode(param.getUserCode());
                    adjustmentDTO.setRemark(param.getRemark());

                    // 如果有匹配的批次，使用匹配批次的批次号
                    if (!CollectionUtils.isEmpty(matchingBatches)) {
                        adjustmentDTO.setProductDate(matchingBatches.get(0).getProductDate());
                    } else {
                        // 保持目标生产日期
                        adjustmentDTO.setProductDate(targetBatch.getProductDate());
                    }
                    if (diffQty.compareTo(BigDecimal.ZERO) > 0) {
                        // 益
                        adjustmentDTO.setAdjustType(2);
                        adjustmentDTO.setSkuQty(diffQty);
                    } else {
                        // 损
                        adjustmentDTO.setAdjustType(1);
                        adjustmentDTO.setSkuQty(diffQty.abs());
                    }
                    resultList.add(adjustmentDTO);
                    log.info("生成调整记录 - SKU:{}, 调整类型:{}, 调整数量:{}", skuCode, adjustmentDTO.getAdjustType(), adjustmentDTO.getSkuQty());
                } else {
                    log.info("批次数量匹配，无需调整 - SKU:{}, 生产日期:{}", skuCode, productDate);
                }
            }
            // 处理数据库中存在但目标盘点数据没有的批次（需要清零）
            List<Date> targetProductDates = skuTargetBatches.stream().map(WmsBatchAdjustDTO::getProductDate).collect(Collectors.toList());
            for (BatchStockDO batchStock : existingBatches) {
                if (!targetProductDates.contains(batchStock.getProductDate()) && batchStock.getSkuQty().compareTo(BigDecimal.ZERO) > 0) {
                    WmsBatchAdjustDTO adjustmentDTO = new WmsBatchAdjustDTO();
                    adjustmentDTO.setSkuCode(skuCode);
                    adjustmentDTO.setSkuId(batchStock.getSkuId());
                    adjustmentDTO.setBatchCode(batchStock.getBatchCode());
                    adjustmentDTO.setProductDate(batchStock.getProductDate());
                    adjustmentDTO.setSkuQty(batchStock.getSkuQty());
                    // 损
                    adjustmentDTO.setAdjustType(1);
                    adjustmentDTO.setFactoryCode(realWarehouse.getFactoryCode());
                    adjustmentDTO.setOutWarehouseCode(realWarehouse.getRealWarehouseOutCode());
                    adjustmentDTO.setCreatorCode(param.getUserCode());
                    adjustmentDTO.setRemark("门店批次库存盘点 - 移除多余批次");
                    resultList.add(adjustmentDTO);
                }
            }
        }
        //过滤掉调整数量是0的数据
        resultList=resultList.stream().filter(item->item.getSkuQty().compareTo(BigDecimal.ZERO)>0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(resultList)){
            log.info("盘点调整结果为空,单号:{}", param.getOutRecordCode());
            return Lists.newArrayList();
        }
        // 补充SKU单位信息
        enrichSkuUnitInfo(resultList);
        // 处理批次号和生产日期格式
        processBatchCodesAndDates(resultList);
        // 进行批次调整
        this.saveBatchAdjust(resultList, param.getBusinessType());
        return Lists.newArrayList();
    }

    /**
     * 补充SKU单位信息
     */
    private void enrichSkuUnitInfo(List<WmsBatchAdjustDTO> adjustList) {
        if (CollectionUtils.isEmpty(adjustList)) {
            return;
        }
        List<String> skuCodeList = adjustList.stream().map(WmsBatchAdjustDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodeList);
        Map<String, SkuInfoExtDTO> skuInfoExtMap = skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        for (WmsBatchAdjustDTO batchDetail : adjustList) {
            if (skuInfoExtMap.containsKey(batchDetail.getSkuCode())) {
                batchDetail.setUnitCode(skuInfoExtMap.get(batchDetail.getSkuCode()).getSpuUnitCode());
            }
        }
    }

    /**
     * 处理批次号和生产日期格式
     */
    private void processBatchCodesAndDates(List<WmsBatchAdjustDTO> adjustList) {
        for (WmsBatchAdjustDTO batchDetail : adjustList) {
            if (Objects.nonNull(batchDetail.getProductDate())) {
                String productDate = DateUtil.formatDate(batchDetail.getProductDate());
                BatchStockUtils.convertBatchCodeProductDateString(batchDetail.getBatchCode(), productDate, false, (code, date) -> {
                    batchDetail.setBatchCode(code);
                    batchDetail.setProductDate(date);
                });
            }
        }
    }


    /**
     * 检查物料维度的容差限制
     *
     * @param param 请求参数
     * @param batchStockMap 数据库现有批次库存数据Map
     * @return 超过容差限制的物料及其差异值 Map，若为空则表示所有物料都在容差范围内
     */
    @Override
    public List<SkuDiffResultDTO> tryInventoryBatch(SpecialShopBatchAdjustDTO param, Map<String, List<BatchStockDO>> batchStockMap) {
        List<String> skuCodeList = param.getRwBatchList().stream().map(WmsBatchAdjustDTO::getSkuCode).distinct().collect(Collectors.toList());
        if (Objects.isNull(batchStockMap)) {
            Long realWarehouseId =realWarehouseMapper.queryWarehouseIdByShopCode(param.getShopCode());
            List<BatchStockDO> batchStockDOList = batchStockMapper.selectBySkuCodeListAndRealWarehouseId(skuCodeList, realWarehouseId);
            batchStockMap = batchStockDOList.stream().collect(Collectors.groupingBy(BatchStockDO::getSkuCode));
        }
        // 读取库存批次容差配置
        BaseinfoProperty data = BaseinfoConfiguration.getInstance().getObject("shop_batch_config", "shop_batch_config");
        if (Objects.isNull(data) || StringUtils.isBlank(data.getValue())) {
            throw new RuntimeException("未配置库存批次容差值");
        }
        ShopBatchConfigDTO shopBatchConfigDTO = JSON.parseObject(data.getValue(), ShopBatchConfigDTO.class);
        // 查询商品中心物料
        List<SkuInfoExtDTO> skuInfoExtDTOList = skuFacade.skusBySkuCode(skuCodeList);
        Map<String, SkuInfoExtDTO> skuInfoExtMap = skuInfoExtDTOList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        // 构建 SKU 容差映射
        Map<String, BigDecimal> skuDiffMap = new HashMap<>();
        Set<String> exsitSkuCodeSet = new HashSet<>();
        for (WmsBatchAdjustDTO batchDetail : param.getRwBatchList()) {
            //param.getRwBatchList()根据skuCode+productDate,判断批次不能重复
            String batchCode =(Objects.isNull(batchDetail.getProductDate())?"":DateUtil.formatDate(batchDetail.getProductDate()));
            String uniKey = batchDetail.getSkuCode() +"_"+ batchCode;
            if (exsitSkuCodeSet.contains(uniKey)) {
                throw new RomeException(ResCode.STOCK_ERROR_1013, "商品编码:" + batchDetail.getSkuCode()+"批次日期重复:"+batchCode);
            }
            exsitSkuCodeSet.add(uniKey);
            String skuCode = batchDetail.getSkuCode();
            if (!skuInfoExtMap.containsKey(skuCode)) {
                continue;
            }
            SkuInfoExtDTO skuInfoExtDTO = skuInfoExtMap.get(skuCode);
            // 判断是否需要匹配 SKU Type 和 Combine Type
            boolean skuTypeCheck = CollectionUtils.isEmpty(shopBatchConfigDTO.getSkuType()) || shopBatchConfigDTO.getSkuType().contains(skuInfoExtDTO.getSkuTypeCode());
            boolean combineTypeCheck = CollectionUtils.isEmpty(shopBatchConfigDTO.getCombineType()) || shopBatchConfigDTO.getCombineType().contains(skuInfoExtDTO.getCombineType());
            if (skuTypeCheck && combineTypeCheck) {
                BigDecimal tolerance = "KG".equals(batchDetail.getUnitCode()) ? shopBatchConfigDTO.getWeightDiff() : shopBatchConfigDTO.getCountDiff();
                if (tolerance != null) {
                    skuDiffMap.put(skuCode, tolerance);
                }
            }
        }
        // 物料维度的差异汇总
        Map<String, BigDecimal> targetSkuQtyMap = new HashMap<>();
        for (WmsBatchAdjustDTO targetBatch : param.getRwBatchList()) {
            targetSkuQtyMap.merge(targetBatch.getSkuCode(), targetBatch.getSkuQty(), BigDecimal::add);
        }

        Map<String, BigDecimal> currentSkuQtyMap = new HashMap<>();
        for (Map.Entry<String, List<BatchStockDO>> entry : batchStockMap.entrySet()) {
            BigDecimal totalQty = entry.getValue().stream().map(BatchStockDO::getSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            currentSkuQtyMap.put(entry.getKey(), totalQty);
        }

        // 计算差异
        Set<String> allSkuCodes = new HashSet<>();
        allSkuCodes.addAll(targetSkuQtyMap.keySet());
        allSkuCodes.addAll(currentSkuQtyMap.keySet());

        List<SkuDiffResultDTO> resultList = new ArrayList<>();
        for (String skuCode : allSkuCodes) {
            BigDecimal targetQty = targetSkuQtyMap.getOrDefault(skuCode, BigDecimal.ZERO);
            BigDecimal currentQty = currentSkuQtyMap.getOrDefault(skuCode, BigDecimal.ZERO);
            BigDecimal diff = targetQty.subtract(currentQty);

            BigDecimal tolerance = skuDiffMap.get(skuCode);
            boolean exceeded = tolerance != null && diff.abs().compareTo(tolerance) > 0;
            resultList.add(new SkuDiffResultDTO(skuCode, diff, tolerance, exceeded));
        }
        return resultList;
    }

    @Override
    public PageInfo<FrBatchAdjustDTO> selectInventoryBatchAdjustByPage(FrBatchAdjustDTO dto) {
        //查询对应的门店编号
        Long realWarehouseId = realWarehouseMapper.queryWarehouseIdByShopCode(dto.getShopCode());
        if(Objects.isNull(realWarehouseId)){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "门店编号不存在");
        }
        dto.setRealWarehouseId(realWarehouseId);
        Page page = PageHelper.startPage(dto.getPageIndex(), dto.getPageSize());
        List<FrBatchAdjustDTO> list = frBatchAdjustRepository.selectInventoryBatchAdjustList(dto);
        if(CollectionUtils.isEmpty(list)){
            PageInfo<FrBatchAdjustDTO> pageInfo = new PageInfo<>(list);
            pageInfo.setTotal(page.getTotal());
            return pageInfo;
        }
        //返回每个调整单的商品种类
        List<Long> frontRecordIdList = RomeCollectionUtil.getValueList(list, "id");
        List<FrBatchAdjustDetailDTO> detailDTOList = frBatchAdjustDetailMapper.selectFrontIdList(frontRecordIdList);
        Map<Long,Integer> detailDTOMap = detailDTOList.stream().collect(Collectors.toMap(FrBatchAdjustDetailDTO::getFrontRecordId, FrBatchAdjustDetailDTO::getSkuTypeCount, (v1, v2) -> v1));
        //查询对应工号对应的员工信息
        List<String> userCodeList = list.stream().filter(v->StringUtils.isNotBlank(v.getCreatorCode())).map(FrBatchAdjustDTO::getCreatorCode).collect(Collectors.toList());
        List<EmployeeInfoDTO> employeeList = employeeFacade.searchEmployeeByNumList(userCodeList);
        Map<String, EmployeeInfoDTO> employeeMap = employeeList.stream().collect(Collectors.toMap(EmployeeInfoDTO::getEmployeeNumber, Function.identity(), (v1, v2) -> v1));
        for (FrBatchAdjustDTO frBatchAdjust : list) {
            Integer skuTypeCount = detailDTOMap.get(frBatchAdjust.getId());
            if(Objects.nonNull(skuTypeCount)){
                frBatchAdjust.setSkuTypeCount(skuTypeCount);
            }
            if(employeeMap.containsKey(frBatchAdjust.getCreatorCode())){
                frBatchAdjust.setCreatorName(employeeMap.get(frBatchAdjust.getCreatorCode()).getName());
            }
            frBatchAdjust.setCreateDate(DateUtil.formatDate(frBatchAdjust.getCreateTime()));
        }
        PageInfo<FrBatchAdjustDTO> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Override
    public List<FrBatchAdjustDetailDTO> selectInventoryBatchAdjustDetailList(FrBatchAdjustDetailDTO dto) {
        if(Objects.isNull(dto.getFrontRecordId())){
            throw new RomeException(ResCode.STOCK_ERROR_1001, "frontRecordId不能为空");
        }
        List<FrBatchAdjustDetailDTO> detailDTOList  = frBatchAdjustDetailMapper.selectInventoryBatchAdjustDetailList(dto);
        if(CollectionUtils.isEmpty(detailDTOList)){
            return Lists.newArrayList();
        }
        List<String> skuCodeList = detailDTOList.stream().map(FrBatchAdjustDetailDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skuListBySkuCodes(skuCodeList);
        Map<String, SkuInfoExtDTO> skuMap = skuInfoList.stream().collect(Collectors.toMap(SkuInfoExtDTO::getSkuCode, Function.identity(), (v1, v2) -> v1));
        for (FrBatchAdjustDetailDTO frBatchAdjustDetailDTO : detailDTOList) {
            if(!skuMap.containsKey(frBatchAdjustDetailDTO.getSkuCode())){
                continue;
            }
            SkuInfoExtDTO skuInfo = skuMap.get(frBatchAdjustDetailDTO.getSkuCode());
            frBatchAdjustDetailDTO.setSkuName(skuInfo.getName());
        }
        return detailDTOList;
    }


    /**
     * 设置基础单位
     * @param frontDetails
     */
    private void setSkuId(List<BatchAdjustRecordDetailE> frontDetails){
        List<String> skuCodeList = RomeCollectionUtil.getValueList(frontDetails, "skuCode");
        List<SkuInfoExtDTO> skuInfoList = skuFacade.skuListBySkuCodes(skuCodeList);
        Map<String, SkuInfoExtDTO> skuMap = RomeCollectionUtil.listforMap(skuInfoList, "skuCode");
        for (BatchAdjustRecordDetailE frontDetail : frontDetails) {
            SkuInfoExtDTO skuInfo = skuMap.get(frontDetail.getSkuCode());
            if(skuInfo != null){
                frontDetail.setSkuId(skuInfo.getId());
                frontDetail.setValidity(skuInfo.getTotalShelfLife());
            }
        }
    }
}
