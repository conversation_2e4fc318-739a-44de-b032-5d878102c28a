package com.rome.stock.core.domain.service;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.core.api.dto.*;
import com.rome.stock.core.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.core.constant.RealWarehouseTypeVO;
import com.rome.stock.core.remote.base.dto.StoreDTO;

import java.util.List;
import java.util.Map;

public interface RealWarehouseService {

	/**
	 * 根据主键查询实仓信息
	 * @param id 主键
	 * @return 实仓Dto
	 */
	RealWarehouse findByRealWarehouseId(Long id);

	/**
	 * 根据主键查询实仓信息
	 * @param ids 主键
	 * @return 实仓Dto
	 */
	List<RealWarehouse> findByRealWarehouseIds(List<Long> ids);

	/**
	 * 根据仓库编码查询实仓信息
	 * @param code 仓库编码
	 * @return 实仓Dto
	 */
	RealWarehouse findByRealWarehouseCode(String code);

	/**
	 * 根据外部仓库编码和工厂编号查询实仓信息
	 * @param outCode 外部仓库编码
	 * @param factoryCode 工厂编号
	 * @return 实仓Dto
	 */
	RealWarehouse findByRealWarehouseOutCodeAndFactoryCode(String outCode, String factoryCode);

	/**
	 * 根据实仓外部编码和工厂编码批量查询实仓信息
	 * @param list
	 * @return
	 */
	List<RealWarehouse> findByRealWarehouseOutCodeAndFactoryCodeList(List<QueryRealWarehouse> list);

	/**
	 * 根据门店编码查询实仓信息
	 * @param code 仓库编码
	 * @return 实仓Dto
	 */
	RealWarehouse findByRealWarehouseShopCode(String code);

	/**
	 * 根据仓库id批量查询实仓信息
	 * @param ids
	 * @return
	 */
	List<RealWarehouse> getRealWarehouseByIds(List<Long> ids);

	/**
	 * 根据门店编码查询实仓信息
	 * @param code 仓库编码
	 * @return 实仓Dto
	 */
	List<RealWarehouse> findByRwListShopCodes(List<String> codes);

	/**
	 * 根据条件查询实仓信息
	 * @param paramDTO 查询条件参数Dto
	 * @return 实仓Dto列表
	 */
	PageInfo<RealWarehouse> findByRealWarehouseCondition(RealWarehouseParamDTO paramDTO);

	/**
	 * 查询所有实仓信息
	 * @return 所有实仓集合
	 */
	List<RealWarehouse> findRealWarehouseAllList();

	/**
	 * 新增实仓信息
	 *  暂时不实现此功能，数据从WMS同步
	 */
	Long addRealWarehouse(RealWarehouseAddDTO realWarehouseAddDTO);

	/**
	 * 编辑实仓信息
	 */
	void updateRealWarehouse(RealWarehouse realWarehouse);

	/**
	 * 启用实仓
	 */
	void enableRealWarehouse(Long realWarehouseId, Long userId);

	/**
	 * 停用实仓
	 */
	void disableRealWarehouse(Long realWarehouseId, Long userId);

	/**
	 * 分配虚仓
	 */
	void allotVirtualWarehouse(RealWarehouseParamDTO paramDTO);

	/**
	 * 根据实仓主键ID查询实仓覆盖区域
	 */
	List<RealWarehouseArea> findAreaListByRealWarehouseId(Long realWarehouse);

	/**
	 * 分配实仓覆盖区域
	 */
	void allotRealWarehouseArea(Long realWarehouseId, RealWarehouseParamDTO paramDTO);

	/**
	 * 根据仓库编码查询实仓信息
	 * @param code 仓库编码
	 * @return 实仓Dto
	 */
	List<RealWarehouse> findListByRealWarehouseCode(List<String> code);

	/**
	 * 根据仓库库编码和工厂编码获取实仓信息
	 * */
	RealWarehouse findByRwCodeAndFactoryCode(String realWarehouseCode,String factoryCode);

	/**
	 * 查询仓库编码(不包含门店仓库)
	 * @param code
	 * @return
	 */
	List<RealWarehouse> queryWhByConditionForAdmin(RealWarehouseParamDTO paramDTO);

	/**
	 * 根据工厂code查询仓库-含门店
	 * @param factoryCode
	 * @return
			 */
	List<RealWarehouse> queryRealWarehouseByFactoryCode(String factoryCode);

	/**
	 * 根据工厂code查询仓库-非门店
	 * @param factoryCode
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByFactoryCodeNoShop(String factoryCode);

	/**
	 * 查询非门店仓
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseNoShop();

	/**
	 * 根据行政区域查询实仓信息
	 * @param queryRealWarehouse
	 * @return
	 */
	List<RealWarehouse> queryRWByCondition(QueryAreaRWarehouse queryRealWarehouse);

	/**
	 * 根据门店编码和仓库类型查询仓库，必须要求指定类型的仓库在对应的工厂下是唯一的
	 * @param shopCode 门店编码
	 * @param warehouseTypeVO  仓库类型
	 * @return
	 */
	RealWarehouse queryRealWarehouseByShopCodeAndType(String shopCode , RealWarehouseTypeVO warehouseTypeVO );

    /**
     * 根据门店号以及是否加盟查询转置仓
     * @param shopCode
     * @param isJoin
     * @return
     */
	RealWarehouse queryRealWarehouseByShopCodeAndTypeForJoinConvert(String shopCode, boolean isJoin);


	/**
	 * 获取加盟门店的退货仓库
	 * @param shopCode
	 * @return
	 */
	RealWarehouse getJoinReturnWarehouse(String shopCode);


	/**
	 * 根据仓库内部编号查询仓库信息
	 * @param code
	 * @return
	 */
	RealWarehouse queryRealWarehouseByInCode(String code);


	/**
	 * 根据实仓id查询实仓的所有skuId
	 * @param realWarehouseId
	 * @return
	 */
	List<RealWarehouseStockDTO> querySkuIdByWhId(String realWarehouseId);


	/**
	 * 根据仓库类型筛选工厂
	 * @param types
	 * @return
	 */
	List<RealWarehouse> getRealWarehouseFactory(List<Integer> types);

	/**
	 * 根据工厂code和仓库类型查询仓库信息
	 * @param types
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByFactoryCodeAndRWType(String factoryCode,List<Integer> types);

	/**
	 * 根据仓库类型查仓库
	 * @param type
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByRWType(Integer type);
	/**
	 * 根据批量工厂编码查询实仓信息
	 * @param factoryCodes
	 * @return
	 */
	List<RealWarehouse> queryRealWarehousesByFactoryCodes(List<String> factoryCodes);

	/**
	 * 查询虚拟商品实仓信息
	 * @return
	 */
	RealWarehouse queryVirtualSkuRealWarehouse();

	/**
	 * 根据门店编码查询默认发货仓
	 * @param shopCode
	 * @return
	 */
	Map<String, String> queryDefaultRealWarehouseByShopCode(String shopCode);

	/**
	 * 查询实仓信息列表（排除门店）
	 * @param nameOrCode
	 * @return
	 */
	List<RealWarehouse> queryRWList(String nameOrCode);


	/**
	 * 根据工厂code和仓库类型查询仓库信息---销售中心用
	 * @param factoryCode
	 * @param type:0.发货仓（不需要包装）；1.包装仓
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByFactoryCodeAndType(String factoryCode,Integer type);

    List<RealWarehouse> queryRealWarehouseByFactoryCodeAndRealWarehouseType(String factoryCode,String supplierCode, Integer type);

	List<Long> queryRealWarehouseIdByShopCodes(List<String> shopCodes);

	/**
	 * 根据门店code查询实仓信息
	 * @param shopCodes
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByShopCodes(List<String> shopCodes);

	/**
	 * 根据虚仓code查询实仓
	 * @param virtualWarehouseCode
	 * @return
	 */
	RealWarehouse queryRealWarehouseByVmCode(String virtualWarehouseCode);


	/**
	 * 根据内部编码查询实仓信息
	 * @param realWarehouseCode
	 * @return
	 */
	RealWarehouse queryRealWarehouseByRealWarehouseCode(String realWarehouseCode);

	/**
	 * 根据实仓内部编码查询实仓信息（批量）
	 * @param realWarehouseCodeList
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByRealWarehouseCodeList(List<String> realWarehouseCodeList);


	/**
	 * 根据渠道查实仓信息
	 * @param channelCode
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByChannelCode(String channelCode);

	/**
	 * 根据仓库类型查询分组工厂
	 * @param realWarehouseType
	 * @return
	 */
	List<StoreDTO> queryFactoryByRwType(Integer realWarehouseType);

	/**
	 * 根据仓库类型查询分组工厂
	 * @param realWarehouseTypeList
	 * @return
	 */
	List<StoreDTO> queryFactoryByRwTypeList(List<Integer> realWarehouseTypeList);

    void addOrUpdateInformation(RealWarehouseAddition realWarehouseAddition);

	RealWarehouseAddition getByRealWarehouseId(Long realWarehouseId);

    List<RealWarehouse> queryCompanyCodesByFactoryCodes(List<String> factoryCodes);

    /**
	 * 同步实仓信息mdm
	 * @param list
	 */
	void syncRealWarehouseMdm(List<RealWarehouseMdmDTO> list);

	/**
	 * 据鲲鹏渠道查询所有实仓信息
	 * @param kpChannelCode
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByKpChannelCodeAll(String  kpChannelCode);


	/**
	 * 据鲲鹏渠道分页查询实仓信息
	 * @param queryRealWarehoureKpDTO
	 * @return
	 */
	PageInfo<RealWarehouse> queryRealWarehouseByKpChannelCodePage(QueryRealWarehoureKpDTO queryRealWarehoureKpDTO);

	/**
	 * 根据仓库编码查询仓库信息
	 * @param factoryCodes
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseLimitWarehouse(List<String> factoryCodes);

    RealWarehouse queryReturnWarehouseByParams(QueryReturnWarehouseDTO queryReturnWarehouseDTO);

	/**
	 * 根据仓库外部编码(供应商编码)和仓库类型查询实仓列表
	 * @param realWarehouseOutParamDTO
	 * @return
	 */
	List<RealWarehouse> queryRealWarehouseByOutCodeAndType(RealWarehouseOutParamDTO realWarehouseOutParamDTO);

	List<RealWarehouse> queryDeliveryWarehouseByOutRecordCode(String outRecordCode);

	/**
	 * 新配置的仓库,需要使用仓库全编码:X051-A001
	 * @param factoryCode
	 * @param warehouseCode
	 * @param callBack 如果是WDT回调库存，则返回外部编码，否则返回实仓编码
	 * @return
	 */
    String queryWDTWarehouseCode(String factoryCode, String warehouseCode);

	/**
	 * 根据工厂编码和仓库外部编码查询实仓信息
	 * @param factoryCode
	 * @param realWarehouseOutCode
	 * @return
	 */
    RealWarehouse queryWarehouseByFactoryCodeAndWarehouseCode(String factoryCode, String realWarehouseOutCode);

	List<String> querySpecialWarehouseList();

}
