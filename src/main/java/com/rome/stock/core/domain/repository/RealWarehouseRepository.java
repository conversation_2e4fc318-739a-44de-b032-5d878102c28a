package com.rome.stock.core.domain.repository;

import com.rome.arch.core.exception.RomeException;
import com.rome.stock.core.api.dto.*;
import com.rome.stock.core.api.dto.frontrecord.SkuStock;
import com.rome.stock.core.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.core.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.constant.RealWarehouseTypeVO;
import com.rome.stock.core.domain.convertor.RealWarehouseAreaConvertor;
import com.rome.stock.core.domain.convertor.RealWarehouseConvertor;
import com.rome.stock.core.domain.convertor.RealWarehouseStockConvertor;
import com.rome.stock.core.domain.entity.RealWarehouseE;
import com.rome.stock.core.domain.entity.RealWarehouseStockE;
import com.rome.stock.core.infrastructure.dataobject.*;
import com.rome.stock.core.infrastructure.mapper.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;

@Repository
public class RealWarehouseRepository {

    @Autowired
    private RealWarehouseConvertor realWarehouseConvertor;
    @Autowired
    private RealWarehouseAreaConvertor realWarehouseAreaConvertor;
    @Autowired
    private RealWarehouseMapper realWarehouseMapper;
    @Autowired
    private RealWarehouseAreaMapper realWarehouseAreaMapper;
    @Resource
    private RealWarehouseStockMapper realWarehouseStockMapper;
    @Resource
    private RealWarehouseStockConvertor realWarehouseStockConvertor;
    @Autowired
    private ChannelSalesMapper channelSalesMapper;
    @Autowired
    private VirtualWarehouseMapper virtualWarehouseMapper;
    @Resource
    private RealWarehouseReturnConfigMapper realWarehouseReturnConfigMapper;


    public RealWarehouseE getRealWarehouseById(Long id) {
        RealWarehouseDO realWarehouseDo = realWarehouseMapper.queryById(id);
        RealWarehouseE realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDo);
        return realWarehouseE;
    }

    public RealWarehouseE getRealWarehouseByCode(String code) {
        RealWarehouseDO realWarehouseDo = realWarehouseMapper.queryByCode(code);
        RealWarehouseE realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDo);
        return realWarehouseE;
    }

    public List<RealWarehouseE> getRealWarehouseByIds(List<Long> ids) {
        List<RealWarehouseDO> realWarehouseDOList = realWarehouseMapper.queryByIds(ids);
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDOList);
        return realWarehouseEList;
    }

    public RealWarehouseE getRealWarehouseByOutCodeAndFactoryCode(String outCode, String factoryCode) {
        RealWarehouseDO realWarehouseDo = realWarehouseMapper.queryByOutCodeAndFactoryCode(outCode, factoryCode);
        RealWarehouseE realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDo);
        return realWarehouseE;
    }

    public List<RealWarehouseE> getRealWarehouseByOutCodeAndFactoryCodeList(List<QueryRealWarehouse> list){
      return   realWarehouseConvertor.doToEntity(realWarehouseMapper.queryBatchByOutCodeAndFactoryCode(list));
    }

    /**
     * 根据门店编号批量查询实体仓库
     *
     * @param shopCodes
     * @return
     */
    public List<RealWarehouseE> getRwListByShopCodes(List<String> shopCodes) {
        List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryByShopCodeList(shopCodes);
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDoList);
        return realWarehouseEList;
    }

    /**
     * 根据门店编号批量查询可用实体仓库
     *
     * @param shopCodes
     * @return
     */
    public List<RealWarehouseE> getAvailableRwListByShopCodes(List<String> shopCodes) {
        List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryAvailableRwListByShopCodeList(shopCodes);
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDoList);
        return realWarehouseEList;
    }

    public List<RealWarehouseE> getRealWarehouseByCondition(RealWarehouseParamDTO paramDTO) {
        List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryByCondition(paramDTO);
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDoList);
        return realWarehouseEList;
    }

    public List<RealWarehouseE> getRealWarehouseAllList() {
        List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryAll();
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDoList);
        return realWarehouseEList;
    }

    public boolean saveRealWarehouse(RealWarehouseE realWarehouseE) {
        RealWarehouseDO realWarehouseDo = realWarehouseConvertor.entityToDo(realWarehouseE);
        boolean executeResult = realWarehouseMapper.save(realWarehouseDo);
        realWarehouseE.setId(realWarehouseDo.getId());
        return executeResult;
    }

    public boolean updateRealWarehouseByWhere(RealWarehouseE realWarehouseE) {
        if (null == realWarehouseE) {
            return false;
        }
        RealWarehouseDO realWarehouseDo = realWarehouseConvertor.entityToDo(realWarehouseE);
        boolean executeResult = realWarehouseMapper.updateByWhere(realWarehouseDo);
        return executeResult;
    }


    public boolean updateRealWarehouseByWhereMerchant(RealWarehouseE realWarehouseE) {
        if (null == realWarehouseE) {
            return false;
        }
        RealWarehouseDO realWarehouseDo = realWarehouseConvertor.entityToDo(realWarehouseE);
        boolean executeResult = realWarehouseMapper.updateRealWarehouseByWhereMerchant(realWarehouseDo);
        return executeResult;
    }

    public boolean updateRealWarehouseStatusEnable(Long realWarehouseId, Long userId) {
        return realWarehouseMapper.updateStatusEnable(realWarehouseId, userId);
    }

    public boolean updateRealWarehouseStatusDisable(Long realWarehouseId, Long userId) {
        return realWarehouseMapper.updateStatusDisable(realWarehouseId, userId);
    }

    public boolean updateRealWarehouseIsAvailable(RealWarehouseDO realWarehouseDo) {
        if (null == realWarehouseDo) {
            return false;
        }
        boolean executeResult = realWarehouseMapper.updateIsAvailable(realWarehouseDo);
        return executeResult;
    }

    public boolean updateRealWarehouseIsDeleted(RealWarehouseDO realWarehouseDo) {
        if (null == realWarehouseDo) {
            return false;
        }
        boolean executeResult = realWarehouseMapper.updateIsDeleted(realWarehouseDo);
        return executeResult;
    }

    /**********实仓区域**********/
    public List<RealWarehouseE> queryAreaListByRealWarehouseId(Long realWarehouseId) {
        List<RealWarehouseAreaDo> realWarehouseAreaDoList = realWarehouseAreaMapper.queryListByRealWarehouseId(realWarehouseId);
        List<RealWarehouseE> realWarehouseEList = realWarehouseAreaConvertor.doToEntity(realWarehouseAreaDoList);
        return realWarehouseEList;
    }

    public boolean deleteRealWarehouseAreaByRWId(Long realWarehouseId, Long userId) {
        return realWarehouseAreaMapper.deleteByRealWarehouseId(realWarehouseId, userId);
    }

    public boolean insertRealWarehouseArea(List<RealWarehouseAreaAddDTO> realWarehouseAreaAddDTOList) {
        return realWarehouseAreaMapper.addBatch(realWarehouseAreaAddDTOList);
    }

    /**
     * 根据门店查询仓库id
     *
     * @param shopCode
     * @return
     */
    public Long queryWarehouseIdByShopCode(String shopCode) {
        Long warehouseId = realWarehouseMapper.queryWarehouseIdByShopCode(shopCode);
        return warehouseId;
    }

    public List<RealWarehouseE> getRealWarehousesByCode(List<String> code) {
        List<RealWarehouseDO> realWarehouseDos = realWarehouseMapper.querysByCodes(code);
        List<RealWarehouseE> realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDos);
        return realWarehouseE;
    }

    /**
     * 根据id集合查询仓库信息
     *
     * @param warehouseIdList
     * @return
     */
    public List<RealWarehouseE> queryWarehouseByIds(List<Long> warehouseIdList) {
        List<RealWarehouseDO> realWarehouseDos = realWarehouseMapper.queryWarehouseByIds(warehouseIdList);
        List<RealWarehouseE> realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDos);
        return realWarehouseE;
    }

    /**
     * 根据id集合分页查询仓库信息
     *
     * @param queryRealWarehoureKpDTO
     * @return
     */
    public List<RealWarehouseE> queryWarehouseByIdPage(QueryRealWarehoureKpDTO queryRealWarehoureKpDTO) {
        List<RealWarehouseDO> realWarehouseDos = realWarehouseMapper.queryWarehouseByIdPage(queryRealWarehoureKpDTO);
        List<RealWarehouseE> realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDos);
        return realWarehouseE;
    }
    /**
     * 根据仓库id查询仓库名称
     *
     * @param warehouseId
     * @return
     */
    public String getWarehouseNameById(Long warehouseId) {
        return realWarehouseMapper.getWarehouseNameById(warehouseId);
    }

    public RealWarehouseE getByRwCodeAndFactoryCode(String realWarehouseCode, String factoryCode) {
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.getByRwCodeAndFactoryCode(realWarehouseCode, factoryCode));
    }

    /**
     * 查询实物库存,根据skuId列表和realWarehouseId查询库存(仓库调拨专用)
     *
     * @return
     */
    public List<RealWarehouseStockDTO> listStockBySkuWhIdForWhAllot(List<Long> skuIds, Long warehouseId, Integer isQualityAllotcate) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.listStockBySkuWhIdForWhAllot(skuIds, warehouseId, isQualityAllotcate);
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }

    public List<SkuStock> queryRealStockBySkuCodeAndRealWarehouseIdsForSummary(List<String> skuCodeList, List<Long> warehouseIdList) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.queryRealStockBySkuCodeAndRealWarehouseIdsForSummary(skuCodeList, warehouseIdList);
        return realWarehouseConvertor.stockDoListToSkuStockList(stockDOList);
    }


    /**
     * 查询实物库存,根据skuCodes列表和realWarehouseId查询库存
     *
     * @return
     */
    public List<RealWarehouseStockDTO> listStockBySkuCodesAndRealWarehouseId(List<String> skuCodes, Long warehouseId) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.listStockBySkuCodesAndRealWarehouseId(skuCodes, warehouseId);
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }

    /**
     * 根据仓库ID查询实仓库存
     *
     * @return
     */
    public List<RealWarehouseStockDTO> queryStockByWhIdForWhAllot(RealWarehouseStockDTO dto) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.queryStockByWhIdForWhAllot(dto);
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }

    /**
     * 根据工厂code查询仓库信息
     *
     * @param factoryCode
     * @return
     */
    public List<RealWarehouse> queryRealWarehouseByFactoryCode(String factoryCode) {
        List<RealWarehouseDO> list = realWarehouseMapper.queryRealWarehouseByFactoryCode(factoryCode);
        return realWarehouseConvertor.entityToDto(realWarehouseConvertor.doToEntity(list));
    }


    public RealWarehouse querySingleRealWarehouseByFactoryCodeAndRwType(String factoryCode, Integer type) {
        RealWarehouseDO realWarehouseDO = realWarehouseMapper.querySingleRealWarehouseByFactoryCodeAndRwType(factoryCode, type);
        return realWarehouseConvertor.entityToDto(realWarehouseConvertor.doToEntity(realWarehouseDO));
    }

    /**
     * 根据仓库内部编号查询仓库信息
     *
     * @param code
     * @return
     */
    public RealWarehouseE queryRealWarehouseByInCode(String code) {
        RealWarehouseDO realWarehouseDo = realWarehouseMapper.queryRealWarehouseByInCode(code);
        RealWarehouseE realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDo);
        return realWarehouseE;
    }

    /**
     * 根据仓库ID查询实仓库存
     *
     * @return
     */
    public List<RealWarehouseStockDTO> querySkuIdByWhId(Long warehouseId) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.querySkuIdByWhId(warehouseId);
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }

    /**
     * 根据仓库ID查询实仓库存
     *
     * @return
     */
    public List<RealWarehouseStockDTO> querySkuInfoByWhStock(RealWarehouseStockDTO stockDTO) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.querySkuInfoByWhStock(realWarehouseConvertor.stockDtoToDo(stockDTO));
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }

    /**
     * 根据仓库类型查询所有工厂信息
     *
     * @param types
     * @return
     */
    public List<RealWarehouseE> getRealWarehouseFactory(List<Integer> types) {
        List<RealWarehouseDO> realWarehouseDos = realWarehouseMapper.getRealWarehouseFactory(types);
        List<RealWarehouseE> realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDos);
        return realWarehouseE;
    }

    /**
     * 根据工厂code和仓库类型查询仓库信息
     *
     * @param types
     * @return
     */
    public List<RealWarehouseE> queryRealWarehouseByFactoryCodeAndRWType(String factoryCode,String supplierCode, List<Integer> types) {
        List<RealWarehouseDO> realWarehouseDos = realWarehouseMapper.queryRealWarehouseByFactoryCodeAndRWType(factoryCode,supplierCode, types);
        List<RealWarehouseE> realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDos);
        return realWarehouseE;
    }

    public List<Long> queryRealWarehouseIdsByCode(String realWarehouseCode) {
        List<Long> ids = realWarehouseMapper.queryRealWarehouseIdsByCode(realWarehouseCode);
        return ids;
    }

    /**
     * 根据内部编码查询实仓
     * @param realWarehouseCode
     * @return
     */
    public RealWarehouseDO queryRealWarehouseByRealWarehouseCode(String realWarehouseCode){
       return realWarehouseMapper.queryRealWarehouseByRealWarehouseCode(realWarehouseCode);
    }

    /**
     * 根据内部编码查询实仓
     * @param realWarehouseCode
     * @return
     */
    public RealWarehouseE queryRealWarehouseEByRealWarehouseCode(String realWarehouseCode){
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.queryRealWarehouseByRealWarehouseCode(realWarehouseCode));
    }


    /**
     * 根据内部编码查询实仓 批量接口
     * @param realWarehouseCodeList
     * @return
     */
    public List<RealWarehouseDO> queryRealWarehouseByRealWarehouseCodeList(List<String> realWarehouseCodeList){
        return realWarehouseMapper.queryRealWarehouseByRealWarehouseCodeList(realWarehouseCodeList);
    }


    /**
     * 根据仓库类型查仓库
     *
     * @param type
     * @return
     */
    public List<RealWarehouseE> queryRealWarehouseByRWType(Integer type) {
        List<RealWarehouseDO> realWarehouseDos = realWarehouseMapper.queryRealWarehouseByRWType(type);
        List<RealWarehouseE> realWarehouseE = realWarehouseConvertor.doToEntity(realWarehouseDos);
        return realWarehouseE;
    }


    /**
     * 根据仓库ID查询实仓库存
     *
     * @return
     */
    public List<RealWarehouseStockDTO> queryStockByWhIdAndSkuIds(Long realWarehouseId, List<Long> skuIds) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.queryStockByWhIdAndSkuIds(realWarehouseId, skuIds);
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }

    /**
     * 查询仓库实际库存
     *
     * @param id
     * @return
     */
    public List<RealWarehouseStockE> queryAllWarehouseStockById(Long id) {
        List<RealWarehouseStockDO> stockDOS = realWarehouseStockMapper.querySkuQtyById(id);
        List<RealWarehouseStockE> list = realWarehouseStockConvertor.doListToEntityList(stockDOS);
        return list;
    }

    /**
     * 根据仓库ID查询实仓库存
     *
     * @return
     */
    public List<RealWarehouseStockE> queryWarehouseStockById(Long realWarehouseId, List<Long> skuIds) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.queryWarehouseStockById(realWarehouseId, skuIds);
        List<RealWarehouseStockE> list = realWarehouseStockConvertor.doListToEntityList(stockDOList);
        return list;
    }

    public List<RealWarehouse> queryWmsRealWarehouseByFactoryCode(String factoryCode) {
        List<RealWarehouseDO> list = realWarehouseMapper.queryWmsRealWarehouseByFactoryCode(factoryCode);
        return realWarehouseConvertor.entityToDto(realWarehouseConvertor.doToEntity(list));
    }

    //查询所有非门店仓
    public List<RealWarehouse> queryNotShopWarehouse(String factoryCode) {
        List<RealWarehouseDO> list = realWarehouseMapper.queryNotShopWarehouse(RealWarehouseTypeVO.RW_TYPE_1.getType(), factoryCode);
        return realWarehouseConvertor.entityToDto(realWarehouseConvertor.doToEntity(list));
    }

    //获取所有非门店仓下的工厂
    public List<String> queryNotShopFactory() {
        return realWarehouseMapper.queryNotShopFactory(RealWarehouseTypeVO.RW_TYPE_1.getType());
    }

    //获取所有非门店仓下的工厂
    public List<String> queryShopFactory() {
        return realWarehouseMapper.queryShopFactory(RealWarehouseTypeVO.RW_TYPE_1.getType());
    }
    /**
     * 通过仓库编码查询仓库信息
     * @param warehouseCode
     * @return
     */
    public RealWarehouseE getRealWarehouseByRmCode(String warehouseCode) {
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.getRealWarehouseByRmCode(warehouseCode));
    }

    /**
     *根据批量工厂编码查询实仓信息
     * @param factoryCodes
     * @return
     */
    public List<RealWarehouseE> queryRealWarehousesByFactoryCodes(List<String> factoryCodes) {
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.queryRealWarehousesByFactoryCodes(factoryCodes));
    }

    /**
     * 根据商家编号查询实仓信息
     * @param merchantId
     * @return
     */
    public RealWarehouse queryRealWarehousesByMerchantId(Long merchantId){
        List< ChannelSalesDo >  channelSales= channelSalesMapper.queryByMerchantId(merchantId);
        if(CollectionUtils.isEmpty(channelSales)){
            return null;
        }
        Long virtualWarehouseGroupId=channelSales.get(0).getVirtualWarehouseGroupId();
        List<VirtualWarehouseDo> virtualWarehouseDos=virtualWarehouseMapper.selectVirtualWarehouseByVWGroupId(virtualWarehouseGroupId);
        if(CollectionUtils.isEmpty(virtualWarehouseDos)){
            return null;
        }
        Long realWarehouseId=virtualWarehouseDos.get(0).getRealWarehouseId();
        RealWarehouseDO realWarehouseDO=realWarehouseMapper.queryById(realWarehouseId);
        if(realWarehouseDO==null){
            return null;
        }
        RealWarehouseE realWarehouseE= realWarehouseConvertor.doToEntity(realWarehouseDO);
        RealWarehouse realWarehouse=realWarehouseConvertor.entityToDto(realWarehouseE);
        return realWarehouse;
    }



    /**
     * 根据商家编号和skuCode查询实仓库存
     * @param merchantId
     * @return
     */
    public RealWarehouseStockDO queryRealWarehouseStockByMerchantId(String skuCode,Long merchantId){
        List< ChannelSalesDo >  channelSales= channelSalesMapper.queryByMerchantId(merchantId);
        if(CollectionUtils.isEmpty(channelSales)){
            return null;
        }
        Long virtualWarehouseGroupId=channelSales.get(0).getVirtualWarehouseGroupId();
        List<VirtualWarehouseDo> virtualWarehouseDos=virtualWarehouseMapper.selectVirtualWarehouseByVWGroupId(virtualWarehouseGroupId);
        if(CollectionUtils.isEmpty(virtualWarehouseDos)){
            return null;
        }
        Long realWarehouseId=virtualWarehouseDos.get(0).getRealWarehouseId();
        RealWarehouseStockDO realWarehouseStockDO=realWarehouseStockMapper.queryWarehouseStockByMerchantId(skuCode,realWarehouseId);
        return realWarehouseStockDO;
    }

    /**
     *查询虚拟商品仓库实仓
     * @return
     */
    public RealWarehouseE queryVirtualSkuRealWarehouse() {
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.queryVirtualSkuRealWarehouse());
    }

    /**
     * 根据渠道编号查询实仓信息
     * @param channelCode
     * @return
     */
    public Map<String, String> queryRealWarehouseByChannelCode(String channelCode){
        List<ChannelSalesDo> channelSales = channelSalesMapper.selectChannelSalesByChannelCodes(Arrays.asList(channelCode));
        if(CollectionUtils.isEmpty(channelSales)){
            return null;
        }
        Long virtualWarehouseGroupId = channelSales.get(0).getVirtualWarehouseGroupId();
        List<VirtualWarehouseDo> virtualWarehouseDos = virtualWarehouseMapper.selectVirtualWarehouseByVWGroupId(virtualWarehouseGroupId);
        if(CollectionUtils.isEmpty(virtualWarehouseDos)){
            return null;
        }
        Long realWarehouseId = virtualWarehouseDos.get(0).getRealWarehouseId();
        RealWarehouseDO realWarehouseDO = realWarehouseMapper.queryById(realWarehouseId);
        if(realWarehouseDO == null){
            return null;
        }
        if(StringUtils.isAnyBlank(channelSales.get(0).getChannelCode(), realWarehouseDO.getFactoryCode(), realWarehouseDO.getRealWarehouseCode())) {
        	return null;
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("channelCode", channelSales.get(0).getChannelCode());
        map.put("factoryCode", realWarehouseDO.getFactoryCode());
        map.put("realWarehouseCode", realWarehouseDO.getRealWarehouseCode());
        return map;
    }

    /**
     * 查询实仓信息列表（排除门店）
     * @return
     */
	public List<RealWarehouseE> queryRWList(@Nullable String nameOrCode) {
		List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryRWList(nameOrCode);
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDoList);
        return realWarehouseEList;
	}

    /**
     * 根据行政区域查实仓信息
     * @param queryRealWarehouse
     * @return
     */
	public List<RealWarehouseE> queryRWByCondition(QueryAreaRWarehouse queryRealWarehouse){
	    return     realWarehouseConvertor.doToEntity(realWarehouseMapper.queryRWByCondition(queryRealWarehouse));
    }

//    public String getChannelByRealWarehouseId(Long id) {
//        List<VirtualWarehouseDo> virtualWarehouseDoList=virtualWarehouseMapper.queryByRealWarehouseId(id);
//        if(CollectionUtils.isEmpty(virtualWarehouseDoList)){
//            throw new RomeException(ResCode.STOCK_ERROR_1002,"实仓对应的虚仓不存在");
//        }
//	    Long groupId=virtualWarehouseDoList.get(0).getVirtualWarehouseGroupId();
//        List<ChannelSalesDo> channelSalesDoList=channelSalesMapper.selectChannelSalesByGId(Arrays.asList(groupId));
//        if(CollectionUtils.isEmpty(channelSalesDoList)){
//            throw new RomeException(ResCode.STOCK_ERROR_1002,"策略组对应的渠道不存在");
//        }
//        return channelSalesDoList.get(0).getChannelCode();
//    }

    public int countRealWarehouseByFactoryCode(String factoryCode) {
	    return realWarehouseMapper.countRealWarehouseByFactoryCode(factoryCode);
    }

    public List<String> queryFactoryCodeByLike(String factoryCode) {
	    return realWarehouseMapper.queryFactoryCodeByLike(factoryCode);
    }

    /**
     * 临时性检验库存查询(union all实现)
     * @param queryForTempInsDTO
     * @return
     */
    public List<StockForTempInsPage> queryStockForTempIns(QueryForTempInsDTO queryForTempInsDTO){
        return realWarehouseStockMapper.queryStockForTempIns(queryForTempInsDTO);
    }

    /**
     * 临时性检验库存查询(工厂维度)
     * @param queryForFactoryDTO
     * @return
     */
    public List<StockForFactoryResultDTO> queryStockTempForFactory(QueryForFactoryDTO queryForFactoryDTO){
        return realWarehouseStockMapper.queryStockTempForFactory(queryForFactoryDTO);
    }

    /**
     * 根据仓库类型查询分组工厂
     * @param realWarehouseType
     * @return
     */
    public List<RealWarehouseDO> queryFactoryByRwType(Integer realWarehouseType) {
        return realWarehouseMapper.queryFactoryByRwType(realWarehouseType);
    }

    public List<RealWarehouse> queryRealWarehouseCodeCodeByLike(String realWarehouseCode,String factoryCode) {
        return realWarehouseMapper.queryRealWarehouseCodeCodeByLike(realWarehouseCode,factoryCode);
    }

    public QueryStockDTO queryRealWarehouseStockByRealWarehouseCodeAndSkuCode(QuerySkuStockDTO querySkuStockDTO) {
        RealWarehouseDO realWarehouse = realWarehouseMapper.getRealWarehouseByRmCode(querySkuStockDTO.getRealWarehouseCode());
        if (null == realWarehouse) {
            throw new RomeException(ResCode.STOCK_ERROR_1002, "实仓编号不存在");
        }
        querySkuStockDTO.setRealWarehouseId(realWarehouse.getId());
        return realWarehouseMapper.queryRealWarehouseStockByRealWarehouseCodeAndSkuCode(querySkuStockDTO);
    }


    /**
     * 查询数据
     *
     * @param realWarehouseTypeList
     * @return
     */
    public List<RealWarehouseDO> queryFactoryByRwTypeList(List<Integer> realWarehouseTypeList) {
        return realWarehouseMapper.queryFactoryByRwTypeList(realWarehouseTypeList);
    }

    public List<RealWarehouse> queryCompanyCodesByFactoryCodes(List<String> factoryCodes) {
        return realWarehouseMapper.queryCompanyCodesByFactoryCodes(factoryCodes);
    }

    public int updateRealWarehouseByInitField(RealWarehouseE realWarehouseE) {
        return realWarehouseMapper.updateRealWarehouseByInitField(realWarehouseE);

    }

    public int updateCostCenterCodeAndCostCenterName(String factoryCode, String costCenterCode, String costCenterName) {
        return realWarehouseMapper.updateCostCenterCodeAndCostCenterName(factoryCode,costCenterCode,costCenterName);
    }

    public List<RealWarehouse> queryByCompanyCodeAndRealWarehouseType(String companyCode,Integer realWarehouseType) {
        List<RealWarehouseDO> realWarehouseDO=realWarehouseMapper.queryByCompanyCodeAndRealWarehouseType(companyCode,realWarehouseType);
        return realWarehouseConvertor.dosToDTOs(realWarehouseDO);
    }

    public int addOrUpdateReturnWarehouseConfig(String factoryCode, String realWarehouseCode, String returnWarehouseCode) {
        //改成配置表sc_real_warehouse_return_config
        Long returnWarehouseId = realWarehouseMapper.queryRWIdByRealWarehouseCode(returnWarehouseCode);
        if (null == returnWarehouseId) {
            throw new RomeException(ResCode.STOCK_ERROR_1001, "退货仓编码不存在,realWarehouseCode:" + realWarehouseCode);
        }
        RealWarehouseReturnConfigDO temp = realWarehouseReturnConfigMapper.findOneByRealWarehouseCodeAndFactoryCode(realWarehouseCode, factoryCode);
        if (null != temp) {
            return realWarehouseReturnConfigMapper.updateById(temp.getId(), returnWarehouseId, returnWarehouseCode);
        }
        RealWarehouseReturnConfigDO realWarehouseReturnConfigDO = new RealWarehouseReturnConfigDO();
        realWarehouseReturnConfigDO.setFactoryCode(factoryCode);
        realWarehouseReturnConfigDO.setRealWarehouseCode(realWarehouseCode);
        realWarehouseReturnConfigDO.setReturnWarehouseCode(returnWarehouseCode);
        realWarehouseReturnConfigDO.setReturnWarehouseId(returnWarehouseId);
        realWarehouseReturnConfigDO.setCreator(-1L);
        realWarehouseReturnConfigDO.setRemark("系统自动创建");
        return realWarehouseReturnConfigMapper.save(realWarehouseReturnConfigDO);
    }

    /**
     * 根据外部编码与工厂编码批量查询
     * @param list
     * @return
     */
    public List<RealWarehouseE> queryByOutCodeAndFactoryCodeList(List<RealWarehouseDO> list) {
        List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryByOutCodeAndFactoryCodeList(list);
        List<RealWarehouseE> realWarehouseEList = realWarehouseConvertor.doToEntity(realWarehouseDoList);
        return realWarehouseEList;
    }

    /**
     * 根据仓库ID查询实仓库存,指定批次
     *
     * @return
     */
    public List<RealWarehouseStockDTO> queryStockByWhIdForWhAllotBatch(RealWarehouseStockDTO dto) {
        List<RealWarehouseStockDO> stockDOList = realWarehouseStockMapper.queryStockByWhIdForWhAllotBatch(dto);
        return realWarehouseConvertor.stockDoListToDtoList(stockDOList);
    }


    /**
     * 根据库位列表查询仓集合
     * @param locationCodes
     * @return
     */
    public List<RealWarehouseE> queryRealWarehouseByLocationCode(List<String> locationCodes) {
        if (locationCodes == null || locationCodes.size() == 0) {
            return new ArrayList<>();
        }
        List<RealWarehouseDO> realWarehouseDoList = realWarehouseMapper.queryWarehouseByLocationCode(locationCodes);
        return realWarehouseConvertor.doToEntity(realWarehouseDoList);
    }

    public List<RealWarehouseE> queryRealWarehouseLimitWarehouse(List<String> factoryCodes){
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.queryRealWarehouseLimitWarehouse(factoryCodes));
    }

    public List<RealWarehouse> queryWarehouseByRealWarehouseList(List<OutWarehouseRecordDTO> realWarehouseList) {
        return realWarehouseConvertor.dosToDTOs(realWarehouseMapper.queryWarehouseByRealWarehouseList(realWarehouseList));
    }

    public List<RealWarehouseE> queryRealWarehouseByOutCodeAndType(RealWarehouseOutParamDTO realWarehouseOutParamDTO){
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.queryRealWarehouseByOutCodeAndType(realWarehouseOutParamDTO.getRealWarehouseOutCode(),realWarehouseOutParamDTO.getRealWarehouseType()));
    }


    public RealWarehouseE queryByShopCode(String shopCode) {
        return realWarehouseConvertor.doToEntity(realWarehouseMapper.queryByShopCode(shopCode));
    }

    public List<String> querySpecialWarehouseList() {
        return realWarehouseMapper.querySpecialWarehouseList();
    }
}
