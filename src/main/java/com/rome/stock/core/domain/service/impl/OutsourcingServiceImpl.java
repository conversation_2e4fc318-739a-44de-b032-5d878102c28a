package com.rome.stock.core.domain.service.impl;

import com.rome.arch.core.domain.EntityFactory;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.core.api.dto.RealWarehouse;
import com.rome.stock.core.api.dto.frontrecord.OutsourcingOutDTO;
import com.rome.stock.core.common.AlikAssert;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.common.RomeCollectionUtil;
import com.rome.stock.core.configuration.processor.UnifiedEntryAndExitTool;
import com.rome.stock.core.configuration.processor.enums.SourceEnum;
import com.rome.stock.core.constant.FrontRecordStatusVO;
import com.rome.stock.core.constant.FrontRecordTypeVO;
import com.rome.stock.core.constant.WarehouseRecordStatusVO;
import com.rome.stock.core.domain.convertor.frontrecord.FrOutsourcingConvertor;
import com.rome.stock.core.domain.convertor.warehouserecord.ReplenishWarehouseConvertor;
import com.rome.stock.core.domain.entity.frontrecord.OutsourcingE;
import com.rome.stock.core.domain.entity.warehouserecord.OutsourcingWarehouseRecordE;
import com.rome.stock.core.domain.entity.warehouserecord.ShopReplenishWarehouseRecordE;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.domain.repository.frontrecord.FrOutsourcingRepository;
import com.rome.stock.core.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.core.domain.service.OneStepWarehouseStockService;
import com.rome.stock.core.domain.service.OutsourcingService;
import com.rome.stock.core.domain.service.RealWarehouseService;
import com.rome.stock.core.facade.RedisRollBackFacade;
import com.rome.stock.core.facade.StockCostFacade;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealWarehouseStockDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class OutsourcingServiceImpl implements OutsourcingService {

	@Resource
	private FrOutsourcingRepository frOutsourcingRepository;
	@Resource
	private FrOutsourcingConvertor frOutsourcingConvertor;
	@Resource
	private ReplenishWarehouseConvertor replenishWarehouseConvertor;
	@Resource
	private EntityFactory entityFactory;
	@Resource
	private CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
	@Resource
	private RealWarehouseService realWarehouseService;
	@Resource
	private WarehouseRecordRepository warehouseRecordRepository;
	@Resource
	private OneStepWarehouseStockService oneStepWarehouseStockService;
	/**
	 * 添加委外出库单：委外成品出库
	 *
	 * @param outsourcingOutDTO
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addOutsourcingOutRecord(OutsourcingOutDTO outsourcingOutDTO) {
		//幂等性判断
		if (frOutsourcingRepository.judgeExistByOutRecordCode(outsourcingOutDTO.getOutRecordCode())) {
			//单据已存在
			return;
		}
		List<WarehouseRecordE> warehouseRecordList = warehouseRecordRepository.queryUnCanceledBySapCode(outsourcingOutDTO.getAppointRecordCode());
		WarehouseRecordE warehouseRecordE = warehouseRecordList.stream().filter(x -> Objects.equals(x.getRecordType(), WarehouseRecordTypeVO.OUTSOURCING_OUT_RECORD.getType())).findFirst().orElse(null);
		if (warehouseRecordE != null) {
			throw new RomeException("999", "采购预约单号appointRecordCode=" +outsourcingOutDTO.getAppointRecordCode()+",已存在未取消的出库单recordCode=" + warehouseRecordE.getRecordCode());
		}
		RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outsourcingOutDTO.getRealWarehouseCode(),outsourcingOutDTO.getFactoryCode());
		AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC);

		OutsourcingE outsourcingE = frOutsourcingConvertor.outsourcingOutDtoToEntity(outsourcingOutDTO);
		outsourcingE.setRealWarehouseId(realWarehouse.getId());
		outsourcingE.setRecordStatus(FrontRecordStatusVO.INIT.getStatus());
		outsourcingE.setRecordType(FrontRecordTypeVO.OUTSOURCE_OUT_RECORD.getType());
		//创建通知单
		outsourcingE.addFrontRecord();
		OutsourcingWarehouseRecordE warehouseRecord = entityFactory.createEntity(OutsourcingWarehouseRecordE.class);
		//根据前置单生成出库单数据
		warehouseRecord.createRecordByFrontRecord(outsourcingE);
		warehouseRecord.setRecordStatus(WarehouseRecordStatusVO.INIT.getStatus());
		warehouseRecord.setAppId("1");
		//创建出库单
		warehouseRecord.addWarehouseRecord();
		//锁库存
		CoreRealStockOpDO coreRealStockOpDO = null;
		boolean isSuccess = false;
		try {
			//委外仓成品出库  冻结库存
			coreRealStockOpDO = warehouseRecord.initLockStockObj(realWarehouse.getId());
			coreRealWarehouseStockRepository.lockStock(coreRealStockOpDO);
			isSuccess = true;
		} catch (RomeException e) {
			throw e;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			if (!isSuccess) {
				RedisRollBackFacade.redisRollBack(coreRealStockOpDO);
			}
		}

	}


	/**
	 * 批量查询总库存
	 *
	 * @param skuIds
	 * @param rwId
	 * @return
	 */
	private Map<Long, CoreRealWarehouseStockDO> getTotalStock(List<Long> skuIds, Long rwId) {
		Map<Long, CoreRealWarehouseStockDO> skuStockMap = new HashMap<>();
		List<CoreRealWarehouseStockDO> queryParam = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(skuIds)) {
			for (Long skuId : skuIds) {
				CoreRealWarehouseStockDO crwDo = new CoreRealWarehouseStockDO();
				crwDo.setSkuId(skuId);
				crwDo.setRealWarehouseId(rwId);
				queryParam.add(crwDo);
			}
			List<CoreRealWarehouseStockDO> stockList = coreRealWarehouseStockRepository.getRWStock(queryParam);
			skuStockMap = RomeCollectionUtil.listforMap(stockList, "skuId");
		}
		return skuStockMap;
	}

	/**
	 * 委外仓成品入库
	 * @param outsourcingOutDTO
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addOutsourcingInRecord(OutsourcingOutDTO outsourcingOutDTO) {
		//幂等性判断
		if (frOutsourcingRepository.judgeExistByOutRecordCode(outsourcingOutDTO.getOutRecordCode())) {
			//单据已存在
			return;
		}
		if (UnifiedEntryAndExitTool.stockInHasConfigured(SourceEnum.ONE_STEP, WarehouseRecordTypeVO.OUTSOURCING_IN_RECORD.getType())) {
			outsourcingOutDTO.setFrontRecordStatus(FrontRecordStatusVO.IN_ALLOCATION.getStatus());
			outsourcingOutDTO.setFrontRecordType(FrontRecordTypeVO.OUTSOURCE_IN_RECORD.getType());
			oneStepWarehouseStockService.createOutsourcing(outsourcingOutDTO);
			return;
		}
		//查询实仓信息
		RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outsourcingOutDTO.getRealWarehouseCode(),outsourcingOutDTO.getFactoryCode());
		AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC);

		//存储委外成品入库单信息
		OutsourcingE outsourcingE = frOutsourcingConvertor.outsourcingOutDtoToEntity(outsourcingOutDTO);
		outsourcingE.setRecordType(FrontRecordTypeVO.OUTSOURCE_IN_RECORD.getType());
		outsourcingE.setRealWarehouseId(realWarehouse.getId());
		//状态为已入库
		outsourcingE.setRecordStatus(FrontRecordStatusVO.IN_ALLOCATION.getStatus());
		//创建通知单
		outsourcingE.addFrontRecord();
		OutsourcingWarehouseRecordE outSourceEndInWarehouseRecord = entityFactory.createEntity(OutsourcingWarehouseRecordE.class);
		//根据前置单生成出库单数据
		outSourceEndInWarehouseRecord.createRecordByFrontRecord(outsourcingE);
		outSourceEndInWarehouseRecord.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
		//设置当前时间为入库时间
		outSourceEndInWarehouseRecord.setOutOrInTime(new Date());
		outSourceEndInWarehouseRecord.setAppId("1");
		//创建出库单
		outSourceEndInWarehouseRecord.addWarehouseRecord();
		//满足库存要求 ，锁库存
		CoreRealStockOpDO endCoreRealStockOpDO = null;
		boolean isSuccess = false;
		try {
			endCoreRealStockOpDO = outSourceEndInWarehouseRecord.initIncreaseStockObj(realWarehouse.getId());
			//再增加成品库存
			coreRealWarehouseStockRepository.increaseRealQty(endCoreRealStockOpDO);
			WarehouseRecordE warehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outSourceEndInWarehouseRecord.getRecordCode());
			warehouseRecordE.setReceiptRecordCode(warehouseRecordE.getRecordCode());
			StockCostFacade.processCost(null, warehouseRecordE);
			isSuccess = true;
		} catch (RomeException e) {
			throw e;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			if (!isSuccess) {
				RedisRollBackFacade.redisRollBack(endCoreRealStockOpDO);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void addOutsourcingMaterialOutRecord(OutsourcingOutDTO outsourcingOutDTO) {
		//幂等性判断
		if (frOutsourcingRepository.judgeExistByOutRecordCode(outsourcingOutDTO.getOutRecordCode())) {
			//单据已存在
			return;
		}
		if (UnifiedEntryAndExitTool.stockOutHasConfigured(SourceEnum.ONE_STEP, WarehouseRecordTypeVO.OUTSOURCE_PURCHASE_END_PRODUCT_OUT_RECORD.getType())) {
			outsourcingOutDTO.setFrontRecordStatus(FrontRecordStatusVO.OUT_ALLOCATION.getStatus());
			outsourcingOutDTO.setFrontRecordType(FrontRecordTypeVO.OUTSOURCE_PURCHASE_RAW_MATERIAL_OUT_RECORD.getType());
			oneStepWarehouseStockService.createOutsourcing(outsourcingOutDTO);
			return;
		}
		WarehouseRecordE recordE = warehouseRecordRepository.getRecordWithDetailByCode(outsourcingOutDTO.getLockRecordCode(),null);
		if(Objects.equals(recordE.getRecordStatus(),WarehouseRecordStatusVO.DISABLED.getStatus())){
			throw new RomeException(ResCode.STOCK_ERROR_1001, "该单已完成或已取消不允许操作。");
		}
		//查询实仓信息
		RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(outsourcingOutDTO.getRealWarehouseCode(),outsourcingOutDTO.getFactoryCode());
		AlikAssert.isNotNull(realWarehouse, ResCode.STOCK_ERROR_1023, ResCode.STOCK_ERROR_1023_DESC);

		OutsourcingE outsourcingMaterialE = frOutsourcingConvertor.outsourcingOutDtoToEntity(outsourcingOutDTO);
		outsourcingMaterialE.setRealWarehouseId(realWarehouse.getId());
		//状态为已入库
		outsourcingMaterialE.setRecordStatus(FrontRecordStatusVO.OUT_ALLOCATION.getStatus());
		outsourcingMaterialE.setRecordType(FrontRecordTypeVO.OUTSOURCE_PURCHASE_RAW_MATERIAL_OUT_RECORD.getType());
		//创建通知单
		outsourcingMaterialE.addFrontRecord();
		OutsourcingWarehouseRecordE outSourceMaterialOutWarehouseRecord = entityFactory.createEntity(OutsourcingWarehouseRecordE.class);
		//根据前置单生成出库单数据
		outSourceMaterialOutWarehouseRecord.createRecordByFrontRecord(outsourcingMaterialE);
		outSourceMaterialOutWarehouseRecord.setRecordStatus(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus());
		outSourceMaterialOutWarehouseRecord.setAppId("1");
		//设置当前时间为入库时间
		outSourceMaterialOutWarehouseRecord.setOutOrInTime(new Date());
		//创建出库单
		outSourceMaterialOutWarehouseRecord.addWarehouseRecord();
		//满足库存要求 ，扣减库存
		CoreRealStockOpDO materialCoreRealStockOpDO = new CoreRealStockOpDO();
		//解锁库存
		CoreRealStockOpDO bigCoreRealStockOpDO = new CoreRealStockOpDO();
		boolean isSuccess = false;
		//查询锁定单据的出入库单
		try {
			ShopReplenishWarehouseRecordE replenishListE = replenishWarehouseConvertor.whToEntity(recordE);
			//出库和释放多锁的库存
			replenishListE.packOutAndUnlockStockObjForBigOrderNew(recordE,outSourceMaterialOutWarehouseRecord.getWarehouseRecordDetails(),bigCoreRealStockOpDO);
			if (!CollectionUtils.isEmpty(bigCoreRealStockOpDO.getDetailDos())) {
				coreRealWarehouseStockRepository.unlockStock(bigCoreRealStockOpDO);
			}
			materialCoreRealStockOpDO = this.initOutSourceMaterialOutWarehouseStockObj(outSourceMaterialOutWarehouseRecord, realWarehouse.getId());
			//先扣减原料实际库存
			coreRealWarehouseStockRepository.decreaseRealQty(materialCoreRealStockOpDO);
			isSuccess = true;
		} catch (RomeException e) {
			throw e;
		} catch (Exception e) {
			throw new RomeException(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
		} finally {
			if (!isSuccess) {
				RedisRollBackFacade.redisRollBack(materialCoreRealStockOpDO);
				RedisRollBackFacade.redisRollBack(bigCoreRealStockOpDO);
			}
		}
	}


	/**
	 * 初始化出库库存对象
	 */
	private CoreRealStockOpDO initOutSourceMaterialOutWarehouseStockObj(OutsourcingWarehouseRecordE outSourceMaterialOutWarehouseRecord, Long rwId){
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail: outSourceMaterialOutWarehouseRecord.getWarehouseRecordDetails()) {
			//数量为0的,或者为虚拟成本sku(X888)  不处理库存的增加和减少
			if(BigDecimal.ZERO.compareTo(detail.getPlanQty()) == 0 || "X888".equals(detail.getSkuCode())){
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getPlanQty());
			coreRealStockOpDetailDO.setCheckBeforeOp(true);
			coreRealStockOpDetailDO.setRealWarehouseId(rwId);
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		CoreRealStockOpDO coreRealStockOpDO = new CoreRealStockOpDO();
		coreRealStockOpDO.setRecordCode(outSourceMaterialOutWarehouseRecord.getRecordCode());
		coreRealStockOpDO.setTransType(outSourceMaterialOutWarehouseRecord.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
		return coreRealStockOpDO;
	}

}
