package com.rome.stock.core.api.controller;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.controller.RomeController;
import com.rome.stock.common.annotation.BathParamsValidate;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.core.api.dto.*;
import com.rome.stock.core.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.common.ValidList;
import com.rome.stock.core.constant.*;
import com.rome.stock.core.domain.repository.SapInterfaceLogRepository;
import com.rome.stock.core.domain.service.RealWarehouseService;
import com.rome.stock.core.domain.service.UnstandardService;
import com.rome.stock.core.facade.BatchStockFacade;
import com.rome.stock.core.facade.StockOnlineOrderFacade;
import com.rome.stock.core.remote.base.dto.StoreDTO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@RomeController
@RequestMapping("/stock/real_warehouse")
@Api(tags={"实仓管理接口"})
public class RealWarehouseController {

    private ParamValidator validator = ParamValidator.INSTANCE;

    @Autowired
    private RealWarehouseService realWarehouseService;

    @Autowired
    private UnstandardService unstandardService;

    @Resource
    private SapInterfaceLogRepository sapInterfaceLogRepository;

    @ApiOperation(value = "根据实仓主键查询实仓信息", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/id/{realWarehouseId}")
    public Response<RealWarehouse> findByRealWarehouseId(@PathVariable("realWarehouseId") String realWarehouseId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseId(Long.parseLong(realWarehouseId));
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓仓库编码查询实仓信息", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/code/{realWarehouseCode}")
    public Response<RealWarehouse> findByRealWarehouseCode(@PathVariable("realWarehouseCode") String realWarehouseCode) {
        if(! validator.validStr(realWarehouseCode)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseCode(realWarehouseCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据条件查询实仓信息,分页", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/condition")
    public Response<PageInfo<RealWarehouse>> findByRealWarehouseConditionPage(@RequestBody RealWarehouseParamDTO paramDTO) {
        if(! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<RealWarehouse> realWarehouseList = realWarehouseService.findByRealWarehouseCondition(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询所有实仓信息", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/list")
    public Response<List<RealWarehouse>> findRealWarehouseAllList() {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.findRealWarehouseAllList();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    @ApiOperation(value = "根据类型查询实仓", nickname = "queryByType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryByType")
    public Response<List<RealWarehouse>> queryByType(@RequestParam Integer type) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByRWType(type);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }



    @ApiOperation(value = "查询所有实仓类型信息", nickname = "rwTypeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/rwTypeList")
    public Response<Map<Integer, String>> rwTypeList() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouseTypeVO.getRealWarehouseTypeList());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询所有实仓层级信息", nickname = "rwRankList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/rwRankList")
    public Response<Map<Integer, String>> rwRankList() {
        try {
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouseRankVO.getRealWarehouseRankList());
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "新增实仓信息", nickname = "add_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/add_real_warehouse")
    public Response addRealWarehouse(@RequestBody RealWarehouseAddDTO realWarehouseAddDTO) {
        try {
            realWarehouseService.addRealWarehouse(realWarehouseAddDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "编辑实仓信息", nickname = "update_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/update_real_warehouse/{id}")
    public Response updateRealWarehouse(@PathVariable("id")String id, @RequestBody RealWarehouse realWarehouse) {
        try {
            if (! validator.validNumber(id)) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            if (! validator.validParam(realWarehouse)) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            realWarehouse.setId(Long.parseLong(id));
            realWarehouseService.updateRealWarehouse(realWarehouse);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "启用实仓", nickname = "enable_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/enable/{realWarehouseId}")
    public Response enableRealWarehouse(@PathVariable("realWarehouseId") String realWarehouseId, Long userId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseService.enableRealWarehouse(Long.parseLong(realWarehouseId), userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "停用实仓", nickname = "disable_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/disable/{realWarehouseId}")
    public Response disableRealWarehouse(@PathVariable("realWarehouseId") String realWarehouseId, Long userId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseService.disableRealWarehouse(Long.parseLong(realWarehouseId), userId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "实仓分配虚仓", nickname = "allot_virtual_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/allot_vm/{id}")
    public Response allotVirtualWarehouse(@PathVariable("id") String realWarehouseId, @RequestBody RealWarehouseParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)
                || ! validator.validCollection(paramDTO.getVwIdSyncRateDTOList())){
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        /*if (! validator.validNumber(paramDTO.getUserId())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }*/
        try {
            paramDTO.setRealWarehouseId(Long.parseLong(realWarehouseId));
            realWarehouseService.allotVirtualWarehouse(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓主键ID查询实仓区域信息", nickname = "query_real_warehouse_area", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/area_list/{realWarehouseId}")
    public Response<List<RealWarehouseArea>> findAreaListByRealWarehouseId(@PathVariable("realWarehouseId") String realWarehouseId) {
        if (! validator.validNumber(realWarehouseId)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouseArea> realWarehouseAreaDoList =
                    realWarehouseService.findAreaListByRealWarehouseId(Long.parseLong(realWarehouseId));
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseAreaDoList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "分配实仓覆盖区域区域", nickname = "allot_real_warehouse_area", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/allot_area/{realWarehouseId}")
    public Response allotRealWarehouseArea(@PathVariable("realWarehouseId") Long realWarehouseId, @RequestBody RealWarehouseParamDTO paramDTO) {
        if (! validator.validParam(paramDTO)
                || ! validator.validCollection(paramDTO.getRealWarehouseAreaAddDTOList())) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            realWarehouseService.allotRealWarehouseArea(realWarehouseId, paramDTO);
            StockOnlineOrderFacade.delRouteInfoByCoverFocusSale(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    /**
     * 获取仓库所有类型(从仓库类型枚举类中获取)
     * @return
     */
    @ApiOperation(value = "获取仓库所有类型", nickname = "list_real_warehouse_type", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = Map.class)
    @GetMapping("/listRealWarehouseType/{type}")
    public Response<Map<Integer, Object>> getWarehouseRecordType(@PathVariable("type") String type) {
        // 字符串非空判断
        if(!validator.validStr(type)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            Map<Integer, Object> warehouseType = WarehouseRecordTypeVO.getRealWareHouseType(type);
            return Response.builderSuccess(warehouseType);
        } catch (RomeException e){
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(), e.getMessage());
        } catch (Exception e){
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003, ResCode.STOCK_ERROR_1003_DESC);
        }
    }

    @ApiOperation(value = "根据条件查询实仓信息-运营平台查询接口,不分页", nickname = "find_real_warehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryForAdmin")
    public Response<List<RealWarehouse>> findByWhConditionForAdmin(@RequestBody RealWarehouseParamDTO paramDTO) {
        if(! validator.validParam(paramDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryWhByConditionForAdmin(paramDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据工厂code查询仓库信息-含门店", nickname = "queryRealWarehouseByFactoryCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseByFactoryCode")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCode(@RequestBody String factoryCode) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByFactoryCode(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据工厂code查询仓库信息-非门店", nickname = "queryRealWarehouseByFactoryCodeNoShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryRealWarehouseByFactoryCodeNoShop")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCodeNoShop(@RequestBody String factoryCode) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByFactoryCodeNoShop(factoryCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询非门店仓", nickname = "queryRealWarehouseNoShop", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealWarehouseNoShop")
    public Response<List<RealWarehouse>> queryRealWarehouseNoShop() {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseNoShop();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓ID查询实仓的sku", nickname = "querySkuIdByWhId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/querySkuIdByWhId")
    public Response<List<RealWarehouseStockDTO>> querySkuIdByWhId(@RequestParam("realWarehouseId") String realWarehouseId) {
        try {
            List<RealWarehouseStockDTO> stockList = realWarehouseService.querySkuIdByWhId(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg(stockList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    @ApiOperation(value = "根据批量工厂编码查询实仓信息", nickname = "queryRealWarehousesByFactoryCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealWarehousesByFactoryCodes")
    public Response<List<RealWarehouse>> queryRealWarehousesByFactoryCodes(@RequestParam("factoryCodes") List<String> factoryCodes) {
        try {
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealWarehousesByFactoryCodes(factoryCodes);
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据条件查询非标品信息,分页", nickname = "queryUnStandardByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = UnStandardDTO.class)
    @PostMapping("/queryUnStandardByCondition")
    public Response<PageInfo<UnStandardDTO>> queryUnStandardByCondition(@RequestBody UnStandardDTO unStandardDTO) {
        if(! validator.validParam(unStandardDTO)) {
            return ResponseMsg.PARAM_ERROR.buildMsg();
        }
        try {
            PageInfo<UnStandardDTO> unStandardList = unstandardService.queryUnStandardByCondition(unStandardDTO);
            return ResponseMsg.SUCCESS.buildMsg(unStandardList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "新增非标品信息", nickname = "saveUnStandard", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/saveUnStandard")
    public Response saveUnStandard(@RequestBody List<UnStandardDTO> unStandardDTOList) {
        try {
            unstandardService.saveUnStandard(unStandardDTOList);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "编辑非标品信息", nickname = "updateUnStandardByWhere", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/updateUnStandardByWhere")
    public Response updateUnStandardByWhere(@RequestBody UnStandardDTO unStandardDTO) {
        try {
            if (!validator.validParam(unStandardDTO)) {
                return ResponseMsg.PARAM_ERROR.buildMsg();
            }
            unstandardService.updateUnStandardByWhere(unStandardDTO);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "非标品库存调整", nickname = "unStandardStockAdjust", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = String.class)
    @PostMapping("/unStandardStockAdjust")
    public Response unStandardStockAdjust() {
        try {
            unstandardService.unStandardStockAdjust();
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询虚拟商品实仓信息", nickname = "queryVirtualSkuRealWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryVirtualSkuRealWarehouse")
    public Response<RealWarehouse> queryVirtualSkuRealWarehouse() {
        try {
            RealWarehouse realWarehouse = realWarehouseService.queryVirtualSkuRealWarehouse();
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

@ApiOperation(value = "根据工厂code和仓库类型查询仓库信息", nickname = "queryRealWarehouseByFactoryCodeAndRWType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @GetMapping("/queryRealWarehouseByFactoryCodeAndRWType")
    public Response<List<RealWarehouse>> queryRealWarehouseByFactoryCodeAndRWType(@RequestParam("factoryCode") String factoryCode, @RequestParam("realWarehouseTypeList") List<Integer> realWarehouseTypeList) {
        try {
            if(factoryCode == null || factoryCode.equals("")) {
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1002, "工厂编码为空");
            }
            if(realWarehouseTypeList == null || realWarehouseTypeList.isEmpty()) {
                return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1002, "仓库类型为空");
            }
            List<RealWarehouse> RealWarehouses = realWarehouseService.queryRealWarehouseByFactoryCodeAndRWType(factoryCode, realWarehouseTypeList);
            return ResponseMsg.SUCCESS.buildMsg(RealWarehouses);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

	@ApiOperation(value = "根据门店编码查询默认发货仓", nickname = "queryDefaultRealWarehouseByShopCode", produces = MediaType.APPLICATION_JSON_VALUE)
	@ApiResponse(code = 200, message = "success", response = Map.class)
	@RequestMapping(value = "/queryDefaultRealWarehouseByShopCode/{shopCode}", method = RequestMethod.GET)
	public Response<Map<String, String>> queryDefaultRealWarehouseByShopCode(@PathVariable("shopCode") String shopCode) {
		if (!validator.validStr(shopCode)) {
	         return ResponseMsg.PARAM_ERROR.buildMsg();
	     }
	    try {
	    	Map<String, String> map = realWarehouseService.queryDefaultRealWarehouseByShopCode(shopCode);
	        return ResponseMsg.SUCCESS.buildMsg(map);
	    } catch (RomeException e) {
	        log.error(e.getMessage(), e);
	        return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
	    } catch (Exception e) {
	        log.error(e.getMessage(), e);
	        return ResponseMsg.EXCEPTION.buildMsg();
	    }
	}

	@ApiOperation(value = "查询实仓信息列表（排除门店）", nickname = "query_rw_list", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryRWList")
    public Response<List<RealWarehouse>> queryRWList(@RequestParam(value = "nameOrCode",required = false)String nameOrCode) {
        try {
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRWList(nameOrCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据门店code + 仓库类型 查询门店发货仓", nickname = "queryRealWarehouseByShopCodeAndType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryRealWarehouseByShopCodeAndType")
    public Response<RealWarehouse> queryRealWarehouseByShopCodeAndType(@RequestParam("shopCode")String shopCode,@RequestParam("type")Integer type){
        try{
            RealWarehouseTypeVO realWarehouseTypeVO= RealWarehouseTypeVO.getTypeVOByType(type);
            if(Objects.nonNull(realWarehouseTypeVO)){
                RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByShopCodeAndType(shopCode, realWarehouseTypeVO);
                return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
            }
             return ResponseMsg.SUCCESS.buildMsg(null);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据虚仓code查询实仓", nickname = "queryRealWarehouseByVmCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByVmCode")
    public Response<RealWarehouse> queryRealWarehouseByVmCode(@RequestParam("virtualWarehouseCode")String virtualWarehouseCode){
        try{
            RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByVmCode(virtualWarehouseCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据实仓内部编码查询实仓", nickname = "queryRealWarehouseByRWCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByRWCode")
    public Response<RealWarehouse> queryRealWarehouseByRWCode(@RequestParam("realWarehouseCode")String realWarehouseCode){
        try{
            RealWarehouse realWarehouse = realWarehouseService.queryRealWarehouseByRealWarehouseCode(realWarehouseCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "批量查询根据实仓内部编码查询实仓", nickname = "queryRealWarehouseByRWCodeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByRWCodeList")
    public Response<List<RealWarehouse>> queryRealWarehouseByRWCodeList(@RequestParam("realWarehouseCodeList")List<String> realWarehouseCodeList){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByRealWarehouseCodeList(realWarehouseCodeList);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据条件查询实仓信息", nickname = "queryRWByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/list/realWarehouse")
    public Response<List<RealWarehouse>> queryRWByCondition(@RequestBody QueryAreaRWarehouse queryRealWarehouse){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRWByCondition(queryRealWarehouse);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据渠道查询实仓信息", nickname = "queryRealWarehouseByChannelCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/realWarehouse/list/{channelCode}")
    public Response<List<RealWarehouse>> queryRealWarehouseByChannelCode(@PathVariable("channelCode") String channelCode){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByChannelCode(channelCode);
           return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据仓库类型查询分组工厂", nickname = "queryRWByCondition", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryFactoryByRwType")
    public Response<List<RealWarehouse>> queryFactoryByRwType(@RequestParam("realWarehouseType") Integer realWarehouseType){
        try{
            List<StoreDTO> storeDTOList = realWarehouseService.queryFactoryByRwType(realWarehouseType);
            return ResponseMsg.SUCCESS.buildMsg(storeDTOList);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据仓库类型集合查询分组工厂", nickname = "queryFactoryByRwTypeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryFactoryByRwTypeList")
    public Response<List<RealWarehouse>> queryFactoryByRwTypeList(@RequestParam("realWarehouseTypeList") List<Integer> realWarehouseTypeList){
        try{
            List<StoreDTO> storeDTOList = realWarehouseService.queryFactoryByRwTypeList(realWarehouseTypeList);
            return ResponseMsg.SUCCESS.buildMsg(storeDTOList);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "查询sap过账推送日志最新一条", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @RequestMapping(value = "/queryTransferStatusList", method = RequestMethod.POST)
    public Response<List<SapInterfaceLogDTO>> listHistory(@RequestBody List<String> recordCodes, @ApiParam(name = "requestService") @RequestParam("requestService") String requestService) {
        try {
            List<SapInterfaceLogDTO> res=sapInterfaceLogRepository.queryByCodeAndRequestService(recordCodes,requestService);
            return Response.builderSuccess(res);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Response.builderFail(ResCode.STOCK_ERROR_1003_DESC,ResCode.STOCK_ERROR_1003_DESC);
        }
    }


    @ApiOperation(value = "新增、修改实仓附加信息", nickname = "addOrUpdateInformation", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseAddition.class)
    @PostMapping("/addOrUpdateInformation")
    public Response addOrUpdateInformation(@RequestBody RealWarehouseAddition realWarehouseAddition) {
        try {
            realWarehouseService.addOrUpdateInformation(realWarehouseAddition);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }

    @ApiOperation(value = "获取实仓附加信息", nickname = "getByRealWarehouseId", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseAddition.class)
    @GetMapping("/getByRealWarehouseId")
    public Response<RealWarehouseAddition> getByRealWarehouseId(@RequestParam("realWarehouseId") Long realWarehouseId) {
        try {
            RealWarehouseAddition realWarehouseAddition=realWarehouseService.getByRealWarehouseId(realWarehouseId);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseAddition);
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }

    @ApiOperation(value = "同步实仓信息mdm", nickname = "syncRealWarehouseMdm", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouseMdmDTO.class)
    @PostMapping("/syncRealWarehouseMdm")
    public Response syncRealWarehouseMdm(@RequestBody List<RealWarehouseMdmDTO> list) {
        try {
            realWarehouseService.syncRealWarehouseMdm(list);
            return ResponseMsg.SUCCESS.buildMsg();
        } catch (RomeException e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }


    @ApiOperation(value = "批量查询公司编码", nickname = "queryCompanyCodesByFactoryCodes", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryCompanyCodesByFactoryCodes")
    public Response<List<RealWarehouse>> queryCompanyCodesByFactoryCodes(@RequestBody List<String> factoryCodes){
        try{
            List<RealWarehouse> list=realWarehouseService.queryCompanyCodesByFactoryCodes(factoryCodes);
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(com.rome.stock.common.constants.ResCode.STOCK_ERROR_1001, e.getMessage());
        }
    }


    @ApiOperation(value = "根据鲲鹏渠道查询所有的实仓列表", nickname = "queryRealWarehouseByKpChannelCodeAll", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @GetMapping("/queryRealWarehouseByKpChannelCodeAll")
    public Response<List<RealWarehouse>>  queryRealWarehouseByKpChannelCodeAll(@RequestParam("kpChannelCode")  String kpChannelCode){
        try{
            List<RealWarehouse> realWarehouses = realWarehouseService.queryRealWarehouseByKpChannelCodeAll(kpChannelCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouses);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }

    @ApiOperation(value = "根据鲲鹏渠道分页查询的实仓列表", nickname = "queryRealWarehouseByKpChannelCodePage", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByKpChannelCodePage")
    public Response<PageInfo<RealWarehouse>> queryRealWarehouseByKpChannelCodePage(@RequestBody QueryRealWarehoureKpDTO queryRealWarehoureKpDTO){
        try{
            PageInfo<RealWarehouse> realWarehousePageInfo = realWarehouseService.queryRealWarehouseByKpChannelCodePage(queryRealWarehoureKpDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehousePageInfo);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }
    
    @ApiOperation(value = "根据outCode和factoryCode查询实仓是否支持批次,true支持，否则不支持", nickname = "queryBySupportBatchStock", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response =Boolean.class)
    @PostMapping("/queryBySupportBatchStock")
    public Response<Boolean> queryBySupportBatchStock(@RequestBody QueryRealWarehouse param) {
        try{
        	RealWarehouse realWarehouse = realWarehouseService.findByRealWarehouseOutCodeAndFactoryCode(param.getWarehouseOutCode(), param.getFactoryCode());
        	if(realWarehouse == null) {
        		return ResponseMsg.FAIL.buildMsg(ResCode.STOCK_ERROR_4002, ResCode.STOCK_ERROR_4002_DESC);
        	}
            return ResponseMsg.SUCCESS.buildMsg(BatchStockFacade.isSupportRealWarehouse(realWarehouse.getId(), realWarehouse.getRealWarehouseType()));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据罗马渠道、单据类型、工厂编号、仓库编号获取对应配置的退货仓", nickname = "queryReturnWarehouseByParams", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryReturnWarehouseByParams")
    public Response<RealWarehouse> queryReturnWarehouseByParams(@RequestBody QueryReturnWarehouseDTO queryReturnWarehouseDTO){
        try{
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseService.queryReturnWarehouseByParams(queryReturnWarehouseDTO));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据仓库外部编码(供应商编码)和仓库类型查询实仓列表", nickname = "queryRealWarehouseByOutCodeAndType", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success", response = RealWarehouse.class)
    @PostMapping("/queryRealWarehouseByOutCodeAndType")
    public Response<List<RealWarehouse>> queryRealWarehouseByOutCodeAndType(@Valid @RequestBody RealWarehouseOutParamDTO realWarehouseOutParamDTO){
        try{
            List<RealWarehouse> realWarehouseList = realWarehouseService.queryRealWarehouseByOutCodeAndType(realWarehouseOutParamDTO);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseList);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg();
        }
    }


    @ApiOperation(value = "根据交易单号查询发货仓", nickname = "queryDeliveryWarehouseByOutRecordCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryDeliveryWarehouseByOutRecordCode")
    public Response<List<RealWarehouse>> queryDeliveryWarehouseByOutRecordCode(@RequestParam("outRecordCode") String outRecordCode){
        try{
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseService.queryDeliveryWarehouseByOutRecordCode(outRecordCode));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }


    @ApiOperation(value = "新配置的仓库,需要使用仓库全编码", nickname = "queryWDTWarehouseCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryWDTWarehouseCode")
    public Response<String> queryWDTWarehouseCode(@RequestParam(value="factoryCode",required = false) String factoryCode,@RequestParam("warehouseCode") String warehouseCode){
        try{
            return ResponseMsg.SUCCESS.buildMsg(realWarehouseService.queryWDTWarehouseCode(factoryCode,warehouseCode));
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }

    @ApiOperation(value = "根据工厂和库位查询仓库信息", nickname = "queryWarehouseByFactoryCodeAndWarehouseCode", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/queryWarehouseByFactoryCodeAndWarehouseCode")
    public Response<RealWarehouse> queryWarehouseByFactoryCodeAndWarehouseCode(@RequestParam(value="factoryCode") String factoryCode,@RequestParam("realWarehouseOutCode") String realWarehouseOutCode){
        try{
            RealWarehouse realWarehouse=realWarehouseService.queryWarehouseByFactoryCodeAndWarehouseCode(factoryCode,realWarehouseOutCode);
            return ResponseMsg.SUCCESS.buildMsg(realWarehouse);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }


    @ApiOperation(value = "查找仓店一体的门店集合", nickname = "querySpecialWarehouseList", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiResponse(code = 200, message = "success")
    @PostMapping("/querySpecialWarehouseList")
    public Response<List<String>> querySpecialWarehouseList(){
        try{
            List<String> list=realWarehouseService.querySpecialWarehouseList();
            return ResponseMsg.SUCCESS.buildMsg(list);
        }catch (RomeException e){
            log.error(e.getMessage(), e);
            return ResponseMsg.FAIL.buildMsg(e.getCode(), e.getMessage());
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ResponseMsg.EXCEPTION.buildMsg(e.getMessage());
        }
    }
}
