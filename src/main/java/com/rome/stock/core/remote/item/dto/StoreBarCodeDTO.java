package com.rome.stock.core.remote.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "门店条码信息 DTO")
@Data
public class StoreBarCodeDTO {

    @ApiModelProperty(value = "渠道编码", example = "107U_100", required = true)
    private String channelCode;

    @ApiModelProperty(value = "商品条码", example = "6912312", required = true)
    private String barCode;

}