package com.rome.stock.core.remote.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "查询请求参数 DTO")
@Data
public class QueryRequestDTO {

    @ApiModelProperty(value = "是否发布，1,有销售权限的", example = "1", required = true)
    private String isPublish="1";

    @ApiModelProperty(value = "门店条码信息列表", required = true)
    private List<StoreBarCodeDTO> storeBarCodeDTOS;

}