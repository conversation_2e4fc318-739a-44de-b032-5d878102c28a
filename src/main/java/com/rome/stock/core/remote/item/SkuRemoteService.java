package com.rome.stock.core.remote.item;

import com.github.pagehelper.PageInfo;
import com.rome.arch.core.clientobject.Response;
import com.rome.stock.core.api.dto.SkuOfStockRecord;
import com.rome.stock.core.api.dto.frontrecord.SkuInfo;
import com.rome.stock.core.api.dto.supplier.SupplierSkuStockDTO;
import com.rome.stock.core.remote.base.dto.SkuInfoExtNewDTO;
import com.rome.stock.core.remote.item.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 类SkuRemoteService的实现描述：商品信息获取
 *
 * <AUTHOR> 2019/5/23 11:50
 */
@FeignClient(value = "item-core", url = "${feignClient.item.core-service.url:}")
public interface SkuRemoteService {

    /**
     * 根据商品id和分类ID查询商品(只查询100条)
     * @param paramList
     * @param pageNum
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skusBySkuIdAndMerchantId",method = RequestMethod.POST)
    Response<PageInfo<SkuInfoExtDTO>> skusBySkuIdAndMerchantId(@RequestBody List<QuerySkuParamExtDTO> paramList, @RequestParam("pageNum") Integer pageNum);

    /**
     * 根据商品code查询商品(只查询100条)
     * @param paramList
     * @param pageNum
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skuListBySkuCodeAndMerchantId",method = RequestMethod.POST)
    Response<PageInfo<SkuInfoExtDTO>> skuListBySkuCodeAndMerchantId(@RequestBody List<QuerySkuParamExtDTO> paramList, @RequestParam("pageNum") Integer pageNum);


    /**
     * 根据barCode查询商品 <br>
     *
     * <AUTHOR> 2019/9/3
     * @param barCodes
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skusByBarCode", method = RequestMethod.POST)
    Response<List<SkusByBarCodeExtDTO>> skuByBarCodes(@RequestBody List<String> barCodes);

    @RequestMapping(value = "/api/v1/item/external/skusByBarCodeAndStoreCode", method = RequestMethod.POST)
    Response<List<SkusByBarCodeExtDTO>> skusByBarCodeAndStoreCode(@RequestBody QueryRequestDTO queryRequestDTO);




    /**
     * 批量通过SkuId和单位Code单位查询基础单位换算关系(只查询100条)
     * @param paramList
     * @param pageNum
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/unitsBySkuIdAndUnitCodeAndMerchantId",method = RequestMethod.POST)
    Response<PageInfo<SkuUnitExtDTO>> unitsBySkuIdAndUnitCodeAndMerchantId(@RequestBody List<SkuUnitParamExtDTO> paramList, @RequestParam("pageNum") Integer pageNum);

    /**
     * 批量通过商品组合Id集合查询商品组合关系(只查询100条)
     * @param paramList
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skuCombinesByCombineSkuIdAndMerchantId",method = RequestMethod.POST)
    Response<Map<Long, List<SkuCombineExtDTO>>> skuCombinesByCombineSkuIdAndMerchantId(@RequestBody List<SkuCombineParamExtDTO> paramList);

    /**
     * 通过skuId查询sku信息
     * @param extDTO
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skuBySkuIdAndMerchantId",method = RequestMethod.POST)
    Response<SkuInfoExtDTO> skuBySkuIdAndMerchantId(@RequestBody QuerySkuParamExtDTO extDTO);

    /**
     * 通过名称模糊查询sku信息
     * @param name
     * @return
     */
    @RequestMapping(value = "/api/v1/item/sku/by-name/{name}",method = RequestMethod.GET)
    Response<List<SkuOfStockRecord>> getSkusByName(@RequestParam("name") String name);

    /**
     * 查询sku所有单位
     * @param paramList
     * @param pageNum
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/unitsBySkuCodeAndMerchantId",method = RequestMethod.POST)
    Response<List<SkuUnitExtDTO>> querySkuUnits(@RequestBody List<QuerySkuParamExtDTO> paramList, @RequestParam("pageNum") Integer pageNum);

    /**
     *
     * 批量通过SkuId和单位名称查询基础单位换算关系
     * @param paramList
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/unitsBySkuIdAndUnitNameAndMerchantId",method = RequestMethod.POST)
    Response<PageInfo<SkuUnitExtDTO>> unitsBySkuIdAndUnitNameAndMerchantId(@RequestBody List<SkuUnitParamExtDTO> paramList);


    /**
     * 查询sku是否需要质检
     * @param factoryCode
     * @param skuCodes
     * @return
     */
    @RequestMapping(value = "/api/v1/factory/sku/factorySkuRequireds",method = RequestMethod.POST)
    Response<List<SkuQualityDTO>> factorySkuRequireds(@RequestParam("factoryCode") String factoryCode ,@RequestBody List<String> skuCodes);

    /**
     * 根据门店编号和skuCodes查询门店所对应的进货权限（从SAP查询）
     * @param storeCode ,经三方沟通，该字段实际上就是工厂编码
     * @param skuCodes
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/getStoreAccessFromSAPBySkuCodesAndStoreCode",method = RequestMethod.POST)
    Response<List<StorePurchaseAccessDTO>> getStoreAccessFromSAPBySkuCodesAndStoreCode(@RequestParam("storeCode") String storeCode ,@RequestParam("skuCodes") List<String> skuCodes);

    /**
     * 根据门店编号和skuCodes查询门店所对应的进货权限
     * @param paramList
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/getStorePurchaseAccessBySkuCodesAndStoreCode",method = RequestMethod.POST)
	Response<List<StorePurchaseDTO>> getStorePurchaseAccessBySkuCodesAndStoreCode(@RequestBody List<StorePurchaseParamDTO> storePurchaseParamDTOList);

    /**
     * 批量通过skuCode、渠道编码、销售单位集合获取Sku/上下架/价格/单位信息
     * @param storeSaleParamDTOList
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skusByManyParam",method = RequestMethod.POST)
	Response<PageInfo<StoreSalePowerDTO>> skusByManyParam(@RequestBody List<StoreSaleParamDTO> storeSaleParamDTOList);

    /**
     * 批量通过skuCodes与skuUintType查询sku单位信息
     * @param skuUintType
     * @param skuCodeList
     * skuCode为Long类型，商品那边定义的
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/skuUnitBySkuCodesAndSkuUintType",method = RequestMethod.POST)
    Response<List<SkuUnitExtDTO>> skuUnitBySkuCodesAndSkuUintType(@RequestBody List<Long> skuCodeList,@RequestParam("skuUintType") Integer skuUintType);

    /**
     * @Description: 通过门店Code分页查询门店有进货权商品 <br>
     *
     * <AUTHOR> 2020/2/6
     * @param storeCodes
     * @return 
     */
    @RequestMapping(value = "/api/v1/item/external/purchaseAccessSkusByStoreCode",method = RequestMethod.POST)
    Response<PageInfo<SkuInfoExtNewDTO>> purchaseAccessSkusByStoreCode(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize, @RequestBody List<String> storeCodes);

    /**
     * @Description: 批量通过门店号查询有进货权限的商品并集 <br>
     *  原来为 /api/v1/item/external/purchaseAccessSkuByStoreCodes
     * <AUTHOR> 2020/2/13
     * @param storeCodes
     * @return 
     */
    @RequestMapping(value = "/api/v1/item/external/purchaseAccessSkuByStoreCodesAndSkuIds",method = RequestMethod.POST)
    Response<PageInfo<PurchaseAccessResultDTO>> purchaseAccessSkuByStoreCodesAndSkuIds(@RequestBody PurchaseAccessSearchParamDTO purchaseAccessSearchParamDTO);


    /**
     * 根据商家id,skuCode,unitCode查询商品信息及组合品子品信息（如果是组合品）
     *
     * <AUTHOR> 2020/3/19
     * @param params
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId",method = RequestMethod.POST)
    Response<List<SkuUnitAndCombineExtDTO>> querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId(@RequestBody List<SkuInfo> params);

    /**
     * 根据商家id,skuCode,unitCode查询商品信息及组合品子品信息（如果是组合品）
     *
     * <AUTHOR> 2020/3/19
     * @param params
     * @return
     */
    @RequestMapping(value = "/api/v1/item/external/querySkuUnitAndCombinesBySkuIdAndUnitCodeAndMerchantId",method = RequestMethod.POST)
    Response<List<SkuUnitAndCombineExtDTO>> querySkuUnitAndCombinesBySkuIdAndUnitCodeAndMerchantId(@RequestBody List<SkuInfo> params);

	/**
	 * 根据渠道编码和skuIds 调用商品接口查询渠道销售商品
	 * @param channelPurchaseParamDTO
	 * @return
	 */
    @RequestMapping(value = "/api/v1/item/xuanTian/getSkuInfoByChannelCodes",method = RequestMethod.POST)
	Response<PageInfo<PurchaseAccessResultDTO>> getSkuInfoByChannelCodes(@RequestBody ChannelPurchaseParamDTO channelPurchaseParamDTO);

    /**
     * 根据渠道编码和商品及单位获取价格及换算比
     * @param pageNum
     * @param paramDtos
     * @return
     */
    @RequestMapping(value = "api/v1/item/external/skuSalePurchasePricesBySkuCodesAndChannelCodeAndUnitCode",method = RequestMethod.POST)
    Response<PageInfo<ChannelSkuPurchasePriceDTO>> skuSalePurchasePricesBySkuCodesAndChannelCodeAndUnitCode(@RequestParam("pageNum") Integer pageNum, @RequestBody List<StoreSaleParamDTO> paramDtos);

    /**
     * 根据单位名称转换为单位code
     * @param unitName
     * @return
     */
    @RequestMapping(value="/api/v1/item/external/unitCodeByUnitName",method = RequestMethod.GET)
    Response<String> unitCodeByUnitName(@RequestParam("unitName") String unitName);
    
    /**
     * 根据商品code列表以及skuName批量查询商品信息(最多查询100条)
     * skuName是模糊查询
     * 
     * @param skuCodes
     * @param skuName
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/api/v1/item/sku/external/skuCodesAndSkuName",method = RequestMethod.POST)
    Response<PageInfo<SupplierSkuStockDTO>> skuCodesAndSkuName(@RequestBody List<String> skuCodes, @RequestParam("skuName") String skuName, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);

    /**
     * 批量查询BOMSku的数据
     * @param paramDTOS
     * @return
     * @deprecated  商品服务已改成从cmp下发 此接口作废
     */
    @PostMapping(value = "/api/v1/bomSku/batchSelectBomSkuAndDetail")
    @Deprecated
    Response<List<BomSkuBatchDTO>> batchSelectBomSkuAndDetail(@RequestBody List<BomSkuParamDTO> paramDTOS);

    /**
     * 查询批量查询BOMSku的数据
     * @param skuCodes
     * @return
     */
    @PostMapping(value ="/api/v1/skuBom/external/selectSkuBomAndDetailBySkuCodes")
    Response<List<BomSkuBatchDTO>> selectSkuBomAndDetailBySkuCodes(@RequestBody QuerySelectBomDTO querySelectBomDTO );

    @RequestMapping(value = "/api/v1/item/external/skuListBySkuCodes",method = RequestMethod.POST)
    Response<List<SkuInfoExtDTO>> skuListBySkuCodes(@RequestBody List<String> skuCodeList);

	/**
     * 批量通过SkuCode、渠道编号、销售单位Code获取销售价格
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/v1/item/external/salePricesBySkuCodesAndChannelCodesAndSaleUnitCodes")
    Response<PageInfo<ChannelSkuPurchasePriceDTO>> salePricesBySkuCodesAndChannelCodesAndSaleUnitCodes(@RequestBody List<QuerySalePurchasePricesExtDTO> dto);


    /**
     * 批量通过skuCode和单位code查询基础单位换算关系
     *
     * @param pageNum 页面num
     * @param list    列表
     * @return {@link Response<PageInfo<SkuUnitExtDTO>>}
     */
    @PostMapping("/api/v1/item/external/unitsBySkuCodeAndUnitCodeAndMerchantId")
    Response<PageInfo<SkuUnitExtDTO>> unitsBySkuCodeAndUnitCodeAndMerchantId(@RequestParam("pageNum") Integer pageNum, @RequestBody List<UnitCodeExtParamDTO> list);

    /**
     * 根据skuCodes查询sku属性
     * @param skuCodeList
     * @return
     */
    @RequestMapping(value = "/api/v1/item/skuAttribute/getSkuAttributeBySkuCodes",method = RequestMethod.POST)
    Response<List<SkuAttributeInfoDTO>> getSkuAttributeBySkuCodes(@RequestBody List<String> skuCodeList);


    /**
     * 批量查询sku供应商信息
     */
    @PostMapping(value = "/api/v1/item/sku/external/skuSupplierList/skuCodes")
    Response<List<SkuSupplierDTO>> skuSupplierList(@RequestBody List<String> skuCodes);

    /**
     * 根据物料类型查询sku接口
     * @return
     */
    @PostMapping(value = "/api/v1/item/sku/pageSkuInfoBySkuType")
    Response<PageInfo<Z016SkuCodeDTO>> searchSkuCodeBySkuType(@RequestParam("pageNum") Integer pageNum,@RequestParam("pageSize") Integer pageSize, @RequestParam("skuTypeCode") String skuTypeCode);

    @RequestMapping(value = "/api/v1/item/skuAttribute/getMdmSkuInfoBySkuCodes",method = RequestMethod.POST)
    Response<List<SkuInfoExtDTO>> getMdmSkuInfoBySkuCodes(@RequestBody List<String> skuCodeList);

    /**
     * 根据skuCodes查询sku属性
     * @param skuCodeList
     * @return
     */
    @RequestMapping(value = "/api/v1/skuWarehouseUnit/selectSkuWarehouseUnitBySkuCode",method = RequestMethod.POST)
    Response<List<SkuAllUnitDTO>> selectSkuWarehouseUnitBySkuCode(@RequestBody List<String> skuCodeList);


}
