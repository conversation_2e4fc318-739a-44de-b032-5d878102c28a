package com.rome.stock.core.remote.sap.dto.sapDto930;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description do单据表
 * <AUTHOR>
 * @date 2020-08-07
 */
@Data
@ApiModel("DO单据表")
public class DoOrderDTO {

    @ApiModelProperty("唯一主键")
    private Long id;

    @ApiModelProperty("do父单号")
    private String parentRecordCode;

    @ApiModelProperty("do单号")
    private String recordCode;

    @ApiModelProperty("sap单号")
    private String sapOrderCode;

    @ApiModelProperty("交易单号")
    private String tradeOrderCode;

    @ApiModelProperty("外部单号")
    private String outOrderCode;

    @ApiModelProperty("入库单号")
    private String inRecordCode;

    @ApiModelProperty("退货原始单号")
    private String originalOrderCode;

    @ApiModelProperty("业务类型：1:出库单")
    private Integer businessType;

    @ApiModelProperty("订单业务类型：1:tob 2:toc")
    private Integer orderBusinessType;

    @ApiModelProperty("do状态 toc 0已新建 拼团中 10待发货 11已发货 15已完成 2已取消 21已关闭 22已拒收 tob 0已新建 10待发货 11已出库 15已完成 2已取消 21已关闭")
    private Integer recordStatus;

    @ApiModelProperty("单据类型")
    private Integer recordType;

    @ApiModelProperty("用户id")
    private String userCode;

    @ApiModelProperty("用户昵称")
    private String userName;

    @ApiModelProperty("用户手机号")
    private String mobile;

    @ApiModelProperty("外部用户id")
    private String outUserCode;

    @ApiModelProperty("外部用户昵称")
    private String outUserName;

    @ApiModelProperty("外部用户手机号")
    private String outMobile;

    @ApiModelProperty("门店编号")
    private String shopCode;

    @ApiModelProperty("门店性质：1 直营，3 加盟，5，加盟托管")
    private Integer shopType;

    @ApiModelProperty("配货类型 1普通配货 2指定仓库配货")
    private Integer requireType;

    @ApiModelProperty("商家id")
    private Long merchantId;

    @ApiModelProperty("鲲鹏渠道code")
    private String kpChannelCode;

    @ApiModelProperty("系统渠道code")
    private String channelCode;

    @ApiModelProperty("渠道类型")
    private Long channelType;

    @ApiModelProperty("实仓id")
    private Long realWarehouseId;

    @ApiModelProperty("工厂代码")
    private String factoryCode;

    @ApiModelProperty("外部仓库编码")
    private String realWarehouseCode;

    @ApiModelProperty("虚拟仓库id")
    private Long virtualWarehouseId;

    @ApiModelProperty("虚仓编码")
    private String virtualWarehouseCode;

    @ApiModelProperty("支付状态: 0未支付 1:部分支付 2:已支付")
    private Integer payStatus;

    @ApiModelProperty("支付方式")
    private Integer payMethod;

    @ApiModelProperty("支付时间")
    private Date payTime;

    @ApiModelProperty("尾款支付时间")
    private Date tailPayTime;

    @ApiModelProperty("订单备注(买家)")
    private String orderRemarkUser;

    @ApiModelProperty("订单备注(卖家)")
    private String orderRemarkSale;

    @ApiModelProperty("外部系统下单时间")
    private Date outCreateTime;

    @ApiModelProperty("配送方式")
    private String deliveryMethod;

    @ApiModelProperty("物流/快递公司编码")
    private String logisticsCode;

    @ApiModelProperty("物流/快递公司名称")
    private String logisticsName;

    @ApiModelProperty("tms派车单号")
    private String tmsRecordCode;

    @ApiModelProperty("是否活动订单 0:否 1:是")
    private Integer isTopicOrder;

    @ApiModelProperty("活动id，多个逗号隔开")
    private String topicIds;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("实际支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty("支付优惠金额")
    private BigDecimal payDiscountAmount;

    @ApiModelProperty("运费")
    private BigDecimal freight;

    @ApiModelProperty("下单来源")
    private Integer orderSource;

    @ApiModelProperty("履约方式")
    private Integer exerciseMethod;

    @ApiModelProperty("履约时效(小时)")
    private Integer exerciseAging;

    @ApiModelProperty("履约成本(元)")
    private BigDecimal exerciseCost;

    @ApiModelProperty("预计发货时间")
    private Date expectDeliveryTime;

    @ApiModelProperty("实际发货时间")
    private Date actualDeliveryTime;

    @ApiModelProperty("承诺送达时间")
    private Date promiseArriveTime;

    @ApiModelProperty("实际送达时间")
    private Date actualArriveTime;

    @ApiModelProperty("取消原因")
    private String reasons;

    @ApiModelProperty("取消时间")
    private Date relinquishTime;

    @ApiModelProperty("是否发生售后 0:否 1:是")
    private Integer hasAfterSales;

    @ApiModelProperty("是否预售单 0:否 1:是")
    private Integer isPresellOrder;

    @ApiModelProperty("是否紧急订单 0:否 1:是")
    private Integer isExigencyOrder;

    @ApiModelProperty("是否母品订单 0:否 1:是")
    private Integer isGroupSkuOrder;

    @ApiModelProperty("是否生成采购单 0:否 1:是")
    private Integer hasPurchaseOrder;

    @ApiModelProperty("是否截停 0:否 1:是")
    private Integer isStop;

    @ApiModelProperty("截停原因")
    private String stopReason;

    @ApiModelProperty("是否问题订单 0:否 1:是")
    private Integer isProblem;

    @ApiModelProperty("问题类型")
    private Integer problemType;

    @ApiModelProperty("问题原因")
    private String problemReason;

    @ApiModelProperty("插旗备注")
    private String flagRemark;

    @ApiModelProperty("插旗时间")
    private Date flagTime;

    @ApiModelProperty("插旗人工号")
    private String flagUser;

    @ApiModelProperty("取消插旗备注")
    private String cancelFlagRemark;

    @ApiModelProperty("取消插旗时间")
    private Date cancelFlagTime;

    @ApiModelProperty("取消插旗人工号")
    private String cancelFlagUser;

    @ApiModelProperty("是否客服插旗 0:否 1:是")
    private Integer isService;

    @ApiModelProperty("锁定库存状态， 0:无需锁定 1:未锁定 2:部分锁定 3:全锁定")
    private Integer lockStockStatus;

    @ApiModelProperty("是否系统报缺 0:否 1:是")
    private Integer isSystemStockout;

    @ApiModelProperty("是否实物报缺 0:否 1:是")
    private Integer isWmsStockout;

    @ApiModelProperty("是否有拆单 0:否 1:是")
    private Integer hasSplit;

    @ApiModelProperty("是否展示do 0:否 1:是")
    private Integer isShow;

    @ApiModelProperty("拆单时间")
    private Date splitTime;

    @ApiModelProperty("合单指纹")
    private String mergeFingerPrint;

    @ApiModelProperty("合单状态， 0:无需合单 1:待合单 2:合单成功")
    private Integer mergeOrderStatus;

    @ApiModelProperty("合单时间")
    private Date mergeOrderTime;

    @ApiModelProperty("是否叶子单 0:否 1:是")
    private Integer isLeaf;

    @ApiModelProperty("是否包含代销品 0:否 1:是")
    private Integer hasProxySku;

    @ApiModelProperty("是否需要寻源分配(0: 无需分配， 1.需要分配 2. 分配完成)")
    private Integer isNeedAllot;

    @ApiModelProperty("期望收货时间")
    private Date replenishTime;

    @ApiModelProperty("同步派车状态 0:无需同步 1:待同步 2:已同步")
    private Integer syncTmsbStatus;

    @ApiModelProperty("派车结果状态：0-无需派车 1-派车中 2-已下发派车")
    private Integer syncDispatchStatus;

    @ApiModelProperty("同步采购状态 0:无需同步 1:待同步(do) 2:已同步(do) 11:待同步(收货) 12:已同步(收货) 21:待同步(拒收) 22:已同步(拒收)")
    private Integer syncPurchaseStatus;

    @ApiModelProperty("同步协同状态 0:无需同步 1:待同步(do) 2:已同步(do) 11:待同步(收货) 12:已同步(收货)")
    private Integer syncSynergyStatus;

    @ApiModelProperty("同步库存状态 0无需同步 1待同步 2已同步")
    private Integer syncStockStatus;

    @ApiModelProperty("do同步wms状态：0-无需同步 1-未同步 2-已同步")
    private Integer syncWmsStatus;

    @ApiModelProperty("同步交易状态 0-无需同步 1-待同步 2-已完成 11-待同步(门店退货) 12-已完成(门店退货) 21-待同步(差异) 22-已完成(差异) 31-待同步(发货) 31-已完成(发货) 41-待同步(拒收) 42-已完成(拒收)")
    private Integer syncTradeStatus;

    @ApiModelProperty("同步外卖接受器状态 0-无需同步 1-待同步 2-已完成")
    private Integer syncTakeawayStatus;

    @ApiModelProperty("同步派车时间")
    private Date syncTmsbTime;

    @ApiModelProperty("同步采购时间")
    private Date syncPurchaseTime;

    @ApiModelProperty("同步库存发货时间")
    private Date syncStockTime;

    @ApiModelProperty("同步wms时间")
    private Date syncWmsTime;

    @ApiModelProperty("创建人")
    private Long creator;

    @ApiModelProperty("更新人")
    private Long modifier;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("是否可用：0-否，1-是")
    private Integer isAvailable;

    @ApiModelProperty("是否逻辑删除：0-否，1-是")
    private Integer isDeleted;

    @ApiModelProperty("版本号:默认0，每次更新1")
    private Integer versionNo;

    @ApiModelProperty("租户id")
    private Long tenantId;

    @ApiModelProperty("业务应用id")
    private String appId;

    @ApiModelProperty("加货原因 0默认 1门店备货 2活动加货 3团购加货 4门店加货 10云店")
    private Integer addReason;

    @ApiModelProperty("拼团标识")
    private Boolean specialType=false;

    @ApiModelProperty("DO明细")
    private List<DoDetailDTO> detailDTOList;
}