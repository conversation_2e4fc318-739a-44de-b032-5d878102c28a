package com.rome.stock.core.remote.item.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.rome.arch.core.clientobject.Response;
import com.rome.arch.core.exception.CodeMessage;
import com.rome.arch.core.exception.RomeException;
import com.rome.stock.common.enums.BatchStockTypeVO;
import com.rome.scm.common.monitor.CustomMonitorFacade;
import com.rome.stock.common.utils.ListUtil;
import com.rome.stock.common.utils.validate.ResponseValidateUtils;
import com.rome.stock.core.api.dto.SkuOfStockRecord;
import com.rome.stock.core.api.dto.frontrecord.SkuInfo;
import com.rome.stock.core.api.dto.supplier.SupplierSkuStockDTO;
import com.rome.stock.core.common.RedisUtil;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.constant.ParamValidator;
import com.rome.stock.core.constant.ResponseMsg;
import com.rome.stock.core.constant.StockCoreConsts;
import com.rome.stock.core.domain.entity.frontrecord.AbstractFrontRecordDetail;
import com.rome.stock.core.remote.item.SkuRemoteService;
import com.rome.stock.core.remote.item.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_DOWN;

/**
 * 类SkuFacade的实现描述：商品中心接口
 *
 * <AUTHOR> 2019/5/23 16:12
 */
@Slf4j
@Component
public class SkuFacade {

    @Resource
    private SkuRemoteService skuRemoteService;

    private ParamValidator validator = ParamValidator.INSTANCE;

    private final Integer PAGE = 1;

    private final Integer PAGESIZE = 100;

    public final String Z016_SKU_CODES_CACHE = "z016Codes";

    public final long Z016_SKU_CODES_CACHE_EXPIRE_TIME = 30*60;

    @Resource
    private RedisUtil redisUtil;


    @Value("${sku.merchantId}")
    private Long defaultMerchantId;

    /**
     * 根据商品id和分类ID查询商品
     * @param skuIds
     * @return
     */
    public List<SkuInfoExtDTO> skusBySkuId(List<Long> skuIds ){
        return  this.skusBySkuId(skuIds , defaultMerchantId);
    }

    /**
     * 根据商品id和分类ID查询商品
     * @param skuIds
     * @return
     */
    public List<SkuInfoExtDTO> skusBySkuId(List<Long> skuIds ,Long merchantId ){
        //log.error("请求item-skusBySkuId, 参数:" + JSON.toJSONString(skuIds));
    	long time = System.currentTimeMillis();
        List<SkuInfoExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuIds)) {
            List<QuerySkuParamExtDTO> paramList = new ArrayList<>();
            for (Long skuId : skuIds){
                QuerySkuParamExtDTO dto = new QuerySkuParamExtDTO();
                dto.setMerchantId(merchantId);
                dto.setSkuId(skuId);
                paramList.add(dto);
            }
            List<List<QuerySkuParamExtDTO>> splitList = splitList(paramList);
            for (List<QuerySkuParamExtDTO> tempList : splitList) {
                Response<PageInfo<SkuInfoExtDTO>> rep = skuRemoteService.skusBySkuIdAndMerchantId(tempList, PAGE);
                if (! validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    resultList.addAll(rep.getData().getList());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-skusBySkuId, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuIds));
        }
        return resultList;
    }

    /**
     * 根据门店编号和skuCodes查询门店所对应的进货权限（从SAP查询）
     * @param storeCode ,经三方沟通，该字段实际上就是工厂编码
     * @param skuCodeList
     * @return
     */
    public   List<StorePurchaseAccessDTO> getStoreAccessFromSAPBySkuCodesAndStoreCode(String storeCode, List<String> skuCodeList) {
        log.info("查询下市商品接口入参 ：工厂={},code={}" , storeCode ,  JSON.toJSONString(skuCodeList));
        //新建列表，不影响入参
        List<String> skuCodes = new ArrayList<>(skuCodeList);
        int maxSize = 200;
        int count = skuCodes.size() / maxSize;
        if (skuCodes.size() % maxSize != 0) {
            count = count + 1;
        }
        List<StorePurchaseAccessDTO> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            int size = maxSize > skuCodes.size() ? skuCodes.size() : maxSize;
            Response<List<StorePurchaseAccessDTO>> res = skuRemoteService.getStoreAccessFromSAPBySkuCodesAndStoreCode(storeCode, skuCodes.subList(0, size));
            if (!validator.validResponse(res)) {
                throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
            } else {
                result.addAll(res.getData());
            }
            skuCodes.subList(0, size).clear();
        }
        log.info("查询下市商品接口结果：" + JSON.toJSONString(result));
        return result;
    }


    /**
     * 根据商品Code查询商品
     * @param skuCodes
     * @return
     */
    public List<SkuInfoExtDTO> skusBySkuCode(List<String> skuCodes){
        return this.skusBySkuCode(skuCodes,defaultMerchantId);
    }
    /**
     * 根据商品Code查询商品
     * @param skuCodes
     * @return
     */
    public List<SkuInfoExtDTO> skusBySkuCode(List<String> skuCodes,Long merchantId){
        long time = System.currentTimeMillis();
        List<SkuInfoExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuCodes)) {
            List<QuerySkuParamExtDTO> paramList = new ArrayList<>();
            for (String skuCode : skuCodes){
                QuerySkuParamExtDTO dto = new QuerySkuParamExtDTO();
                dto.setMerchantId(merchantId);
                dto.setSkuCode(skuCode);
                paramList.add(dto);
            }
            List<List<QuerySkuParamExtDTO>> splitList = splitList(paramList);
            for (List<QuerySkuParamExtDTO> tempList : splitList) {
                Response<PageInfo<SkuInfoExtDTO>> rep = skuRemoteService.skuListBySkuCodeAndMerchantId(tempList, PAGE);
                if (! validator.validResponse(rep)) {
                    log.error("请求商品中心接口异常，参数 ==> {}，响应 ==> {}", JSONObject.toJSONString(tempList), rep == null ? "响应结果为空" : JSONObject.toJSONString(rep));
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    if (rep.getData().getList().size() != tempList.size()) {
                        Set<String> distinctCodes = new HashSet<>(tempList.size()*100/75+1);
                        for(QuerySkuParamExtDTO dto: tempList){
                            distinctCodes.add(dto.getSkuCode());
                        }
                        if(rep.getData().getList().size() != distinctCodes.size()){
                            this.sendMessage(JSON.toJSONString(tempList),"根据code查询商品接口异常，返回物料数量与查询数量不一致，详情："+JSON.toJSONString(rep),"/api/v1/item/external/skuListBySkuCodeAndMerchantId");
                            log.error("请求商品中心接口异常,返回物料数量与查询数量不一致，参数 ==> "+JSONObject.toJSONString(tempList)+"，响应 ==> "+ JSONObject.toJSONString(rep));
                        }
                    }
                    resultList.addAll(rep.getData().getList());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-skusBySkuCode, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuCodes));
        }
        return resultList;
    }

    /**
     * 根据商品barCode查询商品
     * @param barCodes 根据69码
     * @return
     */
    public List<SkusByBarCodeExtDTO> skusByBarCode(List<String> barCodes){
        long time = System.currentTimeMillis();
        List<SkusByBarCodeExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(barCodes)) {
            List<List<String>> splitList = this.splitList(barCodes);
            for (List<String> tempList : splitList) {
                tempList = tempList.stream().distinct().collect(Collectors.toList());
                Response<List<SkusByBarCodeExtDTO>> rep = skuRemoteService.skuByBarCodes(tempList);
                if (! validator.validResponse(rep)) {
                    log.error("请求商品中心接口异常，参数 ==> {}，响应 ==> {}", JSONObject.toJSONString(tempList), rep == null ? "响应结果为空" : JSONObject.toJSONString(rep));
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    //校验barcode是否重复
                    HashSet<String> barCodeSet = new HashSet<>();
                    for (SkusByBarCodeExtDTO ext : rep.getData()) {
                        if (!barCodeSet.add(ext.getBarCode())) {
                            this.sendMessage(JSON.toJSONString(tempList),"请求接口异常，条码不唯一,sku条形码："+ext.getBarCode()+"; 详情："+JSON.toJSONString(rep),"/api/v1/item/external/skusByBarCode");
                            throw new RomeException(ResponseMsg.EXCEPTION.getCode(), ext.getBarCode() + "条码不唯一");
                        }
                    }
                    resultList.addAll(rep.getData());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求item-skusByBarCode, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(barCodes));
        }
        return resultList;
    }


    /**
     * 同一个69码可能有多个商品编码，查销售权限的商品
     * @param storeCode
     * @param barCodes
     * @return
     */
    public List<SkusByBarCodeExtDTO> skusByBarCodeAndStoreCode(String storeCode,List<String> barCodes){
        long time = System.currentTimeMillis();
        List<SkusByBarCodeExtDTO> resultList = new ArrayList<>();
        QueryRequestDTO queryRequestDTO = new QueryRequestDTO();
        List<StoreBarCodeDTO> storeBarCodeDTOS = new ArrayList<>();
        for (String barCode : barCodes){
            StoreBarCodeDTO storeBarCodeDTO = new StoreBarCodeDTO();
            storeBarCodeDTO.setBarCode(barCode);
            storeBarCodeDTOS.add(storeBarCodeDTO);
        }
        queryRequestDTO.setStoreBarCodeDTOS(storeBarCodeDTOS);
        if(CollectionUtils.isNotEmpty(barCodes)) {
            List<List<String>> splitList = this.splitList(barCodes);
            for (List<String> tempList : splitList) {
                tempList = tempList.stream().distinct().collect(Collectors.toList());
                Response<List<SkusByBarCodeExtDTO>> rep = skuRemoteService.skusByBarCodeAndStoreCode(queryRequestDTO);
                if (! validator.validResponse(rep)) {
                    log.error("请求商品中心接口:skusByBarCodeAndStoreCode异常，参数 ==> {}，响应 ==> {}", JSONObject.toJSONString(tempList), rep == null ? "响应结果为空" : JSONObject.toJSONString(rep));
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    //校验barcode是否重复
                    HashSet<String> barCodeSet = new HashSet<>();
                    for (SkusByBarCodeExtDTO ext : rep.getData()) {
                        if (!barCodeSet.add(ext.getBarCode())) {
                            this.sendMessage(JSON.toJSONString(tempList),"请求skusByBarCodeAndStoreCode接口异常，条码不唯一,sku条形码："+ext.getBarCode()+"; 详情："+JSON.toJSONString(rep),"/api/v1/item/external/skusByBarCode");
                            throw new RomeException(ResponseMsg.EXCEPTION.getCode(), ext.getBarCode() + "条码不唯一");
                        }
                    }
                    resultList.addAll(rep.getData());
                }
            }
        }
        return resultList;
    }


    /**
     * (基于对象)
     * 批量通过SkuId和单位Code单位查询基础单位换算关系
     * @param paramList
     * @return
     */
    public List<SkuUnitExtDTO> unitsBySkuIdAndUnitCode(List<SkuUnitParamExtDTO> paramList){
        return this.unitsBySkuIdAndUnitCode(paramList,defaultMerchantId);
    }
    /**
     * (基于对象)
     * 批量通过SkuId和单位Code单位查询基础单位换算关系
     * @param paramList
     * @return
     */
    public List<SkuUnitExtDTO> unitsBySkuIdAndUnitCode(List<SkuUnitParamExtDTO> paramList,Long merchantId){
        long time = System.currentTimeMillis();
        List<SkuUnitExtDTO> resultList = new ArrayList<>();
        Response<PageInfo<SkuUnitExtDTO>> rep = null;
        if(CollectionUtils.isNotEmpty(paramList)) {
            if(null!=merchantId){
                for (SkuUnitParamExtDTO dto : paramList){
                    dto.setMerchantId(merchantId);
                }
            }
            List<List<SkuUnitParamExtDTO>> splitList = splitList(paramList);
            for (List<SkuUnitParamExtDTO> tempList : splitList) {
                rep = skuRemoteService.unitsBySkuIdAndUnitCodeAndMerchantId(tempList, PAGE);
                if (!validator.validResponse(rep) || rep.getData()== null || rep.getData().getList().size() == 0) {
                    if(rep.getData()== null || rep.getData().getList().size() == 0){
                        this.sendMessage(JSONObject.toJSONString(tempList),"查询基础单位换算关系接口异常，返回数据为空；详情："+JSONObject.toJSONString(rep),"/api/v1/item/external/unitsBySkuIdAndUnitCodeAndMerchantId");
                    }
                    log.error("调用商品中心接口失败,参数==> {"+JSONObject.toJSONString(tempList)+"}");
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC+":unitsBySkuIdAndUnitCodeAndMerchantId");
                }else{
                    resultList.addAll(rep.getData().getList());
                }
            }
        }
        //根据skuCode进行分组，检查每个sku对应返回的与基本单位的转换比是否有多个，如果有多个就报错
        Map<String,List<SkuUnitExtDTO>> groupMap=resultList.stream().collect(Collectors.groupingBy(t->t.getSkuCode()));
        for (Map.Entry<String, List<SkuUnitExtDTO>> mapEntry : groupMap.entrySet()) {
            Long count = mapEntry.getValue().stream().map(SkuUnitExtDTO::getScale).distinct().count();
            if(count>1){
                this.sendMessage(JSONObject.toJSONString(paramList),"查询基础单位换算关系接口异常，发现存在多个基本单位转换比skuCode："+mapEntry.getKey()+"; 详情："+JSONObject.toJSONString(rep),"/api/v1/item/external/unitsBySkuIdAndUnitCodeAndMerchantId");
                throw new RomeException(ResCode.STOCK_ERROR_5026, "调用商品中心接口查询，发现存在多个基本单位转换比,skuCode:"+mapEntry.getKey());
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-unitsBySkuIdAndUnitCode, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(paramList));
        }
        return resultList;
    }

    /**
     * (基于前置单明细)
     * 批量通过SkuId和单位Code单位查询基础单位换算关系
     * @param details
     * @return
     */
    public List<SkuUnitExtDTO> unitsByFrontDetail(List<? extends AbstractFrontRecordDetail> details) {
        return this.unitsByFrontDetail(details,defaultMerchantId);
    }
    /**
     * (基于前置单明细)
     * 批量通过SkuId和单位Code单位查询基础单位换算关系
     * @param details
     * @return
     */
    public List<SkuUnitExtDTO> unitsByFrontDetail(List<? extends AbstractFrontRecordDetail> details,Long merchantId){
    	long time = System.currentTimeMillis();
        List<SkuUnitExtDTO> resultList = new ArrayList<>();
        Response<PageInfo<SkuUnitExtDTO>> rep = null;
        List<SkuUnitParamExtDTO> paramList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(details)) {
            paramList = this.coverParamExt(details,merchantId);
            List<List<SkuUnitParamExtDTO>> splitList = splitList(paramList);
            for (List<SkuUnitParamExtDTO> tempList : splitList) {
                rep = skuRemoteService.unitsBySkuIdAndUnitCodeAndMerchantId(tempList, PAGE);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    resultList.addAll(rep.getData().getList());
                }
            }
        }
        //根据skuCode进行分组，检查每个sku对应返回的与基本单位的转换比是否有多个，如果有多个就报错
        Map<String,List<SkuUnitExtDTO>> groupMap=resultList.stream().collect(Collectors.groupingBy(t->t.getSkuCode()));
        for (Map.Entry<String, List<SkuUnitExtDTO>> mapEntry : groupMap.entrySet()) {
            Long count = mapEntry.getValue().stream().map(SkuUnitExtDTO::getScale).distinct().count();
            if(count>1){
                this.sendMessage(JSONObject.toJSONString(paramList),"查询基础单位换算关系接口异常，发现存在多个基本单位转换比skuCode："+mapEntry.getKey()+"; 详情："+JSONObject.toJSONString(rep),"/api/v1/item/external/unitsBySkuIdAndUnitCodeAndMerchantId");
                throw new RomeException(ResCode.STOCK_ERROR_5026, "调用商品中心接口查询，发现存在多个基本单位转换比,skuCode:"+mapEntry.getKey());
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-unitsByFrontDetail, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(details));
        }
        return resultList;
    }

    /**
     * 批量通过商品组合Id集合查询商品组合关系(只查询100条)
     * @param combineSkuIds
     * @return
     */
    public Map<Long, List<SkuCombineExtDTO>> skuCombinesByCombineSkuId(List<Long> combineSkuIds){
        return this.skuCombinesByCombineSkuId(combineSkuIds,defaultMerchantId);
    }
    /**
     * 批量通过商品组合Id集合查询商品组合关系(只查询100条)
     * @param combineSkuIds
     * @return
     */
    public Map<Long, List<SkuCombineExtDTO>> skuCombinesByCombineSkuId(List<Long> combineSkuIds,Long merchantId){
    	long time = System.currentTimeMillis();
        Map<Long, List<SkuCombineExtDTO>> resultMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(combineSkuIds)) {
            List<SkuCombineParamExtDTO> paramList = new ArrayList<>();
            for (Long combineSkuId : combineSkuIds){
                SkuCombineParamExtDTO dto = new SkuCombineParamExtDTO();
                dto.setMerchantId(merchantId);
                dto.setCombineSkuId(combineSkuId);
                paramList.add(dto);
            }
            List<List<SkuCombineParamExtDTO>> splitList = splitList(paramList);
            for (List<SkuCombineParamExtDTO> tempList : splitList) {
                Response<Map<Long, List<SkuCombineExtDTO>>> rep = skuRemoteService.skuCombinesByCombineSkuIdAndMerchantId(tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }
                Map<Long, List<SkuCombineExtDTO>> tempMap = rep.getData();
                for (Long skuId : tempMap.keySet()) {
                    List<SkuCombineExtDTO> resultList = resultMap.get(skuId);
                    // 循环多次可能会查询到同一skuId的组合关系，需要忽略
                    if (null == resultList) {
                        resultList = new ArrayList<>();
                        resultMap.put(skuId, resultList);
                        resultList.addAll(tempMap.get(skuId));
                    }
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-skuCombinesByCombineSkuId, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(combineSkuIds));
        }
        return resultMap;
    }

    /**
     * 前置单转单位转化对象
     * @param details
     * @return
     */
    private List<SkuUnitParamExtDTO> coverParamExt(List<? extends AbstractFrontRecordDetail> details,Long merchantId){
        List<SkuUnitParamExtDTO> paramList = new ArrayList<>();
        for (AbstractFrontRecordDetail detail : details){
            SkuUnitParamExtDTO dto = new SkuUnitParamExtDTO();
            dto.setSkuId(detail.getSkuId());
            dto.setUnitCode(detail.getUnitCode());
            dto.setMerchantId(merchantId);
            paramList.add(dto);
        }
        return paramList;
    }

    /**
     * 通过SKU ID获取SKU单位与基础单位转换比例
     * @param skuCode
     * @return
     */
    public List<SkuUnitExtDTO> skuUnitBySkuCode(String skuCode){
        return this.querySkuUnits(Arrays.asList(skuCode));
    }

    /**
     * 通过单个skuId获取sku信息
     * @param skuId
     * @return
     */
    public SkuInfoExtDTO getSkuInfoBySkuId(Long skuId){
        return this.getSkuInfoBySkuId(skuId,defaultMerchantId);
    }
    /**
     * 通过单个skuId获取sku信息
     * @param skuId
     * @return
     */
    public SkuInfoExtDTO getSkuInfoBySkuId(Long skuId ,Long merchantId){
    	long time = System.currentTimeMillis();
        QuerySkuParamExtDTO extDTO = new QuerySkuParamExtDTO();
        extDTO.setSkuId(skuId);
        extDTO.setMerchantId(merchantId);
        Response<SkuInfoExtDTO> sku = skuRemoteService.skuBySkuIdAndMerchantId(extDTO);
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-getSkuInfoBySkuId, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuId));
        }
        return sku.getData();
    }

    /**
     * 通过名称模糊查询sku信息
     * @param name
     * @return
     */
    public List<SkuOfStockRecord> getSkusByName(String name){
    	long time = System.currentTimeMillis();
        Response<List<SkuOfStockRecord>> skus = skuRemoteService.getSkusByName(name);
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-getSkusByName, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(name));
        }
        return skus.getData();
    }

    /**
     * 查询sku所有单位
     * @param skuCodeList
     * @return
     */
    public List<SkuUnitExtDTO> querySkuUnits(List<String> skuCodeList){
        return this.querySkuUnits(skuCodeList,defaultMerchantId);
    }

    /**
     * 查询sku所有单位
     * @param skuCodeList
     * @return
     */
    public List<SkuUnitExtDTO> querySkuUnits(List<String> skuCodeList,Long merchantId){
    	long time = System.currentTimeMillis();
        List<SkuUnitExtDTO> resultList = new ArrayList<>();
        List<QuerySkuParamExtDTO> paramList = new ArrayList<>();
        for (String skuCode : skuCodeList){
            if(StringUtils.isBlank(skuCode)){
                continue;
            }
            QuerySkuParamExtDTO dto = new QuerySkuParamExtDTO();
            dto.setMerchantId(merchantId);
            dto.setSkuCode(skuCode);
            paramList.add(dto);
        }
        List<List<QuerySkuParamExtDTO>> splitList = splitList(paramList);
        for (List<QuerySkuParamExtDTO> tempList : splitList) {
            Response<List<SkuUnitExtDTO>> response = skuRemoteService.querySkuUnits(tempList, PAGE);
            if (!validator.validResponse(response)) {
                throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
            }
            resultList.addAll(response.getData());
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-querySkuUnits, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuCodeList));
        }
        return resultList;
    }

    /**
     * 根据skuId和单位名称批量查询单位信息
     * @param paramList
     * @return
     */
    public List<SkuUnitExtDTO> unitsBySkuIdAndUnitNameAndMerchantId(List<SkuUnitParamExtDTO> paramList){
        return this.unitsBySkuIdAndUnitNameAndMerchantId(paramList,defaultMerchantId);
    }
    /**
     * 根据skuId和单位名称批量查询单位信息
     * @param paramList
     * @return
     */
    public List<SkuUnitExtDTO> unitsBySkuIdAndUnitNameAndMerchantId(List<SkuUnitParamExtDTO> paramList ,Long merchantId){
        long time = System.currentTimeMillis();
        paramList.forEach(skuUnitParamExtDTO -> skuUnitParamExtDTO.setMerchantId(merchantId));
        Response<PageInfo<SkuUnitExtDTO>> response = skuRemoteService.unitsBySkuIdAndUnitNameAndMerchantId(paramList);
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-unitsBySkuIdAndUnitNameAndMerchantId, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(paramList));
        }
        return response.getData().getList();
    }

    /**
     * 对list进行切片
     * @param list
     * @param <T>
     * @return
     */
    public <T> List<List<T>> splitList(List<T> list) {
        List<List<T>> listArray = new ArrayList<>();
        if (! validator.validCollection(list)) {return listArray;}
        if (list.size() <= PAGESIZE) {
            listArray.add(list);
            return listArray;
        }
        List<T> subList = new ArrayList<>();
        listArray.add(subList);
        for (int i=0; i<list.size(); i++) {
            subList.add(list.get(i));
            if (subList.size() == PAGESIZE) {
                if (i != list.size()-1) {
                    subList = new ArrayList<>();
                    listArray.add(subList);
                }
            }
        }
        return listArray;
    }

    public Long getDefaultMerchantId() {
        return defaultMerchantId;
    }

	/**
	 * 批量通过skuCode、渠道编码、销售单位集合获取Sku/上下架/价格/单位信息
	 * @param storeSaleParamDTOList
	 */
	public List<StoreSalePowerDTO> skusByManyParam(List<StoreSaleParamDTO> storeSaleParamDTOList) {
		long time = System.currentTimeMillis();
		List<StoreSalePowerDTO> resultList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(storeSaleParamDTOList)) {
			List<List<StoreSaleParamDTO>> splitList = splitList(storeSaleParamDTOList);
			for (List<StoreSaleParamDTO> tempList : splitList) {
				Response<PageInfo<StoreSalePowerDTO>> response = skuRemoteService.skusByManyParam(tempList);
				if (!validator.validResponse(response)) {
					throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
				} else {
					resultList.addAll(response.getData().getList());
				}
			}
		}
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-skusByManyParam, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(storeSaleParamDTOList));
        }
        return resultList;
	}

	/**
	 * 根据商品CODE、单位类型查询单位信息
	 * @param skuCodes
	 * @param type
	 * @return
	 */
    public List<SkuUnitExtDTO> querySkuUnitsBySkuCodeAndType(List<String> skuCodes, Long type){
        return this.querySkuUnitsBySkuCodeAndType(skuCodes, type, defaultMerchantId);
    }

	/**
	 * 根据商品CODE、单位类型、商家ID查询单位信息
	 * @param skuCodes
	 * @param type
	 * @param merchantId
	 * @return
	 */
    public List<SkuUnitExtDTO> querySkuUnitsBySkuCodeAndType(List<String> skuCodes,Long type, Long merchantId){
    	long time = System.currentTimeMillis();
        List<SkuUnitExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuCodes) && type != null) {
        	List<QuerySkuParamExtDTO> paramList = new ArrayList<>();
        	for (String skuCode : skuCodes){
        		QuerySkuParamExtDTO dto = new QuerySkuParamExtDTO();
        		dto.setMerchantId(merchantId);
        		dto.setSkuCode(skuCode);
        		paramList.add(dto);
        	}
        	List<List<QuerySkuParamExtDTO>> splitList = splitList(paramList);
        	for (List<QuerySkuParamExtDTO> tempList : splitList) {
        		Response<List<SkuUnitExtDTO>> response = skuRemoteService.querySkuUnits(tempList, PAGE);
        		if (!validator.validResponse(response)) {
        			throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
        		} else {
        			List<SkuUnitExtDTO> skuUnitExtDTOList = response.getData();
        			//相同单位类型的单位信息可能有多个
        			skuUnitExtDTOList = skuUnitExtDTOList.stream().filter(skuUnitExtDTO -> skuUnitExtDTO != null && type.equals(skuUnitExtDTO.getType())).collect(Collectors.toList());
        			resultList.addAll(skuUnitExtDTOList);
        		}
        	}
        }
        if(System.currentTimeMillis() - time > 2000) {
        	log.warn("请求item-querySkuUnitsByType, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数skuCodes:" + JSON.toJSONString(skuCodes) + ", type:" + JSON.toJSONString(type));
        }
        return resultList;
    }

    /**
     * @Description: 批量通过门店号查询有进货权限的商品并集 ,根据skuId自动去除重复数据<br>
     *
     * <AUTHOR> 2020/2/13
     * @param storeCodes
     * @return
     */
    public List<PurchaseAccessResultDTO> purchaseAccessSkuByStoreCodes(Integer pageNum, Integer pageSize, List<String> storeCodes, List<Long> skuIds) {
        List<PurchaseAccessResultDTO> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(storeCodes)) {
            return result;
        }
        int maxPage = ListUtil.getPageNum(storeCodes, pageSize);
        List<String> subList = new ArrayList<>();
        List<Long> skuIdList = new ArrayList<>();
        for(int i=1;i<=maxPage;i++) {
            subList = ListUtil.getPageList(storeCodes, i, pageSize);
            List<PurchaseAccessResultDTO> skuInfoExtNewDTOS = this.getSkuInfoExtDTOS(pageNum, 10000, subList, skuIds);
            if(skuInfoExtNewDTOS != null && skuInfoExtNewDTOS.size() > 0) {
            	for(PurchaseAccessResultDTO dto : skuInfoExtNewDTOS) {
            		if(!skuIdList.contains(dto.getSkuId())) {
            			skuIdList.add(dto.getSkuId());
            			result.add(dto);
            		}
            	}
            	skuInfoExtNewDTOS.clear();
//            	result.addAll(skuInfoExtNewDTOS);
            }
            if(skuIds != null && skuIds.size() > 0) {
            	if(skuIds.size() <= skuIdList.size()) {
            		// 当传skuId时，已查到了，就可以退出了
            		break;
            	}
            }
            skuInfoExtNewDTOS = null;
        }
        return result;
    }

    /**
     * @Description: 分页查询商品 <br>
     *
     * <AUTHOR> 2020/2/13
     * @param pageSize
     * @return 
     */
    private List<PurchaseAccessResultDTO> getSkuInfoExtDTOS(Integer pageNum, Integer pageSize, List<String> storeCodes,List<Long> skuIds) {
    	PurchaseAccessSearchParamDTO paramDTO = new PurchaseAccessSearchParamDTO();
    	paramDTO.setPageNum(pageNum);
    	paramDTO.setPageSize(pageSize);
    	paramDTO.setStoreCodes(storeCodes);
    	paramDTO.setSkuIds(skuIds);
        Response<PageInfo<PurchaseAccessResultDTO>> response = skuRemoteService.purchaseAccessSkuByStoreCodesAndSkuIds(paramDTO);
        if (!validator.validResponse(response)) {
            log.error("调用商品接口查询门店商品进货权异常, 参数 ==> {}", JSONObject.toJSONString(storeCodes));
            throw new RomeException(response.getCode(), response.getMsg());
        }
        PageInfo<PurchaseAccessResultDTO> pageInfo = response.getData();
        List<PurchaseAccessResultDTO> skuUnitExtDTOS = pageInfo.getList();
        boolean flag = pageInfo.isIsLastPage();
        while(!flag) {
            pageNum ++;
            paramDTO.setPageNum(pageNum);
            Response<PageInfo<PurchaseAccessResultDTO>> nextResponse = skuRemoteService.purchaseAccessSkuByStoreCodesAndSkuIds(paramDTO);
            if (!validator.validResponse(nextResponse)) {
                log.error("调用商品接口查询门店商品进货权异常, 参数 ==> {}", JSONObject.toJSONString(storeCodes));
                throw new RomeException(ResponseMsg.EXCEPTION.getCode(), ResponseMsg.EXCEPTION.getMsg());
            }
            PageInfo<PurchaseAccessResultDTO> nextPageInfo = nextResponse.getData();
            flag = nextPageInfo.isIsLastPage();
            skuUnitExtDTOS.addAll(nextPageInfo.getList());
        }
        return skuUnitExtDTOS;
    }


    /**
     * 根据商家id,skuCode,unitCode查询商品信息及组合品子品信息（如果是组合品）
     * @param params
     * @return
     */
    public List<SkuUnitAndCombineExtDTO> querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId(List<SkuInfo> params){
        long time = System.currentTimeMillis();
        List<SkuUnitAndCombineExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(params)) {
            List<List<SkuInfo>> splitList = this.splitList(params);
            for (List<SkuInfo> tempList : splitList) {
                Response<List<SkuUnitAndCombineExtDTO>> rep = skuRemoteService.querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId(tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    if (CollectionUtils.isNotEmpty(rep.getData())){
                        rep.getData().forEach(dto->{
                            if (CollectionUtils.isNotEmpty(dto.getCombineInfo())){
                                dto.getCombineInfo().forEach(combineSimpleDTO->{
                                    if (Objects.nonNull(combineSimpleDTO.getNum()) && Objects.nonNull(combineSimpleDTO.getScale())){
                                        //直接转换为基础单位组合比例(子品单位转换比*组合比例)
                                        combineSimpleDTO.setNum(combineSimpleDTO.getNum().multiply(combineSimpleDTO.getScale()));
                                        //为了防止后续勿使用该字段，转换后将子品单位转换比重置为1
                                        combineSimpleDTO.setScale(BigDecimal.ONE);
                                    }
                                    else {
                                        throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC
                                                +",接口返回组合skuCode:"+combineSimpleDTO.getSkuCode()+"下子品对应组合数量或子品转换系数为空");
                                    }
                                });
                            }
                        });
                    }
                    resultList.addAll(rep.getData());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求querySkuUnitAndCombinesBySkuCodeAndUnitCodeAndMerchantId, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(params));
        }
        return resultList;
    }


    /**
     * 根据商家id,skuId,unitCode查询商品信息及组合品子品信息（如果是组合品）
     * @param params
     * @return
     */
    public List<SkuUnitAndCombineExtDTO> querySkuUnitAndCombinesBySkuIdAndUnitCodeAndMerchantId(List<SkuInfo> params){
        long time = System.currentTimeMillis();
        List<SkuUnitAndCombineExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(params)) {
            List<List<SkuInfo>> splitList = this.splitList(params);
            for (List<SkuInfo> tempList : splitList) {
                Response<List<SkuUnitAndCombineExtDTO>> rep = skuRemoteService.querySkuUnitAndCombinesBySkuIdAndUnitCodeAndMerchantId(tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    if (CollectionUtils.isNotEmpty(rep.getData())){
                        rep.getData().forEach(dto->{
                            if (CollectionUtils.isNotEmpty(dto.getCombineInfo())){
                                dto.getCombineInfo().forEach(combineSimpleDTO->{
                                    if (Objects.nonNull(combineSimpleDTO.getNum()) && Objects.nonNull(combineSimpleDTO.getScale())){
                                        //直接转换为基础单位组合比例(子品单位转换比*组合比例)
                                        combineSimpleDTO.setNum(combineSimpleDTO.getNum().multiply(combineSimpleDTO.getScale()));
                                        //为了防止后续勿使用该字段，转换后将子品单位转换比重置为1
                                        combineSimpleDTO.setScale(BigDecimal.ONE);
                                    }
                                    else {
                                        throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC
                                                +",接口返回组合skuCode:"+combineSimpleDTO.getSkuCode()+"下子品对应组合数量或子品转换系数为空");
                                    }
                                });
                            }
                        });
                    }
                    resultList.addAll(rep.getData());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求querySkuUnitAndCombinesBySkuIdAndUnitCodeAndMerchantId, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(params));
        }
        return resultList;
    }
    
    /**
     * @Description: 批量通过渠道编码查询销售商品并集 ,根据skuId自动去除重复数据<br>
     *
     * <AUTHOR> 2020/4/20
     * @param channelCodes
     * @param skuIds
     * @return
     */
    public List<PurchaseAccessResultDTO> purchaseAccessSkuByChannelCodes(Integer pageNum, Integer pageSize, List<String> channelCodes, List<Long> skuIds) {
        List<PurchaseAccessResultDTO> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(channelCodes)) {
            return result;
        }
        int maxPage = ListUtil.getPageNum(channelCodes, pageSize);
        List<String> subList;
        List<Long> skuIdList = new ArrayList<>();
        List<PurchaseAccessResultDTO> skuInfoExtNewDTOS = null;
        for(int i=1;i<=maxPage;i++) {
            subList = ListUtil.getPageList(channelCodes, i, pageSize);
            skuInfoExtNewDTOS = this.getSkuInfoByChannelCodes(pageNum, 10000, subList, skuIds);
            if(skuInfoExtNewDTOS != null && skuInfoExtNewDTOS.size() > 0) {
            	for(PurchaseAccessResultDTO dto : skuInfoExtNewDTOS) {
            		if(!skuIdList.contains(dto.getSkuId())) {
            			skuIdList.add(dto.getSkuId());
            			result.add(dto);
            		}
            	}
            	skuInfoExtNewDTOS.clear();
            }
            skuInfoExtNewDTOS = null;
            if(skuIds != null && skuIds.size() > 0) {
            	if(skuIds.size() <= skuIdList.size()) {
            		// 当传skuId时，已查到了，就可以退出了
            		break;
            	}
            }
        }
        skuIdList.clear();
        skuIdList = null;
        return result;
    }
    
    /**
     * @Description: 分页查询商品 <br>
     *
     * <AUTHOR> 2020/4/20
     * @param pageSize
     * @return 
     */
    private List<PurchaseAccessResultDTO> getSkuInfoByChannelCodes(Integer pageNum, Integer pageSize, List<String> channelCodes, List<Long> skuIds) {
    	ChannelPurchaseParamDTO channelPurchaseParamDTO = new ChannelPurchaseParamDTO();
    	channelPurchaseParamDTO.setChannelCodeList(channelCodes);
    	channelPurchaseParamDTO.setPageNum(pageNum);
    	channelPurchaseParamDTO.setPageSize(pageSize);
    	channelPurchaseParamDTO.setSkuIds(skuIds);
        Response<PageInfo<PurchaseAccessResultDTO>> response = skuRemoteService.getSkuInfoByChannelCodes(channelPurchaseParamDTO);
        if (!validator.validResponse(response)) {
            log.error("调用商品接口查询渠道销售商品异常, 参数 ==> {}", JSONObject.toJSONString(channelPurchaseParamDTO));
            throw new RomeException(response.getCode(), response.getMsg());
        }
        PageInfo<PurchaseAccessResultDTO> pageInfo = response.getData();
        List<PurchaseAccessResultDTO> skuUnitExtDTOS = pageInfo.getList();
        boolean flag = pageInfo.isIsLastPage();
        while(!flag) {
            pageNum ++;
            channelPurchaseParamDTO.setPageNum(pageNum);
            Response<PageInfo<PurchaseAccessResultDTO>> nextResponse = skuRemoteService.getSkuInfoByChannelCodes(channelPurchaseParamDTO);
            if (!validator.validResponse(nextResponse)) {
                log.error("调用商品接口查询渠道销售商品异常, 参数 ==> {}", JSONObject.toJSONString(channelPurchaseParamDTO));
                throw new RomeException(ResponseMsg.EXCEPTION.getCode(), ResponseMsg.EXCEPTION.getMsg());
            }
            PageInfo<PurchaseAccessResultDTO> nextPageInfo = nextResponse.getData();
            flag = nextPageInfo.isIsLastPage();
            skuUnitExtDTOS.addAll(nextPageInfo.getList());
        }
        return skuUnitExtDTOS;
    }


    /**
     * 根据渠道编码和商品及单位获取价格及换算比
     * @param params
     * @return
     */
    public List<ChannelSkuPurchasePriceDTO> skuSalePurchasePricesBySkuCodesAndChannelCodeAndUnitCode(List<StoreSaleParamDTO> params){
        long time = System.currentTimeMillis();
        List<ChannelSkuPurchasePriceDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(params)) {
            List<List<StoreSaleParamDTO>> splitList = this.splitList(params);
            for (List<StoreSaleParamDTO> tempList : splitList) {
                Response<PageInfo<ChannelSkuPurchasePriceDTO>> rep = skuRemoteService.skuSalePurchasePricesBySkuCodesAndChannelCodeAndUnitCode(PAGE,tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    resultList.addAll(rep.getData().getList());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("skuSalePurchasePricesBySkuCodesAndChannelCodeAndUnitCode, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(params));
        }
        return resultList;
    }

    /**
     * 根据单位名称批量查询单位编码
     * @param unitNames
     * @return
     */
    public Map<String, String> unitCodeByUnitName(List<String> unitNames){
        Map<String, String> map = new HashMap<>();
        for (String unitName : unitNames) {
            String unitCode = "";
            Response<String> response = skuRemoteService.unitCodeByUnitName(unitName);
            if (!validator.validResponse(response)) {
                throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC+ ":  单位不能存在" + unitName);
            }else{
                map.put(unitName, response.getData());
            }
        }
        return map;
    }
    
    /**
     * 批量通过SkuCode、渠道编号、销售单位Code获取销售价格
     *
     * @param dtoList
     * @return
     */
    public List<ChannelSkuPurchasePriceDTO> salePricesBySkuCodesAndChannelCodesAndSaleUnitCodes(List<QuerySalePurchasePricesExtDTO> dtoList) {
        long time = System.currentTimeMillis();
        List<ChannelSkuPurchasePriceDTO> list = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dtoList)) {
            //集合分割处理
            List<List<QuerySalePurchasePricesExtDTO>> partList = ListUtils.partition(dtoList, PAGESIZE);
            Response<PageInfo<ChannelSkuPurchasePriceDTO>> resp;
            for (int i = 0; i < partList.size(); i++) {
                List<QuerySalePurchasePricesExtDTO> subList = partList.get(i);
                resp = skuRemoteService.salePricesBySkuCodesAndChannelCodesAndSaleUnitCodes(subList);
                ResponseValidateUtils.validResponse(resp);
                list.addAll(resp.getData().getList());
            }
        }
        if (System.currentTimeMillis() - time > 2000) {
            log.warn("请求item-skusBySkuCode, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(dtoList));
        }
        return list;
    }


/**
     * 根据商品code列表以及skuName查询商品信息(最多查询100条)
     * skuName是模糊查询
     * @param skuCodes
     * @return
     */
    public PageInfo<SupplierSkuStockDTO> skuCodesAndSkuName(List<String> skuCodes, String skuName, Integer pageNum, Integer pageSize){
    	Response<PageInfo<SupplierSkuStockDTO>> response = skuRemoteService.skuCodesAndSkuName(skuCodes, skuName, pageNum, pageSize);
    	if (!validator.validResponse(response)) {
            throw new RomeException(response.getCode(), "商品接口数据异常：" + response.getMsg());
        }
        return response.getData();
    }

    /**
     * 根据成品去查组合品的信息
     * @param querySelectBomDTO
     * @return
     */
    public List<BomSkuBatchDTO> batchSelectBomSkuAndDetail(QuerySelectBomDTO querySelectBomDTO){
        Response<List<BomSkuBatchDTO>> response = skuRemoteService.selectSkuBomAndDetailBySkuCodes(querySelectBomDTO);
        if (!validator.validResponse(response)) {
            throw new RomeException(response.getCode(), "商品接口数据异常：" + response.getMsg());
        }
        return response.getData();
    }


    /**
     * 根据成品去查组合品的信息
     * @param skuCodeList
     * @return
     */
    public List<SkuInfoExtDTO> skuListBySkuCodes(List<String> skuCodeList){
        Response<List<SkuInfoExtDTO>> response = skuRemoteService.skuListBySkuCodes(skuCodeList);
        if (!validator.validResponse(response)) {
            throw new RomeException(response.getCode(), "商品接口[查询含税价格]数据异常：" + response.getMsg());
        }
        return response.getData();
    }


    public List<SkuUnitExtDTO> unitsBySkuCodeAndUnitCodeAndMerchantId(List<UnitCodeExtParamDTO> list) {
        log.info("批量通过skuCode和单位code查询基础单位换算关系，参数：{}", JSON.toJSONString(list));
        Response<PageInfo<SkuUnitExtDTO>> response=null;
        try {
            response = skuRemoteService.unitsBySkuCodeAndUnitCodeAndMerchantId(list.size(), list);
        } catch (Exception e) {
            log.error("调用商品中心接口失败，异常：", e);
            throw new RomeException(ResponseMsg.EXCEPTION.getCode(), "批量通过skuCode和单位code查询基础单位换算关系异常：" + response!=null? response.getMsg():e);
        }
        if (!ResponseMsg.SUCCESS.getCode().equals(response.getCode())) {
            throw new RomeException(response.getCode(), response.getMsg());
        }
        return response.getData().getList();
    }

    /**
     * 根据skuCodes查询sku属性
     * @param skuCodeList
     * @return
     */
    public List<SkuAttributeInfoDTO> getSkuAttributeBySkuCodes(@RequestBody List<String> skuCodeList){
        long time = System.currentTimeMillis();
        List<SkuAttributeInfoDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuCodeList)) {
            List<List<String>> splitList = this.splitList(skuCodeList);
            for (List<String> tempList : splitList) {
                Response<List<SkuAttributeInfoDTO>> rep = skuRemoteService.getSkuAttributeBySkuCodes(tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC);
                }else{
                    resultList.addAll(rep.getData());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求getSkuAttributeBySkuCodes, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuCodeList));
        }
        return resultList;
    }


    /**
     * 根据skuCodes查询sku供应商
     * @param skuCodeList
     * @return
     */
    public List<SkuSupplierDTO> skuSupplierList(List<String> skuCodeList){
        long time = System.currentTimeMillis();
        List<SkuSupplierDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuCodeList)) {
            List<List<String>> splitList = this.splitList(skuCodeList);
            for (List<String> tempList : splitList) {
                Response<List<SkuSupplierDTO>> rep = skuRemoteService.skuSupplierList(tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC+"，查供应商失败");
                }else{
                    resultList.addAll(rep.getData());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求skuSupplierList, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuCodeList));
        }
        return resultList;
    }

    /**
     * 判断是否辅料
     * skuCode以8开头的9位数，一定是辅料
     */
    public boolean isSupplementaryMaterial(SkuInfoExtDTO skuInfo){
    	if(skuInfo == null || BatchStockTypeVO.isSupportBatchSpuType(skuInfo.getSpuType())) {
    		return false;
    	}
    	return true;
    }


    /***
     * 查询Z016主数据编码
     * @return
     */
    public List<String> searchZ016Codes(){
        List<String> z016Codes = new ArrayList<>();
        List<Object> skuCodes = redisUtil.range(Z016_SKU_CODES_CACHE,0,-1);
        if(skuCodes == null || skuCodes.isEmpty()){
            int page = 1;
            while (true){
                Response<PageInfo<Z016SkuCodeDTO>> response  = skuRemoteService.searchSkuCodeBySkuType(page,PAGESIZE,"Z016");
                if (!validator.validResponse(response)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC+"，查Z016主数据失败");
                }
                List<Z016SkuCodeDTO> z016SkuCodeDTOS = response.getData() == null ?Collections.EMPTY_LIST : response.getData().getList();
                for(Z016SkuCodeDTO z016SkuCodeDTO :z016SkuCodeDTOS){
                    z016Codes.add(z016SkuCodeDTO.getSkuCode());
                    skuCodes.add(z016SkuCodeDTO.getSkuCode());
                }
                //返回的数据未空
                if(z016SkuCodeDTOS.isEmpty()){
                    break;
                }
                //当前查询页数超过返回的页数，或者返回的nextpage是0
                if(page>=response.getData().getPageNum()
                        || response.getData().getNextPage() ==0
                        || page>=50){
                    break;
                }
                page ++;
            }
            List<Object> tmpList = redisUtil.range(Z016_SKU_CODES_CACHE,0,-1);
            if(CollectionUtils.isEmpty(tmpList) && CollectionUtils.isNotEmpty(skuCodes) ){
                redisUtil.mlSet(Z016_SKU_CODES_CACHE,skuCodes,Z016_SKU_CODES_CACHE_EXPIRE_TIME);
            }
        }else{
            for(Object obj : skuCodes){
                z016Codes.add(String.valueOf(obj));
            }
        }
        return z016Codes;
    }


    /**
     * 根据skuCodes查询sku供应商
     * @param skuCodeList
     * @return
     */
    public List<SkuInfoExtDTO> getMdmSkuInfoBySkuCodes(List<String> skuCodeList){
        long time = System.currentTimeMillis();
        List<SkuInfoExtDTO> resultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(skuCodeList)) {
            List<List<String>> splitList = this.splitList(skuCodeList);
            for (List<String> tempList : splitList) {
                Response<List<SkuInfoExtDTO>> rep = skuRemoteService.getMdmSkuInfoBySkuCodes(tempList);
                if (!validator.validResponse(rep)) {
                    throw new RomeException(ResCode.STOCK_ERROR_5026, ResCode.STOCK_ERROR_5026_DESC+"，查供应商失败");
                }else{
                    resultList.addAll(rep.getData());
                }
            }
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求getMdmSkuInfoBySkuCodes, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuCodeList));
        }
        return resultList;
    }

    /**
     * 飞书预警消息
     * @param request
     * @param msg
     */
    private void sendMessage(String request,String msg,String url){
        try{
            CustomMonitorFacade.callMonitor(request, Response.builderFail(CodeMessage.FAIL.getCode(), "\n**"+msg+"**\n"), null, url, "item-core");
        }catch (Exception e){
            log.error("item-core飞书预警异常：{}",e);
        }
    }

    /**
     * 查询sku工厂单位信息
     * @param skuCodeList
     * @return
     */
    public Map<String, SkuAllUnitDTO> selectSkuWarehouseUnitBySkuCode(List<String> skuCodeList){
        long time = System.currentTimeMillis();
        List<SkuAllUnitDTO> resultList = new ArrayList<>();
        List<List<String>> splitList = splitList(skuCodeList);
        for (List<String> tempList : splitList) {
            Response<List<SkuAllUnitDTO>> response = skuRemoteService.selectSkuWarehouseUnitBySkuCode(tempList);
            if (!validator.validResponse(response)) {
                throw new RomeException(com.rome.stock.common.constants.ResCode.STOCK_ERROR_5026, com.rome.stock.common.constants.ResCode.STOCK_ERROR_5026_DESC);
            }
            resultList.addAll(response.getData());
        }
        if(System.currentTimeMillis() - time > 2000) {
            log.warn("请求item-selectSkuWarehouseUnitBySkuCode, 执行耗时【" + (System.currentTimeMillis() - time) + "】, 参数:" + JSON.toJSONString(skuCodeList));
        }
        return  resultList.stream().collect(Collectors.toMap(SkuAllUnitDTO::getSkuCode,  Function.identity(), (key1, key2)-> key1));
    }

    /**
     * 获取sku单位信息，目前只支持发货单位
     * @param skuAllUnitDTO
     * @param type
     * @param factoryCode
     * @return
     */
    public SkuUnitExtDTO getUnitByTypeAndFactory(SkuAllUnitDTO skuAllUnitDTO, Long type ,String factoryCode) {
        if (skuAllUnitDTO == null) {
            return null;
        }
        //优先取工厂单位
        if (StringUtils.isNotBlank(factoryCode) && CollectionUtils.isNotEmpty(skuAllUnitDTO.getWarehouseUnits())) {
            String factoryTypeKey = factoryCode + "_" + type;
            Map<String, SkuWarehouseUnitDTO> factoryMap = skuAllUnitDTO.getFactoryUnitTypeMap();
            if (factoryMap == null) {
                factoryMap = skuAllUnitDTO.getWarehouseUnits().stream().collect(Collectors.toMap(SkuWarehouseUnitDTO::getFactoryTypekey, Function.identity(), (key1, key2) -> key1));
                skuAllUnitDTO.setFactoryUnitTypeMap(factoryMap);
            }
            SkuWarehouseUnitDTO skuWarehouseUnitDTO = factoryMap.get(factoryTypeKey);
            //如果工厂单位存在，则优先返回工厂单位
            if (skuWarehouseUnitDTO != null) {
                SkuUnitExtDTO skuUnitExtDTO = new SkuUnitExtDTO();
                BeanUtils.copyProperties(skuWarehouseUnitDTO, skuUnitExtDTO);
                return skuUnitExtDTO;
            }
        }
        //如果工厂单位不存在，则取sku单位
        if (CollectionUtils.isNotEmpty(skuAllUnitDTO.getSkuCodeUnits())) {
            Map<Long, SkuUnitExtDTO> skuUnitTypeMap = skuAllUnitDTO.getSkuUnitTypeMap();
            if (skuUnitTypeMap == null) {
                skuUnitTypeMap = skuAllUnitDTO.getSkuCodeUnits().stream().collect(Collectors.toMap(SkuUnitExtDTO::getType, Function.identity(), (key1, key2) -> key1));
                skuAllUnitDTO.setSkuUnitTypeMap(skuUnitTypeMap);
            }
            return skuUnitTypeMap.get(type);
        }
        return null;
    }

}
