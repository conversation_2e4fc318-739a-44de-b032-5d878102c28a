package com.rome.stock.core.configuration.context;

import com.google.common.collect.Lists;
import com.rome.stock.core.api.dto.WarehouseRecordDTO;
import com.rome.stock.core.common.AlikAssert;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.common.StockMessageTool;
import com.rome.stock.core.configuration.HandlerDispatchHolder;
import com.rome.stock.core.configuration.dto.dispatchNotice.DispatchNoticeConfigDTO;
import com.rome.stock.core.configuration.service.DispatchNoticeService;
import com.rome.stock.core.constant.WarehouseRecordStatusVO;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.message.consumer.PopReturnChargeAgainstConsumer;
import com.rome.stock.core.domain.repository.DispatchNoticeRepository;
import com.rome.stock.core.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.core.domain.service.WarehouseRecordService;
import com.rome.stock.core.infrastructure.dataobject.DispatchNoticeDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DispatchNoticeConfigContext {

    @Resource
    private HandlerDispatchHolder handlerDispatchHolder;
    @Resource
    private WarehouseRecordRepository warehouseRecordRepository;
    @Resource
    private DispatchNoticeRepository dispatchNoticeRepository;
    @Resource
    private WarehouseRecordService warehouseRecordService;
    @Resource
    private PopReturnChargeAgainstConsumer popReturnChargeAgainstConsumer;

    /**
     * 派车通知
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void dispatchNoticeRecord(DispatchNoticeConfigDTO dto){
        //获取对应处理Handler
        DispatchNoticeService serviceHandler = handlerDispatchHolder.getRecordHandler(dto.getDispatchConfigType());
        WarehouseRecordE warehouseRecordE=warehouseRecordRepository.queryWarehouseRecordByRecordCode(dto.getRecordCode());
        //参数校验
        serviceHandler.validationParams(dto,warehouseRecordE);
        //新增派车单
        DispatchNoticeDO dispatchNoticeDO = serviceHandler.initDispatchNotice(dto,warehouseRecordE);
        boolean executeResult = dispatchNoticeRepository.insertDispatchNotice(dispatchNoticeDO);
        AlikAssert.isTrue(executeResult, ResCode.STOCK_ERROR_4001, ResCode.STOCK_ERROR_4001_DESC);
        //更新后置单单据信息及状态
        serviceHandler.updateWarehouseRecord(dispatchNoticeDO,warehouseRecordE);
        //已出库派车,发送MQ消息,bms重新推送变更数据
        if(WarehouseRecordStatusVO.OUT_ALLOCATION.getStatus().equals(warehouseRecordE.getRecordStatus())) {
            StockMessageTool.bmsLogisticsChange(warehouseRecordE);
        }
        if(dto.getAutoStock()){
            //无需下发WMS,修改sync_wms_status 为2
            warehouseRecordRepository.updateRecordSyncStatusToSynchronized(Lists.newArrayList(dto.getRecordCode()));
            //调用自动出库接口
            WarehouseRecordDTO warehouseRecordDTO = popReturnChargeAgainstConsumer.wmsOutFun(dto.getRecordCode());
            //这个逻辑一定要放在最后一步
            warehouseRecordService.wmsOutRecordCallBack(warehouseRecordDTO);
        }

    }

}
