package com.rome.stock.core.configuration.dto.dispatchNotice;

import com.rome.stock.core.common.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 派车通知DTO
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
public class DispatchNoticeConfigDTO {

    @ApiModelProperty(value = "派车类型(NEED_DISPATCH:派车 SELF_TAKE:自提)", required = true)
    @NotNull(message="派车类型(NEED_DISPATCH:派车 SELF_TAKE:自提)")
    private String dispatchConfigType;

    @ApiModelProperty(value = "sap单据编码", required = true)
    @NotBlank(message="sap单据编码")
    private String recordCode;

    @ApiModelProperty(value = "物流公司编码")
    private String logisticsCode;

    @ApiModelProperty(value = "承运商简称")
    private String logisticsShortName;

    @ApiModelProperty(value = "TMS派车单号")
    private String thirdRecordCode;

    @ApiModelProperty(value = "来源系统")
    private String sourceSystem;


    @ApiModelProperty(value = "司机姓名",hidden = true)
    private String driverName;

    @ApiModelProperty(value = "司机手机",hidden = true)
    private String driverMobile;

    @ApiModelProperty(value = "推荐路线",hidden = true)
    private String recommendedRoute;

    @ApiModelProperty(value = "预测出车时间",hidden = true)
    private Date predictTime;

    @ApiModelProperty(value = "实际出车时间",hidden = true)
    private Date faceTime;

    @ApiModelProperty(value = "邮件状态",hidden = true)
    private Integer mailStatus;

    @ApiModelProperty(value = "邮件id",hidden = true)
    private Long mailId;

    @ApiModelProperty(value = "波次号",hidden = true)
    private String waveCode;

    @ApiModelProperty(value = "车牌号",hidden = true)
    private String carNo;

    @ApiModelProperty("是否自动出/入库")
    private Boolean autoStock=false;

    public String getLogisticsCode(){
        return StringUtils.defaultString(this.logisticsCode);
    }
}
