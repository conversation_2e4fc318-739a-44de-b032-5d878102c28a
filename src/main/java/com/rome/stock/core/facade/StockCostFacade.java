/**
 * Filename StockCostFacade.java
 * Company 上海来伊份科技有限公司。
 *
 * <AUTHOR>
 * @version
 */
package com.rome.stock.core.facade;

import com.google.common.collect.Lists;
import com.rome.arch.core.exception.RomeException;
import com.rome.arch.util.SpringBeanUtil;
import com.rome.stock.common.constants.warehouse.WarehouseRecordConstant;
import com.rome.stock.common.enums.warehouse.WarehouseRecordTypeVO;
import com.rome.stock.common.utils.DateUtil;
import com.rome.stock.core.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.core.api.dto.warehouserecord.RecordDetailDTO;
import com.rome.stock.core.api.dto.warehouserecord.ShopPackDTO;
import com.rome.stock.core.common.ResCode;
import com.rome.stock.core.common.RomeCollectionUtil;
import com.rome.stock.core.common.StringUtils;
import com.rome.stock.core.constant.RealWarehouseTypeVO;
import com.rome.stock.core.constant.WarehouseRecordStatusVO;
import com.rome.stock.core.constant.WmsSyncStatusVO;
import com.rome.stock.core.constant.WmsSyncTransferStatusVO;
import com.rome.stock.core.domain.convertor.WarehouseRecordDetailConvertor;
import com.rome.stock.core.domain.entity.RealWarehouseE;
import com.rome.stock.core.domain.entity.RwBatchE;
import com.rome.stock.core.domain.entity.frontrecord.PurchaseOrderE;
import com.rome.stock.core.domain.entity.warehouserecord.AbstractWarehouseRecord;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.domain.message.transaction.MessageDTO;
import com.rome.stock.core.domain.message.transaction.OrderCostMsgSender;
import com.rome.stock.core.domain.repository.CoreRealWarehouseStockRepository;
import com.rome.stock.core.domain.repository.RealWarehouseRepository;
import com.rome.stock.core.domain.repository.RwBatchRepository;
import com.rome.stock.core.domain.repository.frontrecord.FrPurchaseOrderRepository;
import com.rome.stock.core.domain.repository.frontrecord.FrontWarehouseRecordRelationRepository;
import com.rome.stock.core.domain.repository.warehouserecord.WarehouseRecordRepository;
import com.rome.stock.core.domain.service.OrderUtilService;
import com.rome.stock.core.domain.service.PurchaseOrderService;
import com.rome.stock.core.domain.service.ShopRetailService;
import com.rome.stock.core.infrastructure.dataobject.*;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreRealStockOpDetailDO;
import com.rome.stock.core.infrastructure.dataobject.core.CoreStockOpFactoryDO;
import com.rome.stock.core.infrastructure.mapper.FrontWarehouseRecordRelationMapper;
import com.rome.stock.core.infrastructure.mapper.OrderCostMapper;
import com.rome.stock.core.infrastructure.mapper.WarehouseRecordDetailMapper;
import com.rome.stock.core.infrastructure.mapper.WarehouseRecordMapper;
import com.rome.stock.core.infrastructure.mapper.frontrecord.FrChargeAgainstMapper;
import com.rome.stock.core.remote.item.facade.SkuFacade;
import com.rome.stock.core.remote.network.facade.NetWorkFacade;
import com.rome.stock.core.remote.orderCneter.dto.DoOrderDetailDTO;
import com.rome.stock.core.remote.orderCneter.dto.ProductBomRefDTO;
import com.rome.stock.core.remote.orderCneter.facade.OrderCenterFacade;
import com.rome.stock.core.remote.venus.TradePathTools;
import com.rome.stock.core.remote.venus.dto.CompanyTraderPathConstans;
import com.rome.stock.core.remote.venus.dto.StockMessageLogDTO;
import com.rome.stock.core.remote.venus.dto.TradePathInfoDTO;
import com.rome.stock.core.remote.venus.facade.VenusStockFacade;
import com.rome.stock.wms.config.BaseinfoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存成本-财务中台，功能面板
 *
 * <AUTHOR>
 * @since 2021-8-10 16:38:13
 */
@Slf4j
public class StockCostFacade {

	static {
		warehouseRecordRepository = SpringBeanUtil.getBean(WarehouseRecordRepository.class);
		warehouseRecordDetailMapper = SpringBeanUtil.getBean(WarehouseRecordDetailMapper.class);
		warehouseRecordMapper = SpringBeanUtil.getBean(WarehouseRecordMapper.class);
		orderUtilService = SpringBeanUtil.getBean(OrderUtilService.class);
		coreRealWarehouseStockRepository = SpringBeanUtil.getBean(CoreRealWarehouseStockRepository.class);
		orderCostMsgSender = SpringBeanUtil.getBean(OrderCostMsgSender.class);
		tradePathTools = SpringBeanUtil.getBean(TradePathTools.class);
		netWorkFacade = SpringBeanUtil.getBean(NetWorkFacade.class);
		frontWarehouseRecordRelationRepository = SpringBeanUtil.getBean(FrontWarehouseRecordRelationRepository.class);
		frPurchaseOrderRepository = SpringBeanUtil.getBean(FrPurchaseOrderRepository.class);
		purchaseOrderService = SpringBeanUtil.getBean(PurchaseOrderService.class);
		orderCostMapper = SpringBeanUtil.getBean(OrderCostMapper.class);
		frChargeAgainstMapper = SpringBeanUtil.getBean(FrChargeAgainstMapper.class);
		frontWarehouseRecordRelationMapper = SpringBeanUtil.getBean(FrontWarehouseRecordRelationMapper.class);
		rwBatchRepository = SpringBeanUtil.getBean(RwBatchRepository.class);
		realWarehouseRepository = SpringBeanUtil.getBean(RealWarehouseRepository.class);
		venusStockFacade = SpringBeanUtil.getBean(VenusStockFacade.class);
		skuFacade = SpringBeanUtil.getBean(SkuFacade.class);
		shopRetailService = SpringBeanUtil.getBean(ShopRetailService.class);
		orderCenterFacade = SpringBeanUtil.getBean(OrderCenterFacade.class);
		warehouseRecordDetailConvertor = SpringBeanUtil.getBean(WarehouseRecordDetailConvertor.class);

	}
	private static WarehouseRecordDetailConvertor warehouseRecordDetailConvertor;
	private static WarehouseRecordRepository warehouseRecordRepository;
	private static WarehouseRecordDetailMapper warehouseRecordDetailMapper;
	private static WarehouseRecordMapper warehouseRecordMapper;
	private static OrderUtilService orderUtilService;
	private static CoreRealWarehouseStockRepository coreRealWarehouseStockRepository;
	private static OrderCostMsgSender orderCostMsgSender;
	private static TradePathTools tradePathTools;
	private static NetWorkFacade netWorkFacade;
	private static FrontWarehouseRecordRelationRepository frontWarehouseRecordRelationRepository;
	private static FrPurchaseOrderRepository frPurchaseOrderRepository;
	private static PurchaseOrderService purchaseOrderService;
	private static OrderCostMapper orderCostMapper;
	private static FrChargeAgainstMapper frChargeAgainstMapper;
	private static FrontWarehouseRecordRelationMapper frontWarehouseRecordRelationMapper;
	private static RwBatchRepository rwBatchRepository;
	private static RealWarehouseRepository realWarehouseRepository;
	private static VenusStockFacade venusStockFacade;
	private static SkuFacade skuFacade;
	private static ShopRetailService shopRetailService;
 	private static OrderCenterFacade orderCenterFacade;



	public final static String ELECTRONICWAREHOUSE = "electronic.warehouse";
	public final static String REALWAREHOUSECODE = "real.warehouse.code";



	public static void processCost(CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE) {
		initPackageByReceiveRecord(warehouseRecordE);
//		initDouYinChannelPackageOutAndInRecord(warehouseRecordE,stockOpFactoryDO);
		processCost(stockOpFactoryDO, warehouseRecordE, null);
	}


	/**
	 * 处理库存成本数据，即财务中台，包括创建虚拟税筹出入库单据和发送mq通知等工具类<br/>
	 * 是否需要验证创建虚拟税筹出入库单据，需要扩展补充isValidateCreateVirtualRecord该方法<br/>
	 * 统一创建虚拟出入库，将stockOpFactoryDO传入，方便回滚，后续再自己的事务调stockOpFactoryDO.commit()方法，并统一回滚
	 *
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE 需要注意传wmsRecordCode\receiptRecordCode
	 */
	public static void processCost(CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE, String extMsg) {

		MessageDTO messageDTO = new MessageDTO();
		List<String> refRecordCodes = new ArrayList<>();
		// 是否需要验证创建虚拟税筹出入库单据，根据单据类型
		TradePathInfoDTO tradePathDto = tradePathTools.buildTradePathDto(warehouseRecordE);
		if (tradePathDto.getNeedCreate()) {
			List<RealWarehouseDO> realWarehouseDOs = tradePathTools.getRealWarehouseIdByCreateVirtualRecord(tradePathDto,warehouseRecordE.getRecordCode(), messageDTO);
			if (realWarehouseDOs != null && realWarehouseDOs.size() > 0) {
				List<WarehouseRecordTypeVO> multiPathList = multiPathTypeList(warehouseRecordE.getRecordType());
				if (null != multiPathList && realWarehouseDOs.size() > 1) {
					//加盟补货的,领用,销售系统的有特殊逻辑，如果中间公司有多个，前面的几个都是13、14的虚入，
					for (int i = 0; i < realWarehouseDOs.size() - 1; i++) {
						// 创建虚拟税筹出入库单据
						List<String> refRecordCode = createVirtualRecord(multiPathList, stockOpFactoryDO, warehouseRecordE, realWarehouseDOs.get(i).getId());
						refRecordCodes.addAll(refRecordCode);
					}
				}
				// 创建虚拟税筹出入库单据
				List<String> refRecordCode = createVirtualRecord(getTypeListByType(warehouseRecordE.getRecordType()), stockOpFactoryDO, warehouseRecordE, realWarehouseDOs.get(realWarehouseDOs.size() - 1).getId());
				refRecordCodes.addAll(refRecordCode);
				messageDTO.setRefRecordCodes(refRecordCodes);

			}
		}
		messageDTO.setRecordCode(warehouseRecordE.getRecordCode());
		messageDTO.setRecordType(warehouseRecordE.getRecordType());
		messageDTO.setReceiveRecord(warehouseRecordE.getWmsRecordCode());
		messageDTO.setChannelCode(warehouseRecordE.getChannelCode());
		messageDTO.setZtRecordCode(warehouseRecordE.getReceiptRecordCode());
		messageDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		messageDTO.setExtMsg(extMsg);
		orderCostMsgSender.sendMsg(messageDTO);
	}


	/**
	 * 如果是仓库领用快递直发的包材单据,过账时使用领用单类型过账
	 * @param warehouseRecordE
	 */
	private static void initPackageByReceiveRecord(WarehouseRecordE warehouseRecordE) {
		if(Objects.equals(WarehouseRecordTypeVO.PACKAGE_MATERIAL_OUT_RECORD.getType(),warehouseRecordE.getRecordType())){
			//如果是包材单据
			String receiveRecordCode=warehouseRecordMapper.queryReceiveRecordCodeByPackageCode(warehouseRecordE.getRecordCode());
			WarehouseRecordDo receiveRecordDo = warehouseRecordMapper.getByCode(receiveRecordCode, null);
			//判断是否能找到对应的领用快递直发单据
			if (Objects.nonNull(receiveRecordDo) && Objects.equals(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType(), receiveRecordDo.getRecordType())
					&& Objects.equals(receiveRecordDo.getTransWay(),2)) {
				//如果是仓库领用快递直发的包材单据,过账时使用领用单类型过账
				warehouseRecordE.setRecordType(WarehouseRecordTypeVO.WAREHOUSE_OUT_USE_RECORD.getType());
				warehouseRecordE.setOutRecordCode(receiveRecordCode);
				List<WarehouseRecordDetail> warehouseRecordDetails =warehouseRecordRepository.queryDetailListByRecordCode(warehouseRecordE.getRecordCode());
				warehouseRecordE.setWarehouseRecordDetails(warehouseRecordDetails);
			}
		}
	}


	/**
	 * 抖音渠道的电商TOC单据,需要先生成门店包装出入库单
	 * @param warehouseRecordE
	 */
	private static void initDouYinChannelPackageOutAndInRecord(WarehouseRecordE warehouseRecordE,CoreStockOpFactoryDO stockOpFactoryDO) {
		if(WarehouseRecordTypeVO.getOnlineRetailTypes().containsKey(warehouseRecordE.getRecordType()) && isSpecialChannel(warehouseRecordE.getChannelCode())){
			//如果是电商单,并且是抖音渠道的单子
			//过滤出大包装组合的物料,将7字码物料转换为2字码物料
			List<DoOrderDetailDTO> detailList = orderCenterFacade.queryDODetailAndBomByOutCode(warehouseRecordE.getRecordCode());
			if (CollectionUtils.isEmpty(detailList)) {
				log.info("出库单【{}】查询订单中心:queryDODetailAndBomByOutCode接口没有明细数据", warehouseRecordE.getRecordCode());
				return;
			}
			detailList = detailList.stream().filter(v -> !CollectionUtils.isEmpty(v.getProductBomRefList())).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(detailList)) {
				log.info("出库单【{}】查询订单中心:queryDODetailAndBomByOutCode接口过滤boom组合后没有数据", warehouseRecordE.getRecordCode());
				return;
			}
			Map<String, List<DoOrderDetailDTO>> listMap = detailList.stream().collect(Collectors.groupingBy(DoOrderDetailDTO::getSkuCode));
			//存在Boom组合的子品物料
			Set<String> boomChildSkuCode = new HashSet<>();
			for (DoOrderDetailDTO doOrderDetailDTO : detailList) {
				List<ProductBomRefDTO> productBomRefList = doOrderDetailDTO.getProductBomRefList();
				if (CollectionUtils.isEmpty(productBomRefList)) {
					continue;
				}
				for (ProductBomRefDTO productBomRefDTO : productBomRefList) {
					boomChildSkuCode.add(productBomRefDTO.getSkuCode());
				}
			}
			RealWarehouseE realWarehouseE = realWarehouseRepository.getRealWarehouseById(warehouseRecordE.getRealWarehouseId());
			//初始化门店包装出库单的参数
			OutWarehouseRecordDTO outWarehouseRecordDTO=new OutWarehouseRecordDTO();
			//大包装物料,不下发WMS
			outWarehouseRecordDTO.setIsLargePackaging(1);
			outWarehouseRecordDTO.setSapOrderCode(warehouseRecordE.getSapOrderCode());
			outWarehouseRecordDTO.setFactoryCode(realWarehouseE.getFactoryCode());
			outWarehouseRecordDTO.setWarehouseCode(realWarehouseE.getRealWarehouseOutCode());
			outWarehouseRecordDTO.setRecordType(WarehouseRecordTypeVO.SHOP_ASSEMBLE_OUT_WAREHOUSE_RECORD.getType());
			outWarehouseRecordDTO.setChannelCode(warehouseRecordE.getChannelCode());
			outWarehouseRecordDTO.setOutRecordCode(warehouseRecordE.getSapOrderCode());
			outWarehouseRecordDTO.setOutCreateTime(DateUtil.now());
			//生成门店包装单号
			String outRecordCode = orderUtilService.queryOrderCode(WarehouseRecordTypeVO.SHOP_ASSEMBLE_OUT_WAREHOUSE_RECORD.getCode());
			outWarehouseRecordDTO.setRecordCode(outRecordCode);
			//原路出库单明细
			List<RecordDetailDTO> outDetailList = Lists.newArrayList();
			for (Map.Entry<String, List<DoOrderDetailDTO>> parentSkuEntry : listMap.entrySet()) {
				for (DoOrderDetailDTO doOrderDetailDTO : parentSkuEntry.getValue()) {
					for (ProductBomRefDTO childDetailDTO : doOrderDetailDTO.getProductBomRefList()) {
						RecordDetailDTO recordDetailDTO = new RecordDetailDTO();
						recordDetailDTO.setBasicSkuQty(childDetailDTO.getBomQty().multiply(doOrderDetailDTO.getPlanQty()).setScale(3, RoundingMode.DOWN));
						recordDetailDTO.setSkuId(childDetailDTO.getSkuId());
						recordDetailDTO.setSkuCode(childDetailDTO.getSkuCode());
						recordDetailDTO.setBasicUnit(childDetailDTO.getBasicUnitName());
						recordDetailDTO.setBasicUnitCode(childDetailDTO.getBasicUnitCode());
						recordDetailDTO.setDeliveryLineNo(childDetailDTO.getSkuCode());
						outDetailList.add(recordDetailDTO);
					}
				}
			}
			//循环遍历detailList,将相同deliveryLineNo的basicSkuQty进行累加,行合并
			outDetailList = outDetailList.stream().collect(Collectors.groupingBy(RecordDetailDTO::getDeliveryLineNo)).entrySet().stream().map(v -> {
				RecordDetailDTO recordDetailDTO = new RecordDetailDTO();
				recordDetailDTO.setBasicSkuQty(v.getValue().stream().map(RecordDetailDTO::getBasicSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
				recordDetailDTO.setSkuId(v.getValue().get(0).getSkuId());
				recordDetailDTO.setSkuCode(v.getValue().get(0).getSkuCode());
				recordDetailDTO.setBasicUnit(v.getValue().get(0).getBasicUnit());
				recordDetailDTO.setBasicUnitCode(v.getValue().get(0).getBasicUnitCode());
				recordDetailDTO.setDeliveryLineNo(v.getValue().get(0).getSkuCode());
				return recordDetailDTO;
			}).collect(Collectors.toList());
			outWarehouseRecordDTO.setDetailList(outDetailList);

			//初始化门店包装入库单的参数
			OutWarehouseRecordDTO inWarehouseRecordDTO=new OutWarehouseRecordDTO();
			//大包装物料,不下发WMS
			inWarehouseRecordDTO.setIsLargePackaging(1);
			inWarehouseRecordDTO.setSapOrderCode(warehouseRecordE.getSapOrderCode());
			inWarehouseRecordDTO.setFactoryCode(realWarehouseE.getFactoryCode());
			inWarehouseRecordDTO.setWarehouseCode(realWarehouseE.getRealWarehouseOutCode());
			inWarehouseRecordDTO.setRecordType(WarehouseRecordTypeVO.SHOP_ASSEMBLE_IN_WAREHOUSE_RECORD.getType());
			inWarehouseRecordDTO.setChannelCode(warehouseRecordE.getChannelCode());
			inWarehouseRecordDTO.setOutRecordCode(warehouseRecordE.getSapOrderCode());
			inWarehouseRecordDTO.setOutCreateTime(DateUtil.now());
			//生成门店包装单号
			inWarehouseRecordDTO.setRecordCode(orderUtilService.queryOrderCode(WarehouseRecordTypeVO.SHOP_ASSEMBLE_IN_WAREHOUSE_RECORD.getCode()));
			//成品入库单明细
			List<RecordDetailDTO> inDetailList = Lists.newArrayList();
			for (Map.Entry<String, List<DoOrderDetailDTO>> parentSkuEntry : listMap.entrySet()) {
				for (DoOrderDetailDTO doOrderDetailDTO : parentSkuEntry.getValue()) {
					RecordDetailDTO recordDetailDTO = new RecordDetailDTO();
					recordDetailDTO.setBasicSkuQty(doOrderDetailDTO.getPlanQty());
					recordDetailDTO.setSkuId(doOrderDetailDTO.getSkuId());
					recordDetailDTO.setSkuCode(doOrderDetailDTO.getSkuCode());
					recordDetailDTO.setBasicUnit(doOrderDetailDTO.getUnit());
					recordDetailDTO.setBasicUnitCode(doOrderDetailDTO.getUnitCode());
					recordDetailDTO.setDeliveryLineNo(doOrderDetailDTO.getSkuCode());
					inDetailList.add(recordDetailDTO);
				}
			}
			//循环遍历detailList,将相同deliveryLineNo的basicSkuQty进行累加,行合并
			inDetailList = inDetailList.stream().collect(Collectors.groupingBy(RecordDetailDTO::getDeliveryLineNo)).entrySet().stream().map(v -> {
				RecordDetailDTO recordDetailDTO = new RecordDetailDTO();
				recordDetailDTO.setBasicSkuQty(v.getValue().stream().map(RecordDetailDTO::getBasicSkuQty).reduce(BigDecimal.ZERO, BigDecimal::add));
				recordDetailDTO.setSkuId(v.getValue().get(0).getSkuId());
				recordDetailDTO.setSkuCode(v.getValue().get(0).getSkuCode());
				recordDetailDTO.setBasicUnit(v.getValue().get(0).getBasicUnit());
				recordDetailDTO.setBasicUnitCode(v.getValue().get(0).getBasicUnitCode());
				recordDetailDTO.setDeliveryLineNo(v.getValue().get(0).getSkuCode());
				return recordDetailDTO;
			}).collect(Collectors.toList());
			inWarehouseRecordDTO.setDetailList(inDetailList);

			//生成门店包装出入库单
			ShopPackDTO shopPackDTO = new ShopPackDTO();
			shopPackDTO.setOutWarehouseRecordDTO(outWarehouseRecordDTO);
			shopPackDTO.setOutRecordCode(warehouseRecordE.getSapOrderCode());
			shopPackDTO.setInWarehouseRecordDTO(inWarehouseRecordDTO);
			shopPackDTO.setOperateStock(false);
			//包装出入库单不操作库存
			shopRetailService.addShopRetailPack(Lists.newArrayList(shopPackDTO));
			//门店包装出入库单,统一使用门店包装出库单过账
			WarehouseRecordE outWarehouseRecordE = warehouseRecordRepository.queryWarehouseRecordByRecordCode(outRecordCode);
			StockCostFacade.sendOutOrderToCostMsg(outWarehouseRecordE);
		}
	}



	/**
	 * 冲销单据创建虚拟单据以及发送cost消息
	 *
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE
	 */
	public static void processCostForReverse(CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE) {
		String frontRecordCode = frontWarehouseRecordRelationRepository.getFrontRecordCodeByRecordCode(warehouseRecordE.getRecordCode());
		if (StringUtils.isEmpty(frontRecordCode)) {
			throw new RomeException("1001", "冲销前置单号不存在：" + warehouseRecordE.getRecordCode());
		}
		String originRecordCode = frChargeAgainstMapper.getByRecordCode(frontRecordCode).getOriginRecordCode();
		if(Objects.equals(warehouseRecordE.getRecordType(), WarehouseRecordTypeVO.SHOP_RECEIVE_REVERSE.getType())){
			//门店领用冲销过账
			processCostForShopReceiveReverse(originRecordCode,stockOpFactoryDO,warehouseRecordE);
		}else{
			processCostForReverse(originRecordCode, stockOpFactoryDO, warehouseRecordE);
		}

	}


	public static void processCostForReverse(String originRecordCode, CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE) {

		List<StockMessageLogDTO> messageLogs = venusStockFacade.queryByGroupId(originRecordCode);
		if (CollectionUtils.isEmpty(messageLogs)) {
			throw new RomeException("1001", "财务中台日志表无记录，无法冲销：" + originRecordCode);
		}
		List<String> refRecordCodes = new ArrayList<>();
		MessageDTO messageDTO = new MessageDTO();
		// 倒序排序，冲销的时候 产生的单据顺序需要反过来
		List<StockMessageLogDTO> temp = messageLogs.stream().sorted(new Comparator<StockMessageLogDTO>() {
			@Override
			public int compare(StockMessageLogDTO o1, StockMessageLogDTO o2) {
				return o2.getSortNum().compareTo(o1.getSortNum());
			}
		}).collect(Collectors.toList());
		//直送比较特殊，直送的只冲销采购入库单
		if (temp.size() > 1 && !warehouseRecordE.getRecordType().equals(3043)) {
			// 创建虚拟税筹出入库单据
			for (StockMessageLogDTO messageLogDO : temp) {
				//原单自己无需查询，肯定不是虚单
				if (!originRecordCode.equals(messageLogDO.getBizId())) {
					OrderCostDO costDO = orderCostMapper.queryByZtRecordCode(messageLogDO.getBizId());
					if (costDO.getRecordType().equals(WarehouseRecordTypeVO.VIRTUAL_IN.getType())
							|| costDO.getRecordType().equals(WarehouseRecordTypeVO.VIRTUAL_OUT.getType())
							|| costDO.getRecordType().equals(WarehouseRecordTypeVO.DISPARITY_VIRTUAL_OUT.getType())
							|| costDO.getRecordType().equals(WarehouseRecordTypeVO.DISPARITY_VIRTUAL_IN.getType())
							|| costDO.getRecordType().equals(WarehouseRecordTypeVO.WAREHOUSE_USE_VIRTUAL_OUT.getType())
							|| costDO.getRecordType().equals(WarehouseRecordTypeVO.RETURN_VIRTUAL_OUT.getType())
							|| costDO.getRecordType().equals(WarehouseRecordTypeVO.RETURN_VIRTUAL_IN.getType())) {
						List<String> refRecordCode = createVirtualRecord(true, Arrays.asList(WarehouseRecordTypeVO.getByType(costDO.getRecordType())), stockOpFactoryDO, warehouseRecordE, costDO.getRealWarehouseId());
						refRecordCodes.add(refRecordCode.get(0) + "__" + costDO.getZtRecordCode());
					} else if (costDO.getRecordType().equals(WarehouseRecordTypeVO.PURCHASE_IN_WAREHOUSE_RECORD_INTERNATIONAL.getType())) {
						refRecordCodes.add("R" + costDO.getZtRecordCode() + "__" + costDO.getZtRecordCode());
					}
				} else {
					refRecordCodes.add(warehouseRecordE.getRecordCode() + "__" + originRecordCode);
				}
			}
		} else {
			refRecordCodes.add(warehouseRecordE.getRecordCode() + "__" + originRecordCode);
		}
		messageDTO.setRefRecordCodes(refRecordCodes);
		messageDTO.setRecordCode(warehouseRecordE.getRecordCode());
		//固定为3000，方便cost那边统一handler处理
		messageDTO.setRecordType(WarehouseRecordTypeVO.REVERSE_RECORD.getType());
		messageDTO.setReceiveRecord(warehouseRecordE.getWmsRecordCode());
		messageDTO.setChannelCode(warehouseRecordE.getChannelCode());
		messageDTO.setZtRecordCode(warehouseRecordE.getReceiptRecordCode());
		messageDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		orderCostMsgSender.sendMsg(messageDTO);
	}


	/**
	 * 门店领用冲销
	 * @param originRecordCode
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE
	 */
	public static void processCostForShopReceiveReverse(String originRecordCode, CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE) {
		List<StockMessageLogDTO> messageLogs = venusStockFacade.queryGroupDataByBizId(originRecordCode);
		if (CollectionUtils.isEmpty(messageLogs)) {
			throw new RomeException("1001", "财务中台日志表无记录，无法冲销：" + originRecordCode);
		}
		MessageDTO messageDTO = new MessageDTO();
		messageDTO.setRecordCode(warehouseRecordE.getRecordCode());
		messageDTO.setRecordType(WarehouseRecordTypeVO.SHOP_RECEIVE_REVERSE.getType());
		messageDTO.setReceiveRecord(warehouseRecordE.getWmsRecordCode());
		messageDTO.setChannelCode(warehouseRecordE.getChannelCode());
		messageDTO.setZtRecordCode(warehouseRecordE.getReceiptRecordCode());
		messageDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		orderCostMsgSender.sendMsg(messageDTO);
	}

	/**
	 * 直送推送到财务中台
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE
	 */
	public static void processCostForDirect(CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE) {
		List<RwBatchE> rwBatchES=rwBatchRepository.queryByRecordCode(warehouseRecordE.getRecordCode());
		processCostForDirect(stockOpFactoryDO,warehouseRecordE,rwBatchES);
	}


	/**
	 * 指定批次
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE
	 * @param rwBatchES
	 */
	public static void processCostForDirect(CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE,List<RwBatchE> rwBatchES) {

		//新增外采入库单
		warehouseRecordE.setSyncWmsStatus(WmsSyncStatusVO.NO_REQUIRED.getStatus());
		//使用门店入库单的业务单号（即大仓出库单号），查询大仓采购入库单
		PurchaseOrderE purchaseOrderE=frPurchaseOrderRepository.queryByOutRecordCode(warehouseRecordE.getSapOrderCode());
		if(null == purchaseOrderE){
			throw new RomeException(ResCode.STOCK_ERROR_1017,"查询采购前置单不存在，outRecordCode:"+warehouseRecordE.getSapOrderCode());
		}
		List<String> refRecordCodes=new ArrayList<>();
		Long realWarehouseId=purchaseOrderE.getRealWarehouseId();
		//新增直送采购入库单
		List<String> purchaseInRecordCode=createVirtualRecord(Arrays.asList(WarehouseRecordTypeVO.WH_CHAIN_DIRECT_IN_RECORD),stockOpFactoryDO,warehouseRecordE,realWarehouseId);
		//关联大仓入库单的前置单和后置单关联关系
		if(CollectionUtils.isEmpty(purchaseInRecordCode)){
			throw new RomeException(ResCode.STOCK_ERROR_1001,"新增直送采购入库单单号为空");
		}
		WarehouseRecordDo warehouseRecordDo=warehouseRecordMapper.getByCode(purchaseInRecordCode.get(0),null);
		if(null == warehouseRecordDo){
			throw new RomeException(ResCode.STOCK_ERROR_1001,"直送采购入库单不存在："+purchaseInRecordCode.get(0));
		}
		//保存关联关系
		saveFrontWarehouseRelation(purchaseOrderE, warehouseRecordDo.getRecordCode(),warehouseRecordDo.getId());
		//仓库直送入库单构建并且生成批次
		purchaseOrderService.addRwBatchAndReceipt(purchaseInRecordCode.get(0),null
				,WmsSyncTransferStatusVO.INIT_TRANSFER.getStatus(), WarehouseRecordConstant.INIT_SYNC_TRADE,false,rwBatchES);
		refRecordCodes.addAll(purchaseInRecordCode);
		//大仓出库单操作库存，更新实收数量及批次数据
		addPurchaseOut(stockOpFactoryDO,warehouseRecordE.getWarehouseRecordDetails(),purchaseOrderE.getSapPoNo(),rwBatchES);
		//如果是整单收0
		BigDecimal checkSkuActualQty = warehouseRecordE.getWarehouseRecordDetails().stream().map(WarehouseRecordDetail::getActualQty).reduce(BigDecimal.ZERO, BigDecimal::add);
		if(BigDecimal.ZERO.compareTo(checkSkuActualQty)==0){
			//直接返回，不给财务中台发消息
			log.warn("直送整单拒收，不发消息给财务中台recordCode:{}",warehouseRecordE.getRecordCode());
			return;
		}
		refRecordCodes.add(purchaseOrderE.getSapPoNo());
		//门店入库单(税筹)
		warehouseRecordE.setReceiptRecordCode(warehouseRecordE.getRecordCode());
		warehouseRecordE.setRecordType(WarehouseRecordTypeVO.SHOP_CHAIN_DIRECT_IN_RECORD.getType());
		warehouseRecordE.setRecordStatus(WarehouseRecordStatusVO.IN_ALLOCATION.getStatus());
		MessageDTO messageDTO = new MessageDTO();
		// 是否需要验证创建虚拟税筹出入库单据，根据单据类型
		TradePathInfoDTO tradePathDto = tradePathTools.buildTradePathDto(warehouseRecordE);
		if (tradePathDto.getNeedCreate()) {
			List<RealWarehouseDO> realWarehouseDO = tradePathTools.getRealWarehouseIdByCreateVirtualRecord(tradePathDto,warehouseRecordE.getRecordCode(), messageDTO);
			if (realWarehouseDO != null) {
				// 创建虚拟税筹出入库单据
				List<String> refRecordCodeList = createVirtualRecord(getTypeListByType(warehouseRecordE.getRecordType()), stockOpFactoryDO, warehouseRecordE, realWarehouseDO.get(0).getId());
				refRecordCodes.addAll(refRecordCodeList);
			}
		}
		messageDTO.setRefRecordCodes(refRecordCodes);
		messageDTO.setRecordCode(warehouseRecordE.getRecordCode());
		messageDTO.setRecordType(warehouseRecordE.getRecordType());
		messageDTO.setReceiveRecord(warehouseRecordE.getWmsRecordCode());
		messageDTO.setZtRecordCode(warehouseRecordE.getReceiptRecordCode());
		messageDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		orderCostMsgSender.sendMsg(messageDTO);
	}


	/**
	 * 直送业务保存前置单和门店入库单的关联关系
	 * @param purchaseOrderE
	 */
	private static void saveFrontWarehouseRelation(PurchaseOrderE purchaseOrderE, String recordCode,Long id) {
		FrontWarehouseRecordRelationDO relation = new FrontWarehouseRecordRelationDO();
		relation.setWarehouseRecordId(id);
		relation.setRecordCode(recordCode);
		relation.setFrontRecordId(purchaseOrderE.getId());
		relation.setFrontRecordType(purchaseOrderE.getRecordType());
		relation.setFrontRecordCode(purchaseOrderE.getRecordCode());
		frontWarehouseRecordRelationMapper.insertFrontWarehouseRecordRelation(relation);
	}

	/**
	 * 添加大仓出库单
	 * @param stockOpFactoryDO
	 * @param recordCode
	 * @return
	 */
	public static void addPurchaseOut(CoreStockOpFactoryDO stockOpFactoryDO, List<WarehouseRecordDetail> details,String recordCode){
		addPurchaseOut(stockOpFactoryDO,details,recordCode,null);
	}

	/**
	 * 添加大仓出库单,并生成批次
	 * @param stockOpFactoryDO
	 * @param details
	 * @param recordCode
	 * @param rwBatchES
	 */
	public static void addPurchaseOut(CoreStockOpFactoryDO stockOpFactoryDO, List<WarehouseRecordDetail> details,String recordCode,List<RwBatchE> rwBatchES){
		WarehouseRecordDo warehouseRecordDo=warehouseRecordMapper.getByCode(recordCode,null);
		if(null == warehouseRecordDo){
			throw new RomeException(ResCode.STOCK_ERROR_1001,"大仓出库单不存在");
		}
		List<WarehouseRecordDetailDo> detailList=warehouseRecordDetailMapper.queryListByRecordcode(recordCode);
		if(CollectionUtils.isEmpty(detailList)){
			throw new RomeException(ResCode.STOCK_ERROR_1001,"大仓出库单明细不存在");
		}

		Map<String, BigDecimal> detailMap=details.stream().collect(Collectors.toMap(WarehouseRecordDetail::getSkuCode, WarehouseRecordDetail::getActualQty,(v1, v2)->v2));
		for (WarehouseRecordDetailDo warehouseRecordDetailDo : detailList) {
			if(detailMap.containsKey(warehouseRecordDetailDo.getSkuCode())){
				warehouseRecordDetailDo.setActualQty(detailMap.get(warehouseRecordDetailDo.getSkuCode()));
			}
		}
		//删除当前出库单明细
		warehouseRecordDetailMapper.deleteWarehouseRecordDetailByRecordCode(recordCode);
		//将入库单明细插入到出库单
		List<WarehouseRecordDetailDo> warehouseDetailListOut=warehouseRecordDetailConvertor.entityListToDoList(details);
		for (WarehouseRecordDetailDo warehouseRecordDetail : warehouseDetailListOut) {
			warehouseRecordDetail.setWarehouseRecordId(warehouseRecordDo.getId());
			warehouseRecordDetail.setRecordCode(warehouseRecordDo.getRecordCode());
		}
		//重新插入出库单明细
		warehouseRecordDetailMapper.insertWarehouseRecordDetails(warehouseDetailListOut);
		//将出库单出库
		warehouseRecordMapper.updateToOutAllocation(warehouseRecordDo.getId());
		warehouseRecordDo.setWarehouseRecordDetails(warehouseDetailListOut);
		CoreRealStockOpDO ccoOut = stockOpFactoryDO.createCoreRealStockOpDO();
		initStockObj(warehouseRecordDo, ccoOut,false);
		coreRealWarehouseStockRepository.decreaseRealQty(ccoOut);
		if (!CollectionUtils.isEmpty(rwBatchES)){
			List<RwBatchE> list = new ArrayList<>();
			Map<String,WarehouseRecordDetailDo> skuCodeMaps= RomeCollectionUtil.listforMap(detailList,"skuCode");
			for (RwBatchE rwBatchE:rwBatchES){
				WarehouseRecordDetailDo detail = skuCodeMaps.get(rwBatchE.getSkuCode());
				if (Objects.isNull(detail)){
					continue;
				}
				RwBatchE rwBatchDTO = new RwBatchE();
				rwBatchDTO.setBatchCode(rwBatchE.getBatchCode());
				rwBatchDTO.setLineNo(String.valueOf(detail.getId()));
				rwBatchDTO.setSkuCode(detail.getSkuCode());
				rwBatchDTO.setRecordCode(detail.getRecordCode());
				rwBatchDTO.setWmsRecordCode(detail.getRecordCode());
				rwBatchDTO.setSkuId(detail.getSkuId());
				rwBatchDTO.setBusinessType(warehouseRecordDo.getBusinessType());
				rwBatchDTO.setProduceCode(rwBatchE.getProduceCode());
				rwBatchDTO.setProductDate(rwBatchE.getProductDate());
				rwBatchDTO.setSyncPurchaseStatus(WarehouseRecordConstant.INIT_SYNC_PURCHASE);
				rwBatchDTO.setSyncStatus(-1);
				rwBatchDTO.setQualityStatus(-1);
				//整单拒收使用实际收货数量
				rwBatchDTO.setActualQty(rwBatchE.getActualQty());
				rwBatchDTO.setSkuQty(rwBatchE.getSkuQty());
				rwBatchDTO.setUnitCode(rwBatchE.getUnitCode());
				rwBatchDTO.setBasicSkuQty(rwBatchE.getBasicSkuQty());
				rwBatchDTO.setInventoryType(1);
				rwBatchDTO.setCallbackNum(0);
				rwBatchDTO.setRealWarehouseId(warehouseRecordDo.getRealWarehouseId());
				Date startDate = DateUtil.offsiteDate(new Date() , Calendar.HOUR,-24);
				rwBatchDTO.setQualityTime(startDate);
				list.add(rwBatchDTO);
			}
			//生成批次
			if (!CollectionUtils.isEmpty(list)){
				rwBatchRepository.batchInsert(list, warehouseRecordDo.getRealWarehouseId());
			}
		}
	}

	/**
	 * 门店调拨专用，比较特殊
	 *
	 * @param isCrossOrg       是否跨组织
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE 调拨入库单
	 * @param outRecord    门店调拨对应的出库单，出库单在入库单时一起处理
	 */
	public static void processCost(boolean isCrossOrg, CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE, String outShopCode, WarehouseRecordE outRecord, String outDeliverFactory, String outShopWarehouseCode, String inDeliverFactory) {

		MessageDTO messageDTO = new MessageDTO();
		List<String> refRecordCodeList = new ArrayList<>();
		String refRecordCode = outRecord.getRecordCode();
		refRecordCodeList.add(refRecordCode);
		if (isCrossOrg) {
			//1、查询服务组网
			log.info("门店调拨，订单中心传递转置仓数据：recordCode =" + warehouseRecordE.getRecordCode() + "；outDeliverFactory=" + outDeliverFactory + "；outShopWarehouseCode=" + outShopWarehouseCode + "；inDeliverFactory= " + inDeliverFactory);
			RealWarehouseDO realWarehouseDO = null;
			if (StringUtils.isBlank(outDeliverFactory)) {
				outDeliverFactory = netWorkFacade.searchSourceFactory(outShopCode);
			}
			if (StringUtils.isBlank(inDeliverFactory)) {
				inDeliverFactory = netWorkFacade.searchSourceFactory(warehouseRecordE.getShopCode());
			}
			Date outOrInTime = outRecord.getOutOrInTime();
			if (outOrInTime == null) {
				outOrInTime = outRecord.getCreateTime();
			}
			//设置账务日期
			String financialDate =  DateUtil.formatDate(outOrInTime);
			//2、构建查询税筹公司/子公司的参数
			TradePathInfoDTO outTradePathDto = tradePathTools.buildTradePathDto(outDeliverFactory, outShopCode, CompanyTraderPathConstans.DS_REPLNISH_TYPE, financialDate);
			TradePathInfoDTO inTradePathDto = tradePathTools.buildTradePathDto(inDeliverFactory, warehouseRecordE.getShopCode(), warehouseRecordE.getRecordType() == 123 ?CompanyTraderPathConstans.LS_REPLNISH_TYPE : CompanyTraderPathConstans.DS_REPLNISH_TYPE, financialDate);

			//3、查询各自的交易路径
			List<RealWarehouseDO> outTemp = tradePathTools.getRealWarehouseIdByCreateVirtualRecord(outTradePathDto,warehouseRecordE.getRecordCode(), messageDTO);
			List<RealWarehouseDO> inTemp = tradePathTools.getRealWarehouseIdByCreateVirtualRecord(inTradePathDto,warehouseRecordE.getRecordCode(), messageDTO);
			//查询转置仓
			if (StringUtils.isBlank(outShopWarehouseCode)) {
				//4、转置仓都用出库单的
				List<RealWarehouseDO> list = tradePathTools.getRealWarehouseMapper().queryRealWarehouseByFactoryCode(outDeliverFactory);
				for (RealWarehouseDO warehouse : list) {
					if (RealWarehouseTypeVO.RW_TYPE_24.getType().equals(warehouse.getRealWarehouseType())) {
						realWarehouseDO = warehouse;
						break;
					}
				}
			} else {
				realWarehouseDO = tradePathTools.getRealWarehouseMapper().queryByOutCodeAndFactoryCode(outShopWarehouseCode, outDeliverFactory);
			}
			log.info("门店调拨，最终使用的转置仓数据：recordCode =" + warehouseRecordE.getRecordCode() + "；outDeliverFactory=" + outDeliverFactory + "；outShopWarehouseCode=" + outShopWarehouseCode + "；inDeliverFactory= " + inDeliverFactory);
			if (null == realWarehouseDO) {
				throw new RomeException("1001", "转置仓不存在，deliverFactory=" + outDeliverFactory);
			}

			//5、退货税筹或子公司公司虚拟出入库单
			if (outTemp != null) {
				List<String> refRecordCodes = createVirtualRecord(Arrays.asList(WarehouseRecordTypeVO.RETURN_VIRTUAL_IN, WarehouseRecordTypeVO.RETURN_VIRTUAL_OUT), stockOpFactoryDO, warehouseRecordE, outTemp.get(0).getId());
				refRecordCodeList.addAll(refRecordCodes);
			}
			//4、股份转置仓虚拟出入库
			List<String> refRecord = createVirtualRecord(Arrays.asList(WarehouseRecordTypeVO.RETURN_VIRTUAL_IN, WarehouseRecordTypeVO.VIRTUAL_OUT), stockOpFactoryDO, warehouseRecordE, realWarehouseDO.getId());
			refRecordCodeList.addAll(refRecord);
			//5、入库税筹或子公司虚拟出入库单，直营转加盟支持多级交易路径信息
			if (inTemp != null) {
				for (RealWarehouseDO inWarehouseDo : inTemp) {
					List<String> refRecordCodes = createVirtualRecord(Arrays.asList(WarehouseRecordTypeVO.VIRTUAL_IN, WarehouseRecordTypeVO.VIRTUAL_OUT), stockOpFactoryDO, warehouseRecordE, inWarehouseDo.getId());
					refRecordCodeList.addAll(refRecordCodes);
				}
			}
		}
		messageDTO.setRefRecordCodes(refRecordCodeList);
		messageDTO.setRecordCode(warehouseRecordE.getRecordCode());
		messageDTO.setRecordType(warehouseRecordE.getRecordType());
		messageDTO.setReceiveRecord(warehouseRecordE.getWmsRecordCode());
		messageDTO.setZtRecordCode(warehouseRecordE.getReceiptRecordCode());
		messageDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		orderCostMsgSender.sendMsg(messageDTO);
	}

	/**
	 * 获取创建虚拟税筹仓实仓Id,根据后置单
	 *
	 * @param warehouseRecordE
	 * @return 返回为null证明不需要创虚拟税筹单据
	 */
	private static Long getRealWarehouseIdByCreateVirtualRecord(WarehouseRecordE warehouseRecordE) {
		// 给勇军留的，需要扩展接口
		return 443018L; // X997-A001
	}

	private static List<WarehouseRecordTypeVO> multiPathTypeList(Integer recordType) {
		if (Objects.equals(WarehouseRecordTypeVO.LS_REPLENISH_OUT_WAREHOUSE_RECORD.getType(),recordType)
				|| Objects.equals(WarehouseRecordTypeVO.LS_COLD_OVER_STOCK_OUT_RECORD.getType(),recordType)
				|| recordType == 139 || recordType == 49 || recordType == 92 || recordType == 159) {
			return Arrays.asList(WarehouseRecordTypeVO.VIRTUAL_IN, WarehouseRecordTypeVO.VIRTUAL_OUT);
		}

		if(recordType == 27 || recordType == 117 || recordType == 91){
			return Arrays.asList(WarehouseRecordTypeVO.RETURN_VIRTUAL_IN, WarehouseRecordTypeVO.RETURN_VIRTUAL_OUT);
		}
		return null;
	}

	/**
	 * 虚拟单据遵循先入后出原则
	 *
	 * @param recordType
	 * @return
	 */
	private static List<WarehouseRecordTypeVO> getTypeListByType(Integer recordType) {
		switch (recordType) {
			case 11:
			case 12:
			case 167:
			case 175:
			case 176:
			case 148:
			case 150:
			case 152:
			case 139:
			case 42:
			case 45://直送门店入库单
			case 92://销售中心预约do单
			case 159://销售系统预售配送出库单
				return Arrays.asList(WarehouseRecordTypeVO.VIRTUAL_IN, WarehouseRecordTypeVO.VIRTUAL_OUT);
			case 49://仓库领用出库单
				return Arrays.asList(WarehouseRecordTypeVO.VIRTUAL_IN, WarehouseRecordTypeVO.WAREHOUSE_USE_VIRTUAL_OUT);
			case 70://正向差异门店责任
			case 76://正向差异仓库责任
			case 80://差异管理-增加收货仓仓库存[退货.仓库责任]
			case 81://差异管理-增加收货仓仓库存[退货.物流责任]
				return Arrays.asList(WarehouseRecordTypeVO.DISPARITY_VIRTUAL_IN, WarehouseRecordTypeVO.DISPARITY_VIRTUAL_OUT);
			case 23:
			case 27:
			case 115://直营冷链退货
			case 117://加盟冷链退货
			case 91://销售中心退货单
				return Arrays.asList(WarehouseRecordTypeVO.RETURN_VIRTUAL_IN, WarehouseRecordTypeVO.RETURN_VIRTUAL_OUT);
		}
		throw new RomeException("1001", "不支持的单据类型");

	}

	/**
	 * 统一创建虚拟出入库，将stockOpFactoryDO传入，方便回滚，后续再自己的事务调stockOpFactoryDO.commit()方法，并统一回滚
	 *
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE
	 * @param realId
	 */
	private static List<String> createVirtualRecord(List<WarehouseRecordTypeVO> typeList, CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE, Long realId) {
		return createVirtualRecord(false, typeList, stockOpFactoryDO, warehouseRecordE, realId);
	}
	/**
	 * 统一创建虚拟出入库，将stockOpFactoryDO传入，方便回滚，后续再自己的事务调stockOpFactoryDO.commit()方法，并统一回滚
	 *
	 * @param stockOpFactoryDO
	 * @param warehouseRecordE
	 * @param realId
	 * @param isReverse true表示是冲销单据，这个时候需要反向处理，本来是出的需要作成入，本来是入的需要作成出
	 */
	private static List<String> createVirtualRecord(boolean isReverse , List<WarehouseRecordTypeVO> typeList, CoreStockOpFactoryDO stockOpFactoryDO, WarehouseRecordE warehouseRecordE, Long realId) {
		WarehouseRecordDo warehouseRecordDo = new WarehouseRecordDo();
		BeanUtils.copyProperties(warehouseRecordE, warehouseRecordDo);
		//copyProperties不能处理不同对象的相同属性，所以这里用for来单独处理明细
		List<WarehouseRecordDetailDo> warehouseRecordDetails = new ArrayList<>();
		for (WarehouseRecordDetail detail : warehouseRecordE.getWarehouseRecordDetails()) {
			WarehouseRecordDetailDo detailDo = new WarehouseRecordDetailDo();
			BeanUtils.copyProperties(detail, detailDo);
			warehouseRecordDetails.add(detailDo);
		}
		warehouseRecordDo.setWarehouseRecordDetails(warehouseRecordDetails);
		CoreRealStockOpDO cco;
		//统一生成出入库单
		List<String> result = new ArrayList<>(2);
		for (WarehouseRecordTypeVO typeVO : typeList) {
			warehouseRecordDo.setRecordCode(orderUtilService.queryOrderCode(typeVO.getCode()));
			result.add(warehouseRecordDo.getRecordCode());
			warehouseRecordDo.setRecordType(typeVO.getType());
			warehouseRecordDo.setBusinessType((!isReverse && typeVO.getBusinessType() == 1) || (isReverse && typeVO.getBusinessType() == 2) ? 1 : 2);
			warehouseRecordDo.setRecordStatus((!isReverse && typeVO.getBusinessType() == 1) || (isReverse && typeVO.getBusinessType() == 2) ? 11 : 12);
			warehouseRecordDo.setRealWarehouseId(realId);
			warehouseRecordDo.setOutOrInTime(new Date());
			warehouseRecordDo.setSyncDispatchStatus(0);
			warehouseRecordDo.setSyncTransferStatus(0);
			warehouseRecordDo.setSyncWmsStatus(0);
			warehouseRecordDo.setSyncFulfillmentStatus(0);
			warehouseRecordDo.setSyncOrderStatus(0);
			warehouseRecordDo.setSyncPurchaseStatus(0);
			warehouseRecordDo.setSelfTakeout(0);
			warehouseRecordDo.setSyncTradeStatus(0);
			warehouseRecordMapper.insertWarehouseRecord(warehouseRecordDo);
			for (WarehouseRecordDetailDo detail : warehouseRecordDo.getWarehouseRecordDetails()) {
				detail.setRecordCode(warehouseRecordDo.getRecordCode());
				detail.setWarehouseRecordId(warehouseRecordDo.getId());
			}
			warehouseRecordDetailMapper.insertWarehouseRecordDetails(warehouseRecordDo.getWarehouseRecordDetails());
			cco = stockOpFactoryDO.createCoreRealStockOpDO();
			initStockObj(warehouseRecordDo, cco,isReverse);
			if (cco.getDetailDos().size() > 0) {
				if ((!isReverse && typeVO.getBusinessType() == 1) || (isReverse && typeVO.getBusinessType() == 2)) {
					coreRealWarehouseStockRepository.decreaseRealQty(cco);
				} else {
					coreRealWarehouseStockRepository.increaseRealQty(cco);
				}
			}
		}
		return result;
	}


	/**
	 * 增加实体仓库库存的对象
	 * @param warehouseRecordDo
	 * @param coreRealStockOpDO
	 * @param isReverse 是否冲销
	 */
	private static void initStockObj(WarehouseRecordDo warehouseRecordDo, CoreRealStockOpDO coreRealStockOpDO,boolean isReverse) {
		List<CoreRealStockOpDetailDO> increaseDetails = new ArrayList<>();
		for (WarehouseRecordDetailDo detail : warehouseRecordDo.getWarehouseRecordDetails()) {
			//实收数量为0的不处理在途的增加和减少
			if (BigDecimal.ZERO.compareTo(detail.getActualQty()) == 0) {
				continue;
			}
			CoreRealStockOpDetailDO coreRealStockOpDetailDO = new CoreRealStockOpDetailDO();
			coreRealStockOpDetailDO.setSkuId(detail.getSkuId());
			coreRealStockOpDetailDO.setSkuCode(detail.getSkuCode());
			coreRealStockOpDetailDO.setRealQty(detail.getActualQty());
			coreRealStockOpDetailDO.setRealWarehouseId(warehouseRecordDo.getRealWarehouseId());
			if(isReverse){
				//冲销的中间单据不校验是否够扣库存
				coreRealStockOpDetailDO.setCheckBeforeOp(false);
			}
			increaseDetails.add(coreRealStockOpDetailDO);
		}
		coreRealStockOpDO.setRecordCode(warehouseRecordDo.getRecordCode());
		coreRealStockOpDO.setTransType(warehouseRecordDo.getRecordType());
		coreRealStockOpDO.setDetailDos(increaseDetails);
	}
	/**
	 * 出库单发送消息给库存成本中心
	 *
	 * @param warehouseRecordE
	 */
	public static void sendOutOrderToCostMsg(AbstractWarehouseRecord warehouseRecordE) {
		MessageDTO messageDTO = new MessageDTO();
		messageDTO.setRecordCode(warehouseRecordE.getRecordCode());
		messageDTO.setRecordType(warehouseRecordE.getRecordType());
		messageDTO.setZtRecordCode(warehouseRecordE.getRecordCode());
		messageDTO.setRealWarehouseId(warehouseRecordE.getRealWarehouseId());
		orderCostMsgSender.sendMsg(messageDTO);
	}

	/**
	 * 如果配置了如下渠道,就需要和财务中台过账,并操作库存
	 * @param channelCode
	 * @return
	 */
	public static boolean isSpecialChannel(String channelCode){
		if(StringUtils.isBlank(channelCode)){
			return false;
		}
		String channelCodes = BaseinfoConfiguration.getInstance().get("wdt_channel_codes", "channel_codes_146R");
		if(StringUtils.isNotEmpty(channelCodes) && channelCodes.contains(channelCode)){
			return true;
		}else{
			return false;
		}
	}
}
