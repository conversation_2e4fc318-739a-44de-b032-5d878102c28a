package com.rome.stock.core.infrastructure.mapper;

import com.rome.stock.core.api.dto.frontrecord.SkuStock;
import com.rome.stock.core.api.dto.groupbuy.GroupWarehouseRecordDetailTemplate;
import com.rome.stock.core.api.dto.qry.*;
import com.rome.stock.core.api.dto.warehouserecord.SaleTobWarehouseRecordDTO;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordDetail;
import com.rome.stock.core.domain.entity.warehouserecord.WarehouseRecordE;
import com.rome.stock.core.infrastructure.dataobject.WarehouseRecordDo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * WarehouseRecordMapper类的实现描述：出入库单mapper
 *
 * <AUTHOR> 2019/6/26 17:17
 */
public interface WarehouseRecordMapper {

	/**
	 * 保存出入库单
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,channel_type,channel_code,merchant_id,out_create_time
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出入库单(无渠道)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertNoChannelWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存入库单(无渠道)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time,receiver_time
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertNoChannelInWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存入库单(无渠道含批次状态)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time,receiver_time,batch_status
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertInWarehouseRecordIncludeBatchStatus(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存入库单(无渠道含批次状态和CMP状态)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time,receiver_time,batch_status
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertOutWarehouseRecordIncludeBatchCmp(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出库单(无渠道)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time,delivery_time
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertNoChannelOutWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出库单(无渠道含批次状态)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time,delivery_time,batch_status
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertOutWarehouseRecordIncludeBatchStatus(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出库单(无渠道含批次状态和CMP)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,merchant_id,out_create_time,delivery_time
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertInWarehouseRecordIncludeBatchCmp(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出入库单(包含用户信息)
	 * 保存字段：record_code,business_type,record_status,record_type,user_code,real_warehouse_id,channel_type,channel_id,merchant_id,out_create_time,mobile
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertUserWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出入库单(包含用户信息,批次状态)
	 * 保存字段：record_code,business_type,record_status,record_type,user_code,real_warehouse_id,channel_type,channel_id,merchant_id,out_create_time,mobile,batch_status
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertUserWarehouseRecordIncludeBatchStatus(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 保存出入库单(包含用户退货信息)
	 * 保存字段：record_code,business_type,record_status,record_type,user_code,real_warehouse_id,channel_type,channel_id,merchant_id,out_create_time,mobile,reason
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertUserReturnWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 更新仓库单完成状态
	 * 
	 * @param id
	 */
	void updateCompleteStatus(long id);

	/**
	 * 查询团购仓出库单
	 * @param condition
	 * @return
	 */
	List<WarehouseRecordDo> queryGroupWarehouseRecordList(GroupWarehouseRecordCondition condition);

	/**
	 * 导出查询团购仓出库单的数量
	 * @param condition
	 * @return
	 */
	int queryGroupWarehouseRecordCodeList(GroupWarehouseRecordCondition condition);

	/**
	 * 查询退货单
	 */
	List<WarehouseRecordDo> querySalesReturnWarehouseRecordList(SalesReturnRecordParamDTO salesReturnRecordParamDTO);

	/**
	 * 查询入库单 查询字段： 查询条件：
	 */
	List<WarehouseRecordDo> queryInWarehouseRecordList(@Param("warehouseRecord") WarehouseRecordDo warehouseRecord);

	/**
	 * 查询入库单页面
	 */
	List<WarehouseRecordDo> queryInWarehouseRecordPage(@Param("warehouseRecord") WarehouseRecordDo warehouseRecord,
			@Param("types") List<Integer> types, @Param("ids") List<Long> ids,
			@Param("realWarehouseIds") List<Long> realWarehouseIds);

	/**
	 * 查询出库单页面
	 */
	List<WarehouseRecordDo> queryOutWarehouseRecordPage(@Param("warehouseRecord") WarehouseRecordDo warehouseRecord,
			@Param("types") List<Integer> types, @Param("ids") List<Long> ids,
			@Param("realWarehouseIds") List<Long> realWarehouseIds);

	/**
	 * 查询出库单
	 * 
	 * @param warehouseRecord
	 * @return
	 */
	List<WarehouseRecordDo> queryOutWarehouseRecordList(WarehouseRecordDo warehouseRecord);

	/**
	 * 采购入库修改操作
	 * 
	 * @param warehouseRecordDo
	 */
	void updateWarehouseRecord(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 更新仓库单完成状态
	 *
	 * @param id
	 */
	void updateCompleteStatus(@Param("id") Long id, @Param("realWarehouseId") Long realWarehouseId,
			@Param("virtualWarehouseId") Long virtualWarehouseId);

	/**
	 * 更新入库单为初始化状态，大仓采购流程用:修改采购单
	 * 
	 * @param id
	 */
	void updateToInitStatus(@Param("id") Long id);

	/**
	 * 根据单据编号和类型查询订单
	 */
	WarehouseRecordDo getByCode(@Param("code") String code, @Param("recordType") Integer recordType);

	WarehouseRecordDo getById(@Param("id") Long id);

	/**
	 * 根据id查询wms同步状态 0 为无需同步 1为未同步 2为已同步
	 * 
	 * @param id
	 * @return
	 */
	Integer getWarehouseRecordSyncWmsStatusById(@Param("id") Long id);

	List<WarehouseRecordDo> queryWareHouseRecordList(WareHouseRecordCondition wareHouseRecordCondition);

	Integer updateRecordSyncStatusToSynchronized(@Param("recordCodeList") List<String> recordCodeList);

	/**
	 * 更新出库单状态由初始化到已出库
	 */
	int updateToOutAllocation(@Param("id") Long id);

    /**
	 * 更新出库单状态由已出库到已取消
	 */
	int updateToOutFromLocked(@Param("id") Long id);

	/**
	 * 更新出库单状态由已出库到已取消
	 */
	int updateToCancelFromOut(@Param("id") Long id);

	/**
	 * 更新预约单取消状态
	 * @param id
	 * @return
	 */
	int updateCancelReservation(@Param("id")Long id);

	/**
	 * 更新出库单状态由已锁定到已取消
	 */
	int updateToCancelFromLocked(@Param("id") Long id);

	/**
	 * 更新单据状态由初始化到已入库
	 */
	Integer updateToInAllocation(@Param("id") Long id, @Param("outOrInTime") Date outOrInTime);

	/**
	 * 更新单据状态
	 * @param status
	 * @param id
	 * @return
	 */
	Integer updateWarehouseRecordStatus(@Param("status")Integer status,@Param("id")Long id);

	/**
	 * 更新预约单do单信息
	 * @param warehouseRecordDo
	 * @return
	 */
	Integer updateWarehouseRecordByGroupBy(WarehouseRecordDo warehouseRecordDo);

	Integer updateWarehouseRecordByWarehouseId(WarehouseRecordDo warehouseRecordDo);

	Integer updateWarehouseRecordByWarehouseIdAndStatus(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 根据id批量查询出入库单数据（无重复id）
	 */
	List<WarehouseRecordDo> getByIds(@Param("ids") Set<Long> ids);

	/**
	 * 根据id批量查询出入库单数据
	 * 
	 * @param ids
	 * @return
	 */
	List<WarehouseRecordDo> queryWarehouseRecordByIds(@Param("ids") List<Long> ids);

	/**
	 * 更新出库单状态为已取消，wms同步状态为无需同步，从已同步wms改为已取消
	 * @param id
	 * @return
	 */
	int updateToCanceledFromHasSync(@Param("id") Long id);

	/**
	 * 更新出库单状态为已取消，wms同步状态为无需同步 ，从未同步wms改为已取消
	 * @param id
	 * @return
	 */
	int updateToCanceledFromUnSync(@Param("id") Long id);

	/**
	 * 取消采购退供单
	 * @param id
	 * @return
	 */
	int updateToCanceledPurchaseReturn(@Param("id") Long id);

	/**
	 * 更新出库单状态为已取消，wms同步状态为无需同步
	 * @param id
	 * @return
	 */
	int updateToCanceled(@Param("id") Long id);

	int updateToCanceledFromComplete(@Param("id") Long id);

	int updateToCanceledFromBack(@Param("id") Long id);

	/**
	 * 更新叫货的出库单数据为已取消(有初始化---已取消并且没有下发到仓库的单据)
	 * 
	 * @param id
	 * @param isForceCancle
	 * @param id
	 * @return
	 */
	int updateReplenishToCanceled(@Param("id") Long id, @Param("isForceCancle") Integer isForceCancle);

    /**
     * 更新叫货的出库单数据为已取消(有初始化---已取消并且没有下发到仓库的单据)
     *
     * @param id
     * @param isForceCancle
     * @param id
     * @return
     */
    int updateWhToCanceled(@Param("id") Long id, @Param("isForceCancle") Integer isForceCancle);

	int batchUpdateWhToCanceled(@Param("ids") List<Long> ids, @Param("isForceCancle") Integer isForceCancle);
	/**
	 * 更新叫货的出库单数据为转移(有初始化---已取消并且没有下发到仓库的单据)
	 *
	 * @param id
	 * @param id
	 * @return
	 */
	int updateWhToTransfer(@Param("id") Long id);
	/**
	 * 根据单据号更新批次状态
	 * 
	 * @param recordCode
	 */
	void updateRecordBatchStatus(@Param("recordCode") String recordCode);

	/**
	 * 将出库单批次库存库存状态改为待扣减
	 * 
	 * @param recordCode
	 */
	void updateRecordBatchStatusToInit(@Param("recordCode") String recordCode);

	/**
	 * 完成状态改为待处理
	 * 
	 * @param recordCode
	 */
	void updateRecordBatchStatusToInitFromComplete(@Param("recordCode") String recordCode);

	/**
	 * 根据单据号查询批次状态
	 * 
	 * @param recordCode
	 * @return
	 */
	Integer queryWarehouseBatchStatus(@Param("recordCode") String recordCode);

	/**
	 * 根据条件查询出入库单列表
	 * 
	 * @param warehouseRecord
	 * @return
	 */
	List<WarehouseRecordDo> queryWarehouseRecordListByModel(WarehouseRecordDo warehouseRecord);

	/**
	 * 根据条件查询出入库单列表[电商过账用]
	 * 
	 * @param warehouseRecord
	 * @return
	 */
	List<WarehouseRecordDo> queryWarehouseRecordListByModelForOnlineAccount(WarehouseRecordDo warehouseRecord);

	/**
	 * 在时间范围内查询批次状态为未处理的出库单
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	List<WarehouseRecordDo> queryWareHouseRecordListByBatchStatus(@Param("startDate") Date startDate,
			@Param("endDate") Date endDate);

	/**
	 * 批次扣减失败更新状态为异常
	 * 
	 * @param id
	 */
	void updateRecordBatchFailStatus(Long id);

	/**
	 * 根据sap单号查询出入库单
	 * 
	 * @param sapCode
	 * @return
	 */
	List<WarehouseRecordDo> getBySapCode(@Param("sapCode") String sapCode);

	/**
	 * 根据sap单号和仓库查询出入库单
	 *
	 * @param sapCode
	 * @param rwId
	 * @return
	 */
	List<WarehouseRecordDo> getBySapCodeAndRwId(@Param("sapCode") String sapCode, @Param("rwId") Long rwId);

	int updateRecordSyncWmsFailTime(@Param("recordCodes") List<String> recordCodes);

	/**
	 * 保存出入库单(包含派车状态)
	 * 保存字段：record_code,business_type,record_status,record_type,real_warehouse_id,channel_type,channel_code,merchant_id,out_create_time,
	 * sync_dispatch_status
	 * 
	 * @param warehouseRecordDo
	 * @return
	 */
	void insertWrWithDispatchInfo(WarehouseRecordDo warehouseRecordDo);

	/**
	 * 批量修改wms状态为待下发
	 * 
	 * @param wrDo
	 * @return
	 */
	int updateSyncStatusAndSapCode(WarehouseRecordDo wrDo);

	int updateTmsMsg(WarehouseRecordDo wrDo);


	/**
	 * 批量修改wms状态为待下发
	 *
	 * @param wrDo
	 * @return
	 */
	int updateSyncWmsStatus(WarehouseRecordDo wrDo);

	/**
	 * 批量修改wms状态为待下发
	 *
	 * @param wrDo
	 * @return
	 */
	int updateTmsCodeAndSapCode(WarehouseRecordDo wrDo);

	/**
	 * 批量修改sap单号，不修改下发状态
	 *
	 * @param wrDo
	 * @return
	 */
	int updateAndSapCodeAndNoSyncStatus(WarehouseRecordDo wrDo);

	/**
	 * 修改sapCode
	 *
	 * @param wrDo
	 * @return
	 */
	int updateSapCode(WarehouseRecordDo wrDo);

	/**
	 * 修改派车状态为待派车下发
	 * 
	 * @param recordId
	 * @return
	 */
	int updateDispatchStatusToNeedDispatch(@Param("id") Long recordId);

	/**
	 * 更新捋单系统状态
	 * @param id
	 * @param status
	 * @return
	 */
	int updateFulfillmentStatus(@Param("id")Long id);

	/**
	 * 更新状态为待推送销售中心
	 * @param id
	 * @return
	 */
	int updateSyncTradeToBeStatus(@Param("id")Long id);

	/**
	 * 更新第一次通知采购中心标识
	 * @param recordCode
	 * @return
	 */
	int updateTenantIdByRecordCode(@Param("recordCode")String recordCode);

	/**
	 * 更新收货完成标识
	 * @param recordCode
	 * @return
	 */
	int updateReceiptCompleted(@Param("recordCode")String recordCode);

	/**
	 * 更新收货完成标识并设置出入库时间
	 * @param recordCode
	 * @return
	 */
	int updateReceiptCompletedAndInTime(@Param("recordCode")String recordCode);

	/**
	 * 更新待推送采购中心
	 * @param id
	 * @return
	 */
	int updateSyncPurchaseToBeStatus(@Param("id")Long id);

	/**
	 * 批量更新待推送质检中心
	 * @param recodeCodeList
	 * @return
	 */
	int updateSyncPurchaseStatusByRecordCodes(@Param("recodeCodeList") List<String> recodeCodeList);

	/**
	 * 更新推送采购中心成功
	 * @param recordCode
	 * @return
	 */
	int updateSyncPurchaseSuccess(@Param("recordCode")String recordCode);


	/**
	 * 修改为下发排车成功
	 * 
	 * @param recordId
	 * @return
	 */
	int updateDispatchSucc(@Param("id") Long recordId);

	/**
	 * 更新sap交货单号
	 * 
	 * @param recordId
	 * @param sapCode
	 * @return
	 */
	int updateDeliveryCode(@Param("id") Long recordId, @Param("sapCode") String sapCode);

	int updateDeliveryCodeByCode(@Param("recordCode") String recordCode, @Param("sapCode") String sapCode);
	/**
	 * 查询待过账的单据
	 * 
	 * @param page
	 *            页数
	 * @param maxResult
	 *            最大记录数
	 * @return
	 */
	List<WarehouseRecordDo> getWaitTransferOrder(@Param("realWarehouseType") Integer realWarehouseType , @Param("minId") Long minId, @Param("maxResult") int maxResult);

	/**
	 * 修改为下发过账成功
	 * 
	 * @param recordId
	 * @return
	 */
	int updateTransferSucc(@Param("id") Long recordId);

	/**
	 * 修改为下过账中
	 *
	 * @param recordId
	 * @return
	 */
	int updateTransferForProcessing(@Param("id") Long recordId);

	/**
	 * 过账状态从过账中修改为待过账
	 *
	 * @param recordCode
	 * @return
	 */
	int updateWaitTransferFromProcessing(@Param("recordCode") String recordCode);

	/**
	 * 过账状态从过账中修改过账成功
	 *
	 * @param recordCode
	 * @return
	 */
	int updateSuccessForProcessing(@Param("recordCode") String recordCode);

	/**
	 * 修改后置单同步交易状态
	 * 
	 * @param id
	 * @return
	 */
	int updateSyncTradeStatus(@Param("id") Long id);

	// List<WarehouseRecordDo> getWaitSyncCombineToFulfillmentOrder(@Param("page")
	// int page, @Param("maxResult") int maxResult);

	List<WarehouseRecordDo> getWaitSyncDeliveryToFulfillmentOrder(@Param("page") int page,
			@Param("maxResult") int maxResult);

	// /**
	// * 修改为同步捋单系统成功
	// * @param recordId
	// * @return
	// */
	// int updateToHasSyncCombine(@Param("id") Long recordId);

	/**
	 * 修改为已同步交货信息给屡单系统
	 * 
	 * @param recordId
	 * @return
	 */
	int updateToHasSyncDelivery(@Param("id") Long recordId);

	/**
	 * 修改为已同步交货信息给销售中心
	 *
	 * @param recordId
	 * @return
	 */
	int updateToGroupPurchaseHasSyncDelivery(@Param("id") Long recordId);

	/**
	 * 查询未推送cmp出入库单
	 * 
	 * @param typeList
	 * @param subDay
	 * @return
	 */
	List<WarehouseRecordDo> queryWaitWarehouseCmpList(@Param("typeList") List<Integer> typeList, @Param("subDay") Integer subDay);

	/**
	 * 查询未推送cmp7的出入库单
	 * @param typeList
	 * @param subDay
	 * @return
	 */
	List<WarehouseRecordDo> queryWaitCmp7WarehouseCmpList(@Param("typeList") List<Integer> typeList, @Param("subDay") Integer subDay);

	/**
	 * 查询补货待推送cmp的结果单据
	 * @param typeList
	 * @param subDay
	 * @return
	 */
	List<WarehouseRecordDo> queryWaitCmpResultList(@Param("typeList") List<Integer> typeList, @Param("subDay") Integer subDay);

	/**
	 * 更新cmp完成状态
	 * 
	 * @param id
	 */
	void updateCmpStatusComplete(Long id);

	/**
	 * 更新cmp7的状态
	 * @param id
	 */
	void updateCmp7StatusComplete(Long id);

	/**
	 * 更新推送cmp结果成功状态
	 * @param id
	 * @return
	 */
	int updateShopResultSuccess(Long id);

	/**
	 * 查询待推送到交易中心订单
	 */
	List<WarehouseRecordDo> queryBySyncTradeStatus(@Param("page") int page, @Param("maxResult") int maxResult);

	/**
	 * 查询待推送到交易中心订单,不限定时间
	 */
	List<WarehouseRecordDo> queryAllBySyncTradeStatus(@Param("page")int page, @Param("maxResult")int maxResult);

	/**
	 * 修改为下发待过账
	 * 
	 * @param recordId
	 * @return
	 */
	int updateToWaitTransfer(@Param("id") Long recordId);


	/**
	 * 修改cmp7推送结果状态
	 * @param recordId
	 * @return
	 */
   int updateCmp7Status(@Param("id") Long recordId,@Param("status") Integer status,@Param("operator") String operator);

	/**
	 * 修改cmp5/cmp6推送结果状态
	 * @param recordId
	 * @return
	 */
	int updateCmp7StatusAndSyncDispatchStatus(@Param("id") Long recordId,@Param("status") Integer status);

	/**
	 * 更新用户编码
	 * @param userCode
	 * @param id
	 * @return
	 */
   int updateUserCode(@Param("userCode")String userCode,@Param("id")Long id);

	/**
	 * 根据查询条件查找列表
	 *
	 * @param condition
	 * @param types
	 * @return
	 */
	List<SaleTobWarehouseRecordDTO> queryWarehouseRecordListByCondition(
			@Param("condition") SaleTobWarehouseRecordCondition condition, @Param("types") Set<Integer> types);

	/**
	 * 根据出库单编码获取所有的出库单类型
	 * 
	 * @param recordCodes
	 * @return
	 */
	List<WarehouseRecordDo> getRecordTypeByRecordCodes(@Param("recordCodes") List<String> recordCodes);

	/**
	 * 查询未处理门店批次出入库单
	 * 
	 * @param startDate
	 * @param endDate
	 * @param rows
	 * @return
	 */
	List<Long> queryWarehouseBatchList(@Param("startDate") Date startDate, @Param("endDate") Date endDate,
			@Param("rows") Integer rows);

	List<WarehouseRecordDo> queryWarehouseRecordListByCode(@Param("sapOrderCodes") List<String> sapOrderCodes,
			@Param("tmsRecordCode") String tmsRecordCode);

	/**
	 * @Description: 查询待取消PO <br>
	 *
	 * <AUTHOR> 2019/8/26
	 * @param warehouseRecord
	 * @return
	 */
	List<WarehouseRecordDo> queryNeedCancelWarehouseRecordList(WarehouseRecordDo warehouseRecord);

	/**
	 * 根据SAP交货单号查询单据编码
	 * 
	 * @param sapOrderCodes
	 * @return
	 */
	List<String> queryRecordCodeBySapOrderCodes(@Param("sapOrderCodes") List<String> sapOrderCodes);


    /**
     * @Description: 修改单据状态和wms时间 <br>
     *
     * <AUTHOR> 2019/10/22
     * @param recordId
     * @param outOrInTime
	 * @return
     */
	int updateOutAllocation(@Param("recordId") Long recordId, @Param("orderConfirmTime") String orderConfirmTime, @Param("outOrInTime") Date outOrInTime);

	Integer updateInAllocation(@Param("recordId") Long recordId, @Param("orderConfirmTime") String orderConfirmTime, @Param("outOrInTime") Date outOrInTime);


    /**
     * 更新未同步派车单号
     *
     * <AUTHOR> 2019/10/16
     * @param unSyncCodes
     * @param tmsCode
	 * @return
     */
	Integer updateUnSyncWmsRecord(@Param("unSyncCodes") List<String> unSyncCodes, @Param("tmsCode") String tmsCode,@Param("logisticsCode") String logisticsCode);

	/**
	 * 更新同步成功派车单号
	 *
	 * <AUTHOR> 2019/10/16
	 * @param syncCodes
	 * @param tmsCode
	 * @return
	 */
	Integer updateSyncWmsRecord(@Param("syncCodes") List<String> syncCodes, @Param("tmsCode") String tmsCode);

	/**
	 * @Description: 根据单据失败时间及失败状态 <br>
	 *
	 * <AUTHOR> 2019/12/10
	 * @param recordCode
	 * @return
	 */
	Integer updateSyncWmsFailTimeAndStatus(String recordCode);

	/**
	 * 根据交货单号查询出库单号
	 * @param sapOrderCode
	 * @return
	 */
	String queryRWCodeBySapOrderCode(@Param("sapOrderCode")String sapOrderCode);

	/**
	 * 根据sapOrderCode查询入库单号
	 * @param sapOrderCode
	 * @return
	 */
	String queryRecordCodeBySapRecordCode(@Param("sapOrderCode")String sapOrderCode);



	String queryRecordCodeBySapRecordCodeV2(@Param("sapOrderCode")String sapOrderCode);

	/**
	 * 更新派车单号
	 * @param recordCode
	 * @param expressCode
	 * @return
	 */
	int updateTmsRecordCodeByRecordCode(@Param("recordCode")String recordCode,@Param("expressCode") String expressCode);

	/**
	 * 定时查询待同步交货信息给销售中心
	 * @param page
	 * @param maxResult
	 * @return
	 */
	List<Long> getWaitSyncDeliveryToGroupPurchase(@Param("page")Integer page,@Param("maxResult") Integer maxResult);

	/**
	 * 查询团购仓出库单明细列表
	 * @param condition
	 * @return
	 */
	List<GroupWarehouseRecordDetailTemplate> queryGroupWarehouseRecordDetailTemplateList(GroupWarehouseRecordCondition condition);

	/**
	 * 团购退货收货入库状态修改
	 * @param id
	 * @param userId
	 * @return
	 */
    int reservationReturninWarehouse(@Param("id") Long id, @Param("userId") Long userId);

	/**
	 * 更新后置单为待下发
	 *
	 * @param id
	 */
	void updateRecordWmsSyncStatus(Long id);

	/**
	 * 修改wms下发状态为已回撤
	 * @param id
	 * @param userId
	 * @return
	 */
	Integer updateWmsStatusToBackCancle(@Param("id") Long id, @Param("userId") Long userId);


	/**
	 * 更新出库单状态为已取消，且wms同步状态必须为已回撤
	 * @param id
	 * @return
	 */
	int updateToCanceledById(@Param("id") Long id);

	/**
	 * 查询待推送的大仓采购
	 * @param page
	 * @param maxResult
	 * @param recordType
	 * @return
	 */
	List<WarehouseRecordDo> selectWaitNotifyToPurchase(@Param("page") Integer page, @Param("maxResult") Integer maxResult,@Param("recordTypeList")List<Integer>  recordTypeList);

	/**
	 * 查询已收货完成未通知采购中心质检完成的
	 * @param page
	 * @param maxResult
	 * @param recordTypeList
	 * @return
	 */
	List<WarehouseRecordDo> selectWaitQualityToPurchase(@Param("page") Integer page, @Param("maxResult") Integer maxResult,@Param("recordTypeList")List<Integer>  recordTypeList);


    List<String> getWaitWhAllocationNotify();

	int updateSyncOrderStatus(@Param("recordCode")String recordCode);


	int initSyncOrderStatus(@Param("recordCode")String recordCode);

	/**
	 * 查询待推送cmp的门店退货单
	 * @return
	 */
	List<String> getWaitReturnNotifyToCmp();

	/**
	 * 查询待推送店务系统预约退货确认单接
	 * @return
	 */
	List<WarehouseRecordDo> getWaitReturnReceiveToCmp();

	/**
	 * 查询待推送cmp7的门店退货单
	 * @return
	 */
	List<String> getWaitReturnNotifyToCmp7();

	/**
	 *批量查询根据后置单单号
	 * @param recordCodeList
	 * @return
	 */
	List<WarehouseRecordDo> queryWarehouseRecordByCodeList(@Param("recordCodeList")List<String> recordCodeList);

    int updateCmpStatusNeedPush(@Param("id")Long id);

	int updateActualQtyById(WarehouseRecordDetail warehouseRecordDetail);

	int updateActualQtyByDetailId(WarehouseRecordDetail warehouseRecordDetail);

	int batchUpdateDetailActualQtyByRecordCode(String recordCode);

    int updateActualQtyByPlanQty(String recordCode);
    
    /**
	 * 查询根据单据类型和小于指定的时间,主要用在冷热数据迁移
	 * 
	 * @param recordCodes
	 * @return
	 */
	List<WarehouseRecordDo> selectWarehouseRecordByTypeEndTime(@Param("recordType") Integer recordType, @Param("endTime")Date endTime, @Param("pageSize")Integer pageSize);
	
	/**
	 * 查询根据大于指定的Id和创建时间的单据列表,主要用在单据在其他pool是否存在
	 * @param id
	 * @param endTime
	 * @param recordTypes
	 * @param pageSize
	 * @return
	 */
	List<WarehouseRecordDo> queryWarehouseRecordByGtId(@Param("id") Long id, @Param("endTime")Date endTime, @Param("recordTypes")List<Integer> recordTypes, @Param("pageSize")Integer pageSize);

    int deleteWarehouseRecordById(@Param("id")Long id);

	int deleteWarehouseRecordByIdAndRecordStatus(@Param("id")Long id,@Param("recordStatus")Integer recordStatus);

	int updateTransferFailure(@Param("recordCode")String recordCode);

	int deleteWarehouseRecordDetailByIds(@Param("deleteList")List<Long> deleteList);

	/**
	 * 查询列表数据
	 *
	 * @param condition
	 * @return
	 */
	List<WarehouseRecordDo> queryOdyWarehouseRecordList(SaleWarehouseRecordCondition condition);

	/**
	 * 查询未取消的列表数据
	 *
	 * @param sapCode
	 * @return
	 */
	List<WarehouseRecordDo> getUnCanceledBySapCode(@Param("sapCode") String sapCode);

	int updateToCanceledFromCompleteIn(Long id);

	int countUnSyncReceipt(String recordCode);

    int countReceiptByRecordCode(String recordCode);

    int cancelAndUpdateRecordCode(@Param("recordCode")String recordCode,@Param("newRecordCode")String newRecordCode);

	int cancelDetailAndUpdateRecordCode(@Param("recordCode")String recordCode,@Param("newRecordCode")String newRecordCode);

    int countUnCompleteSkuDetail(String recordCode);

    int updateVersionNo(WarehouseRecordE warehouseRecordE);

    int countShopRetailBySapOrderCode(@Param("sapOrderCode")String sapOrderCode);
    
    /**
     * 物理删除后置单，根据单据列表
     * @param deleteList
     * @return
     */
    int deleteWarehouseRecordByRecordCodes(@Param("deleteList")List<String> deleteList);
    
    /**
     * 物理删除后置单明细，根据单据列表
     * @param deleteList
     * @return
     */
    int deleteWarehouseRecordDetailByRecordCodes(@Param("deleteList")List<String> deleteList);
    
    /**
     * 逻辑删除后置单，根据单据列表
     * @param deleteList
     * @return
     */
    int updateDeleteWarehouseRecordByRecordCodes(@Param("deleteList")List<String> deleteList);
    
    /**
     * 逻辑删除后置单明细，根据单据列表
     * @param deleteList
     * @return
     */
    int updateDeleteWarehouseRecordDetailByRecordCodes(@Param("deleteList")List<String> deleteList);

    int updatePlanQtyByDetailId(WarehouseRecordDetail warehouseRecordDetail);

	int batchUpdatePlanQtyByDetailId(@Param("detailList")List<WarehouseRecordDetail> doList);

	int selectUnOutDetail(String recordCode);

    List<SkuStock> queryOnRoadStockByRealWarehouseId(@Param("realWarehouseId")Long realWarehouseId,@Param("skuCodeList")List<String> skuCodeList);

    int updateVirtualWarehouseIdById(@Param("id") Long id, @Param("virtualWarehouseId") Long virtualWarehouseId);

	int updateConfigurationToCancel(@Param("id") Long id , @Param("beforeStatus") Integer beforeStatus, @Param("afterStatus") Integer afterStatus);

    WarehouseRecordDo queryInWarehouseBySapCodeAndRecordTypeList(@Param("sapOrderCode") String sapOrderCode, @Param("recordTypeList") List<Integer> recordTypeList);

	int updateSyncOrderStatusSuccess(@Param("recordCode")String recordCode);

    WarehouseRecordDo queryEntryRecordBySapOrderCode(@Param("sapOrderCode") String sapOrderCode);

    int updateSpecialTag(@Param("recordCode") String recordCode, @Param("selfTakeOut") Integer selfTakeOut);

	int updateRecordWmsSyncStatusByRecordCode(String recordCode);

	String queryReceiveRecordCodeByPackageCode(String recordCode);
}
