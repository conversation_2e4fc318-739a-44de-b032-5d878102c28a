package com.rome.stock.core.infrastructure.dataobject.frontrecord;

import com.rome.stock.core.infrastructure.dataobject.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class FrOutsourcingDO extends BaseDo {
	/**
	 * 唯一主键
	 */
	private Long id;
	/**
	 * 单据编号
	 */
	private String recordCode;

	/**
	 * 商家id
	 */
	private Long merchantId;

	/**
	 * 单据类型
	 */
	private Integer recordType;
	/**
	 * 单据类型1,"委外成品入库单" 2,"委外原料消耗出库单" 3, "委外成品出库
	 */
	private Integer recordStatus;

	/**
	 * 实体仓库id
	 */
	private Long realWarehouseId;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 工产代码
	 */
	private String factoryCode;
	/**
	 * 工产代码
	 */
	private String factoryName;
	/**
	 * 供应商编码代码
	 */
	private String supplierCode;
	/**
	 * 供应商名称
	 */
	private String supplierName;
	/**
	 * 供应商联系人
	 */
	private String supplierContact;

	/**
	 * 外部系统单据编号
	 */
	private String outRecordCode;


	/**
	 * 采购预约单号
	 */
	private String appointRecordCode;

	/**
	 * PO采购单号
	 */
	private String purchaseOrderNo;

	/**
	 * 外部系统数据创建时间
	 */
	private Date outCreateTime;
}
