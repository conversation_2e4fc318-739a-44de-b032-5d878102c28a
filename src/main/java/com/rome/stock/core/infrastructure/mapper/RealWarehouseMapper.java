package com.rome.stock.core.infrastructure.mapper;

import com.rome.stock.core.api.dto.*;
import com.rome.stock.core.api.dto.groupbuy.QueryRealWarehouse;
import com.rome.stock.core.api.dto.warehouserecord.OutWarehouseRecordDTO;
import com.rome.stock.core.domain.entity.RealWarehouseE;
import com.rome.stock.core.infrastructure.dataobject.RealWarehouseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface RealWarehouseMapper {

	RealWarehouseDO queryById(@Param("id") Long id);
	RealWarehouseDO queryByCode(@Param("code") String code);

	List<RealWarehouseDO> queryByIds(@Param("ids") List<Long> ids);

	/**
	 * 根据门店编号查询仓库id
	 * @param shopCode
	 * @return
	 */
	Long queryWarehouseIdByShopCode(@Param("shopCode")String shopCode);

	/**
	 * 根据外部仓库编码和工厂编号查询实仓信息
	 * @param outCode 外部仓库编码
	 * @param factoryCode 工厂编号
	 * @return
	 */
	RealWarehouseDO queryByOutCodeAndFactoryCode(@Param("outCode") String outCode, @Param("factoryCode") String factoryCode);

	/**
	 * 根据外部仓库编码和工厂编号查询实仓信息
	 * @param outCode 外部仓库编码
	 * @param factoryCode 工厂编号
	 * @return
	 */
	List<RealWarehouseDO> queryBatchByOutCodeAndFactoryCode(@Param("list") List<QueryRealWarehouse> list);


	/**
	 * 根据门店编码批量查询
	 * @param codes
	 * @return
	 */
	List<RealWarehouseDO> queryByShopCodeList(@Param("list") List<String> codes);

	/**
	 * 根据门店编码批量查询可用仓库
	 * @param codes
	 * @return
	 */
	List<RealWarehouseDO> queryAvailableRwListByShopCodeList(@Param("list") List<String> codes);

	List<RealWarehouseDO> queryByCondition(RealWarehouseParamDTO paramDTO);
	List<RealWarehouseDO> queryAll();

	/**
	 * 查询全部,包含删除的,专用的
	 * @return
	 */
	List<RealWarehouseDO> queryAllByCache();
	boolean save(RealWarehouseDO realWarehouseDo);
	boolean updateByWhere(RealWarehouseDO realWarehouseDo);
    boolean updateRealWarehouseByWhereMerchant(RealWarehouseDO realWarehouseDo);

	boolean updateStatusEnable(@Param("realWarehouseId") Long realWarehouseId, @Param("userId") Long userId);
	boolean updateStatusDisable(@Param("realWarehouseId") Long realWarehouseId, @Param("userId") Long userId);
	boolean updateIsAvailable(RealWarehouseDO realWarehouseDo);
	boolean updateIsDeleted(RealWarehouseDO realWarehouseDo);
	List<RealWarehouseDO> querysByCodes(@Param("wareHouseCodes") List<String> wareHouseCodes);

	/**
	 * 根据id集合查询仓库
     * @param warehouseIdList
	 * @return
	 */
	List<RealWarehouseDO> queryWarehouseByIds(@Param("idList") List<Long> warehouseIdList);

	String getWarehouseNameById(Long warehouseId);

	RealWarehouseDO getByRwCodeAndFactoryCode(@Param("realWarehouseCode") String realWarehouseCode, @Param("factoryCode") String factoryCode);

	/**
	 * 根据工厂code查询仓库信息
	 * @param factoryCode
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehouseByFactoryCode(@Param("factoryCode") String factoryCode);

	RealWarehouseDO querySingleRealWarehouseByFactoryCodeAndRwType(@Param("factoryCode") String factoryCode, @Param("rwType") Integer rwType);

	RealWarehouseDO queryRealWarehouseByInCode(@Param("code") String code);

	/**
	 * 根据仓库类型
	 * @param types
	 * @return
	 */
	List<RealWarehouseDO> getRealWarehouseFactory(@Param("types") List<Integer> types);

	/**
	 * 根据工厂code和仓库类型查询仓库信息
	 * @param factoryCode
	 * @param types
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehouseByFactoryCodeAndRWType(@Param("factoryCode") String factoryCode,@Param("supplierCode") String supplierCode,@Param("types") List<Integer> types);

	/**
	 * 根据仓库编号查询实仓id
	 * @param realWarehouseCode
	 * @return
	 */
	List<Long> queryRealWarehouseIdsByCode(String realWarehouseCode);

	/**
	 * 根据内部编码查询实仓信息
	 * @param realWarehouseCode
	 * @return
	 */
	RealWarehouseDO queryRealWarehouseByRealWarehouseCode(@Param("realWarehouseCode")String realWarehouseCode);

	/**
	 * 批量查询 根据内部编码查实仓信息
	 * @param realWarehouseCodeList
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehouseByRealWarehouseCodeList(@Param("realWarehouseCodeList")List<String> realWarehouseCodeList);

	/**
	 * 根据仓库类型查仓库
	 * @param type
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehouseByRWType(@Param("type") Integer type);

	/**
	 * 查询非门店实仓
	 * @param factoryCode
	 * @return
	 */
	List<RealWarehouseDO> queryWmsRealWarehouseByFactoryCode(String factoryCode);

	/**
	 * 查询所有非门店仓
	 * @param type
	 * @param factoryCode
	 * @return
	 */
    List<RealWarehouseDO> queryNotShopWarehouse(@Param("type") Integer type,@Param("factoryCode") String factoryCode);

	/**
	 * 获取所有非门店仓下的工厂
	 * @return
	 * @param type
	 */
    List<String> queryNotShopFactory(@Param("type") Integer type);

	/**
	 * 获取所有非门店仓下的工厂
	 * @return
	 * @param type
	 */
	List<String> queryShopFactory(@Param("type") Integer type);

	/**
	 *通过仓库编码查询仓库信息
	 * @param warehouseCode
	 * @return
	 */
    RealWarehouseDO getRealWarehouseByRmCode(@Param("warehouseCode") String warehouseCode);

	/**
	 * 根据批量工厂编码查询实仓信息
	 * @param factoryCodes
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehousesByFactoryCodes(@Param("factoryCodes") List<String> factoryCodes);


	/**
	 * 根据商家编号查询对实仓信息
	 * @param merchantId
	 * @return
	 */
	RealWarehouseDO queryRealWarehouseByMerchantId(@Param("merchantId") Long merchantId);

	/**
	 * 查询虚拟仓库实仓
	 * @return
	 */
	RealWarehouseDO queryVirtualSkuRealWarehouse();

	/**
	 * 根据仓库编号实仓ID
	 * @param realWarehouseCode
	 * @return
	 */
	Long queryRWIdByRealWarehouseCode(@Param("realWarehouseCode")String realWarehouseCode);

	/**
	 * 根据门店编码查询实仓ID
	 * @param shopCode
	 * @return
	 */
	Long queryRWIdByShopCode(@Param("shopCode")String shopCode);

	/**
	 * 根据实仓ID查询实仓
	 * @param realWarehouseId
	 * @return
	 */
	RealWarehouseDO queryRealWarehouseById(Long realWarehouseId);

	/**
	 * 查询实仓信息列表（排除门店）
	 * @return
	 */
	List<RealWarehouseDO> queryRWList(@Param("nameOrCode") String nameOrCode);

	/**
	 *根据鲲鹏仓配置，查询实仓id
	 * @param realWarehouseCodeList
	 * @return
	 */
	List<Long> queryRealWarehouseIdsByKpRands(@Param("ranKs")List<Integer> ranKs);

	/**
	 * 根据行政区域查询实仓信息
	 * @param queryRealWarehouse
	 * @return
	 */
	List<RealWarehouseDO> queryRWByCondition(QueryAreaRWarehouse queryRealWarehouse);

	/**
	 * 判断仓库是否存在
	 * @param factoryCode
	 * @return
	 */
    int countRealWarehouseByFactoryCode(@Param("factoryCode")String factoryCode);

    List<String> queryFactoryCodeByLike(@Param("factoryCode")String factoryCode);

	/**
	 * 根据仓库类型查询分组工厂
	 * @param realWarehouseType
	 * @return
	 */
	List<RealWarehouseDO> queryFactoryByRwType(@Param("realWarehouseType")Integer realWarehouseType);

	QueryStockDTO queryRealWarehouseStockByRealWarehouseCodeAndSkuCode(QuerySkuStockDTO querySkuStockDTO);

	List<RealWarehouse> queryRealWarehouseCodeCodeByLike(@Param("realWarehouseCode")String realWarehouseCode, @Param("factoryCode")String factoryCode );

	/**
	 * 根据仓库类型集合查询工厂信息
	 * @param realWarehouseTypeList
	 * @return
	 */
    List<RealWarehouseDO> queryFactoryByRwTypeList(List<Integer> realWarehouseTypeList);

    List<RealWarehouse> queryCompanyCodesByFactoryCodes(@Param("factoryCodes")List<String> factoryCodes);

	int updateRealWarehouseByInitField(RealWarehouseE realWarehouseE);

    int updateCostCenterCodeAndCostCenterName(@Param("factoryCode")String factoryCode, @Param("costCenterCode")String costCenterCode, @Param("costCenterName")String costCenterName);

    List<RealWarehouseDO> queryByCompanyCodeAndRealWarehouseType(@Param("companyCode") String companyCode, @Param("realWarehouseType") Integer realWarehouseType);

    int updateReturnWarehouse(@Param("id") Long id, @Param("returnWarehouseId") Long returnWarehouseId);

	/**
	 * 根据外部编码与工厂编码批量查询
	 * @param list
	 * @return
	 */
	List<RealWarehouseDO> queryByOutCodeAndFactoryCodeList(@Param("list") List<RealWarehouseDO> list);

	List<RealWarehouseDO> queryWarehouseByLocationCode(@Param("locationCodes")List<String> locationCodes);




	/**
	 * 根据id集合分页查询仓库
	 * @param queryRealWarehoureKpDTO
	 * @return
	 */
	List<RealWarehouseDO> queryWarehouseByIdPage(QueryRealWarehoureKpDTO queryRealWarehoureKpDTO);

	/**
	 * 根据仓库编码查询仓库信息
	 * @param factoryCodes
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehouseLimitWarehouse(@Param("factoryCodes") List<String> factoryCodes);

    List<RealWarehouseDO> queryWarehouseByRealWarehouseList(@Param("realWarehouseList")List<OutWarehouseRecordDTO> realWarehouseList);

	/**
	 * 根据外部编号和仓库类型查询实仓信息
	 * @param realWarehouseOutCode
	 * @param realWarehouseType
	 * @return
	 */
	List<RealWarehouseDO> queryRealWarehouseByOutCodeAndType(@Param("realWarehouseOutCode") String realWarehouseOutCode,
															 @Param("realWarehouseType") Integer realWarehouseType);

	RealWarehouseDO queryByShopCode(String shopCode);

    List<String> querySpecialWarehouseList();

}
