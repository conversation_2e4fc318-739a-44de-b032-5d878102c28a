<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.rome.stock.core</groupId>
  <artifactId>stock-core-service</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>jar</packaging>
  <name>stock-core-service</name>
  
	<parent>
        <groupId>com.rome.public</groupId>
        <artifactId>public-pom</artifactId>
        <version>3.1.0-SNAPSHOT</version>
    </parent>
    <properties>
        <maven.test.skip>true</maven.test.skip>
    </properties>

    <dependencies>
        <!--中台stock-common包-->
        <dependency>
            <groupId>com.rome.stock.common</groupId>
            <artifactId>stock-common</artifactId>
            <version>5.6-SNAPSHOT</version>
        </dependency>

        <!-- 升级 start 2022-05-16-->

        <!-- 中台架构-开启基本的web服务 -->
        <dependency>
            <groupId>com.rome.public</groupId>
            <artifactId>arch-web-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>arch-common</artifactId>
                    <groupId>com.rome.public</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.rome.public</groupId>
            <artifactId>arch-config-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.rome.public</groupId>
            <artifactId>arch-discovery-starter</artifactId>
        </dependency>

        <!--中台架构common包-->
        <dependency>
            <groupId>com.rome.public</groupId>
            <artifactId>arch-common</artifactId>
        </dependency>

        <!-- 中台架构-日志包 -->
        <dependency>
            <groupId>com.rome.public</groupId>
            <artifactId>arch-log-starter</artifactId>
        </dependency>

        <!-- 中台架构-开启mysql数据库-->
        <dependency>
            <groupId>com.rome.public</groupId>
            <artifactId>arch-jdbc-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.3.0</version>
        </dependency>

        <!-- 升级 end -->

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <!--excel工具包-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-net/commons-net -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.6</version>
        </dependency>
       <!--

       <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
        </dependency>


        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.11</version>
        </dependency>-->

        <!-- swagger -->
        <!--redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- swagger-ui -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.6</version>
        </dependency>
        <!--rocketmq-->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <!--hystrix-->
        <!-- <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix-dashboard</artifactId>
        </dependency> -->
        
        <dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.2</version>
		</dependency>

        <!--工具类型-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.4.7</version>
        </dependency>

        <!--开启热部署模式-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>
        
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <!--elastic search-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        
        <!--core-->
        <dependency>
            <groupId>com.rome.stock.core</groupId>
            <artifactId>stock-core</artifactId>
            <version>2.2.8</version>
        </dependency>
        <!--分布式业务单号生成包-->
        <dependency>
            <groupId>com.rome.stock.leaf</groupId>
            <artifactId>stock-leaf</artifactId>
            <version>1.0.3</version>
        </dependency>
        <!--wms包-->
        <dependency>
            <groupId>com.rome.stock.wms</groupId>
            <artifactId>stock-wms</artifactId>
            <version>2.1.9-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>uk.com.robust-it</groupId>
            <artifactId>cloning</artifactId>
            <version>1.9.12</version>
        </dependency>
        <dependency>
            <groupId>uk.com.robust-it</groupId>
            <artifactId>cloning</artifactId>
            <version>1.9.12</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.8.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
            
            <!-- 定义单元测试标准  修改前三个false -->
            <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    
                    <configuration>
                        <skip>true</skip>
                        <skipTests>true</skipTests>
                        <testFailureIgnore>true</testFailureIgnore>
                    </configuration>
                </plugin>    

        </plugins>
    </build>
    
    
</project>
